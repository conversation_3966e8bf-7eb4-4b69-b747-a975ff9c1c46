#!/usr/bin/env bash

cmd=$1

if [[ ${cmd} = 'start' ]];then
  echo 'start'
  /root/avenir-gstash/gstash -config /root/avenir-gstash/config.json  >> /root/data/logs/gstash/stdout-gstash.log 2>&1 &
fi

if [[ ${cmd} = 'restart' ]];then
  echo 'restart'
  killall -9 gstash
  /root/avenir-gstash/gstash -config /root/avenir-gstash/config.json  >> /root/data/logs/gstash/stdout-gstash.log 2>&1 &
fi

if [[ ${cmd} = 'stop' ]];then
  echo 'stop'
  killall -9 gstash
fi
