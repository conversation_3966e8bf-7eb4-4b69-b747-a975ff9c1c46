#!/usr/bin/env bash

env=$1
cmd=$2
port="22"

if [ ! -n "$env" ]; then
  echo "请输入环境，dev|prod"
  exit 1
elif [ "$env" == "dev" ]; then
  servers=("10.2.34.124" "10.2.37.134")
elif [ "$env" == "prod" ]; then
  servers=("10.2.34.124" "10.2.37.134")
else
  echo "环境输入有误，请重新输入，dev|prod"
  exit 1
fi

if [ "$cmd" != "start" ] && [ "$cmd" != "stop" ] && [ "$cmd" != "restart" ]; then
  echo "参数输入有误，请重新输入，start|stop|restart"
  exit 1
fi

for server in ${servers[@]}; do
  echo "running server ${server}..."
  ssh -p ${port} root@${server} "service gstash ${cmd} "
  sleep 20s
done
