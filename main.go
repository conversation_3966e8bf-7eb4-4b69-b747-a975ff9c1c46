package main

import (
	"context"
	"flag"
	"log"
	"os"

	"github.com/tmsong/hlog"
	futil "github.com/tmsong/utils/file"

	"gitlab.docsl.com/security/common/masker"

	"gstash/cmd"
	"gstash/config"
	"gstash/helper/logger"
)

/**
 * @note
 * entry
 */

func main() {
	//读取运行参数
	flagSet := flag.NewFlagSet(config.MODULE_NAME, flag.ExitOnError)
	configPath := flagSet.String("config", "", "path to config file")
	logFile := flagSet.String("logfile", "", "log output file")
	err := flagSet.Parse(os.Args[1:])
	if err != nil {
		log.Fatalln("failed to load args", err)
	}
	masker.SetGetKeyFunc(func(arg string) []byte {
		return []byte(os.Getenv("GSTASH_CONFIG_CRYPT_KEY"))
	})

	//读取配置文件
	if *configPath == "" {
		*configPath = searchConfigPath()
	}

	conf, err := config.LoadFromFile(*configPath)
	if err != nil {
		log.Fatalf("failed to load config file %s - %s\n", *configPath, err)
	}
	conf.ConfigPath = *configPath

	if *logFile != "" {
		conf.Log.LogFile = *logFile
	}

	//初始化日志组件&ctx
	loggerConfig := &hlog.Config{
		TraceHeader: "Gstash-Header-Rid",
		Level:       conf.Log.LogLevel,
		File: func() *hlog.FileConfig {
			if conf.Log.LocalLog {
				return &hlog.FileConfig{
					FileName:  conf.Log.LogFile,
					Interval:  conf.Log.Interval,
					MaxAge:    conf.Log.MaxAge,
					MaxSize:   conf.Log.MaxSize,
					LocalTime: conf.Log.LocalTime,
				}
			}
			return nil
		}(),
		Kafka: func() *hlog.KafkaConfig {
			if conf.Log.KafkaLog {
				return &conf.Log.Kafka
			}
			return nil
		}(),
	}
	logger.Logger = hlog.NewLoggerWithConfig(loggerConfig, 0)
	defer logger.Logger.Close()
	config.ProcId, err = os.Hostname()
	if err != nil {
		config.ProcId = "unknown-host"
	}
	logger.Logger.SetTraceId(config.ProcId)
	//崩溃恢复打印
	defer func() {
		if err := recover(); err != nil {
			logger.Logger.Errorf("abort, unknown error, reason:%v,\n stack:%s\n", err, config.IdentifyPanic())
			logger.Logger.Errorf("abort, unknown error, reason:%v", err)
		}
	}()
	ctx := context.Background()

	err = conf.LoadWorkerFromDB(logger.Logger)
	if err != nil {
		log.Printf("load worker from db error: %v, exit", err)
		return
	}
	conf.InitWorkers()
	config.SetupConfig(conf)
	err = cmd.Gstash(ctx, conf)
	if err != nil {
		log.Printf("gstash exited with error, reason:%v", err)
	}
}

func searchConfigPath() string {
	for _, path := range []string{"config.json", "config.yaml", "config.yml"} {
		if futil.IsExist(path) {
			return path
		}
	}
	return "config.json"
}
