#!/usr/bin/env bash

env=$1
port="22"

if [ ! -n "$env" ]; then
  echo "请输入环境，dev|prod"
  exit 1
elif [ "$env" == "dev" ]; then
  servers=("10.2.34.124" "10.2.37.134")
elif [ "$env" == "prod" ]; then
  servers=("10.2.34.124" "10.2.37.134")
else
  echo "环境输入有误，请重新输入，dev|prod"
  exit 1
fi

export GOPROXY=https://goproxy.cn
rm -rf gstash
go build  -v -o ./gstash .

for server in ${servers[@]}; do
  echo "deploying server ${server}..."
  rsync -e "ssh -p ${port}" -rvt ./gstash root@${server}:/root/avenir-gstash
  rsync -e "ssh -p ${port}" -rvt ./dist/conf/${env}.json root@${server}:/root/avenir-gstash/config.json
  rsync -e "ssh -p ${port}" -rvt ./run.sh root@${server}:/root/avenir-gstash
done
