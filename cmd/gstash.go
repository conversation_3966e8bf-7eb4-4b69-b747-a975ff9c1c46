package cmd

import (
	"context"
	"net/http"
	_ "net/http/pprof"
	"os"
	"os/signal"
	"runtime"
	"syscall"

	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/sirupsen/logrus"
	"github.com/tmsong/hlog"

	"gstash/config"
	"gstash/helper/logger"
	_ "gstash/modloader"
)

/* @note
 * params
 * procSerialNum int >0 表示是否是一个子进程。
 */

func Gstash(
	ctx context.Context,
	conf config.Config,
) (err error) {
	if conf.Debug {
		logger.Logger.SetLevel(logrus.DebugLevel)
	}

	if runtime.GOMAXPROCS(0) == 1 && runtime.NumCPU() > 1 {
		logger.Logger.Warnf("set GOMAXPROCS = %d to get better performance", runtime.NumCPU())
	}

	if conf.PrometheusAddr != "" {
		go func() {
			// 注册prometheus
			config.RegisterPrometheus()
			http.Handle("/metrics", promhttp.Handler())
			logger.Logger.Errorln(http.ListenAndServe(conf.PrometheusAddr, nil))
		}()
	}

	ctx, cancel := contextWithOSSignal(ctx, logger.Logger, syscall.SIGKILL, syscall.SIGINT, syscall.SIGTERM)
	for i := range conf.Worker {
		if err = conf.Worker[i].Start(ctx); err != nil {
			return err
		}
	}

	if conf.PProfAddr != "" { //only sub process run pprof
		go func() {
			if err := http.ListenAndServe(conf.PProfAddr, nil); err != nil {
				logger.Logger.Errorln(err)
			}
		}()
	}

	logger.Logger.Infoln("gstash started...")

	if conf.HotUpdate {
		go func() {
			if err := scanConfig(ctx, conf, cancel); err != nil {
				logger.Logger.Errorln(err)
			}
		}()
	}
	// Check whether any goroutines failed.
	for i := range conf.Worker {
		if waitErr := conf.Worker[i].Wait(); waitErr != nil {
			err = waitErr
		}
	}
	return err
}

func contextWithOSSignal(parent context.Context, logger *hlog.Logger, sig ...os.Signal) (context.Context, context.CancelFunc) {
	osSignalChan := make(chan os.Signal, 1)
	signal.Notify(osSignalChan, sig...)

	ctx, cancel := context.WithCancel(parent)

	go func(cancel context.CancelFunc) {
		sig := <-osSignalChan
		logger.Infoln("received signal:", sig)
		cancel()
	}(cancel)

	return ctx, cancel
}
