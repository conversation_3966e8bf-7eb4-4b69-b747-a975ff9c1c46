//go:build !windows
// +build !windows

package cmd

import (
	"context"
	"gstash/config"
	"gstash/helper/logger"
	"gstash/helper/redis"
	wcModel "gstash/model/worker_config"
	"net"
	"os"
	"syscall"
	"time"

	"golang.org/x/sync/errgroup"
)

const (
	GSTASH_WORKER_CONFIG_HOST_REDIS_KEY = "gsatsh_worker_config_update_host"
)

func startSubProc(args []string, attr *syscall.ProcAttr) (pid int, err error) {
	pid, err = syscall.ForkExec(args[0], args, attr)
	if err != nil {
		logger.Logger.Errorf("start worker error: %v", err)
		return
	}
	logger.Logger.Infof("worker started: %d", pid)
	return
}

// 等待任意子进程退出后重启该进程
func waitSubProc(ctx context.Context, attr *syscall.ProcAttr) error {
	var ws syscall.WaitStatus
	for {
		// wait for any child process
		pid, err := syscall.Wait4(-1, &ws, 0, nil)
		if err != nil {
			logger.Logger.Errorf("wait4() error: %v", err)
			continue
		}
		select {
		case <-ctx.Done():
			return nil
		default:
			// pass
		}

		logger.Logger.Warnf("worker %d stopped unexpectedly (wstatus: %d)", pid, ws)
		// only restart once after stopped unexpectedly
		pid, err = startSubProc(getSubProcArgs(), attr)
		if err != nil {
			logger.Logger.Errorf("startSubProc error , reason:%v", err)
			return err
		}

		// 将锁时间缩短，不立即释放，保证机器分时重启
		redisCli, err := redis.DefaultClient(logger.Logger)
		if err != nil {
			logger.Logger.Errorf("waitSubProcs DefaultClient error, reason:%v", err)
			return err
		}

		suc, err := redisCli.Expire(GSTASH_WORKER_CONFIG_HOST_REDIS_KEY, 10*time.Second)
		if !suc || err != nil {
			logger.Logger.Errorf("gstash exited with error, reason:%v", err)
			return err
		}

	}
}

// 定时扫描 DB 中 worker 配置，发现配置更新后校验配置，配置正常则结束子进程
func scanConfig(ctx context.Context, conf config.Config, cancel context.CancelFunc) error {
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()
HERE:
	for {
		select {
		case <-ctx.Done():
			return nil
		case <-ticker.C:
			select {
			case <-ctx.Done():
				return nil
			default:
				// 查配置
				model, err := wcModel.NewWorkConfigModel(logger.Logger)
				if err != nil {
					logger.Logger.Errorf("scanConfig db query, reason:%v", err)
					continue
				}

				updatedConfig, err := model.QueryWorkerConfigByUpdateTime(conf.LastUpdateTime)
				if err != nil {
					logger.Logger.Errorf("scanConfig db query QueryWorkerConfigByUpdateTime error, reason:%v", err)
					continue
				}
				if len(updatedConfig) <= 0 {
					continue
				}
				logger.Logger.Infoln("Gstash worker config lastUpdateTime", conf.LastUpdateTime)
				newConf, err := config.LoadFromFile(conf.ConfigPath)
				if err != nil {
					logger.Logger.Errorf("scanConfig db query QueryWorkerConfigByUpdateTime error, reason:%v", err)
					continue
				}
				err = newConf.LoadWorkerFromDB(logger.Logger)
				if err != nil {
					logger.Logger.Errorf("scanConfig load worker from db error: %v, exit", err)
					continue
				}
				newConf.InitWorkers()

				newCtx, newCancel := context.WithCancel(context.Background())
				//校验配置
				for _, v := range newConf.Worker {
					_, err = config.GetInputs(v, newCtx, v.InputRaw)
					if err != nil {
						logger.Logger.Errorf("scanConfig GetInputs error, reason:%v", err)
						newCancel()
						goto HERE
					}
					_, err = config.GetFilters(v, newCtx, v.FilterRaw)
					if err != nil {
						logger.Logger.Errorf("scanConfig GetFilters error, reason:%v", err)
						newCancel()
						goto HERE
					}
					_, err = config.GetOutputs(v, newCtx, v.OutputRaw)
					if err != nil {
						logger.Logger.Errorf("scanConfig GetOutputs error, reason:%v", err)
						newCancel()
						goto HERE
					}
				}
				newCancel()

				redisCli, err := redis.DefaultClient(logger.Logger)
				if err != nil {
					logger.Logger.Errorf("scanConfig DefaultClient error , reason:%v", err)
					continue
				}

				host, err := getHost()
				if err != nil {
					logger.Logger.Errorf("scanConfig getHost error, reason:%v", err)
					continue
				}

				logger.Logger.Infoln("Scan an update and start to lock ")
				// 抢锁
				for {
					suc, err := redisCli.SetNX(GSTASH_WORKER_CONFIG_HOST_REDIS_KEY, host, 3*time.Minute)
					if !suc || err != nil {
						time.Sleep(5 * time.Second)
						continue
					}
					cancel()
					go func() {
						time.Sleep(10 * time.Second)
						os.Exit(1)
					}()
					break
				}
			}
		}
	}
}

// 开启子进程
func startProc(ctx context.Context) error {
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	attr := &syscall.ProcAttr{
		Env:   os.Environ(),
		Files: []uintptr{os.Stdin.Fd(), os.Stdout.Fd(), os.Stderr.Fd()},
	}

	_, err := startSubProc(getSubProcArgs(), attr)
	if err != nil {
		return err
	}

	eg, ctx := errgroup.WithContext(ctx)

	eg.Go(func() error {
		return waitSubProc(ctx, attr)
	})

	return waitSignals(ctx)
}

func getSubProcArgs() []string {
	ret := make([]string, len(os.Args))
	copy(ret, os.Args)
	return append(ret, "-subProc")
}

func getHost() (string, error) {
	gInnerIP := ""
	addrs, err := net.InterfaceAddrs()
	if err != nil {
		return gInnerIP, err
	}

	for _, address := range addrs {
		if ipnet, ok := address.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
			if ipnet.IP.To4() != nil {
				gInnerIP = ipnet.IP.String()
				break
			}
		}
	}
	return gInnerIP, nil
}
