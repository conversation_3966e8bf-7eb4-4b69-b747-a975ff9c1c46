// +build windows

package cmd

import (
	"context"
	"golang.org/x/sync/errgroup"
	"gstash/helper/logger"
	"os"
	"syscall"
	"unsafe"
)

func startSubProc(args []string, attr *syscall.ProcAttr) (pid int, handle uintptr, err error) {
	pid, handle, err = syscall.StartProcess(os.Args[0], args, attr)
	if err != nil {
		logger.Logger.Errorf("start worker failed: %v", err)
		return
	}
	logger.Logger.Infof("worker started: %d", pid)
	return
}

func waitSubProcs(ctx context.Context, pids []int, handles []uintptr, attr *syscall.ProcAttr) error {
	// syscall only has `WaitForSingleObject`, but we have to wait multiple processes,
	// so that we find proc `WaitForMultipleObjects` from kernel32.dll.
	// doc: https://docs.microsoft.com/en-us/windows/desktop/api/synchapi/nf-synchapi-waitformultipleobjects
	dll := syscall.MustLoadDLL("kernel32.dll")
	wfmo := dll.MustFindProc("WaitForMultipleObjects")
	for {
		r1, _, err := wfmo.Call(uintptr(len(handles)), uintptr(unsafe.Pointer(&handles[0])), 0, syscall.INFINITE)
		ret := int(r1)
		if ret == syscall.WAIT_FAILED && err != nil {
			logger.Logger.Errorf("WaitForMultipleObjects() error: %v", err)
			continue
		}
		select {
		case <-ctx.Done():
			return nil
		default:
			// pass
		}
		if ret >= syscall.WAIT_OBJECT_0 && ret < syscall.WAIT_OBJECT_0+len(handles) {
			i := ret - syscall.WAIT_OBJECT_0
			syscall.CloseHandle(syscall.Handle(handles[i]))
			logger.Logger.Warnf("worker %d stopped unexpectedly", pids[i])
			// only restart once after stopped unexpectedly
			pid, handle, _ := startSubProc(getSubProcArgs(i+1), attr)
			pids[i] = pid
			handles[i] = handle
		}
	}
}

func startSubProcs(ctx context.Context, workerNum int) error {
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	attr := &syscall.ProcAttr{
		Env:   os.Environ(),
		Files: []uintptr{os.Stdin.Fd(), os.Stdout.Fd(), os.Stderr.Fd()},
	}

	pids := make([]int, workerNum)
	handles := make([]uintptr, workerNum)
	for i := 0; i < workerNum; i++ {
		pid, handle, err := startSubProc(getSubProcArgs(i+1), attr)
		if err != nil {
			return err
		}
		pids[i] = pid
		handles[i] = handle
	}

	eg, ctx := errgroup.WithContext(ctx)
	eg.Go(func() error {
		return waitSubProcs(ctx, pids, handles, attr)
	})
	return waitSignals(ctx)
}

func getSubProcArgs() []string {
	ret := make([]string, len(os.Args))
	copy(ret, os.Args)
	return append(ret, "-subProc")
}
