package inputsls

import (
	"context"
	"errors"
	"fmt"
	"gitlab.docsl.com/security/common/masker"
	"golang.org/x/sync/errgroup"
	"os"
	"regexp"
	"sync"
	"time"

	sls "github.com/aliyun/aliyun-log-go-sdk"

	"gstash/config"
	"gstash/config/logevent"
	"gstash/helper/logger"
	"gstash/helper/redis"
)

// ModuleName is the name used in config file
const ModuleName = "sls"

// ErrorTag tag added to event when process module failed
const ErrorTag = "gstash_input_sls_error"

const (
	SlsCursorOffsetKeyFormat = "gstash_sls_cursor_%s_%s:%d"     // 第一个leader_key，第二个logstore，第三个shardID
	SlsLeaderKeyFormat       = "gstash_input_sls_leader_key_%s" // leader_key
)

// InputConfig holds the configuration json fields and internal objects
type InputConfig struct {
	config.InputConfig
	Endpoint         string `json:"endpoint"`
	Region           string `json:"region"`
	AccessKeyID      string `json:"accessKeyID" mask:"crypt"`
	AccessKeySecret  string `json:"accessKeySecret" mask:"crypt"`
	Project          string `json:"project"`
	Logstore         string `json:"logstore"`
	LogstoreRegex    string `json:"logstoreRegex"`
	LogGroupMaxCount int    `json:"logGroupMaxCount"`
	Interval         int    `json:"interval,omitempty"`
	InitialTimestamp int64  `json:"initialTimestamp"`
	Concurrency      int    `json:"concurrency"`

	LeaderKey      string `json:"leaderKey"`      //标记拉取的是什么日志，用于标记选主
	LeaderExpire   int    `json:"leaderExpire"`   //leader的key的过期时间
	LeaderInterval int    `json:"leaderInterval"` //leader的key多长时间try/续期一次

	hostname           string
	logStoreRegex      *regexp.Regexp
	logStoresAndShards map[string][]int
	mu                 *sync.RWMutex
	client             sls.ClientInterface
	redisClient        *redis.Client
	leader             *redis.Leader
}

// DefaultInputConfig returns an InputConfig struct with default values
func DefaultInputConfig() InputConfig {
	return InputConfig{
		InputConfig: config.InputConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
		Interval:         5,
		LeaderExpire:     30,
		LeaderInterval:   10,
		Concurrency:      10,
		LogGroupMaxCount: 100,
	}
}

// InitHandler initialize the input plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeInputConfig, error) {
	conf := DefaultInputConfig()
	err := config.ReflectConfig(raw, &conf)
	if err != nil {
		return nil, err
	}
	confIface, err := masker.PropCrypt(&conf)
	if err != nil {
		return nil, err
	}

	conf = *confIface.(*InputConfig)
	if conf.hostname, err = os.Hostname(); err != nil {
		return nil, err
	}

	conf.redisClient, err = redis.DefaultClient(logger.Logger)
	if err != nil {
		return nil, err
	}

	conf.Codec, err = config.GetCodecOrDefault(ctx, *raw)
	if err != nil {
		return nil, err
	}
	conf.client = sls.CreateNormalInterfaceV2(conf.Endpoint,
		sls.NewStaticCredentialsProvider(conf.AccessKeyID, conf.AccessKeySecret, ""))
	conf.client.SetAuthVersion(sls.AuthV4)
	conf.client.SetRegion(conf.Region)
	conf.mu = new(sync.RWMutex)
	if conf.LogstoreRegex != "" {
		conf.logStoreRegex, err = regexp.Compile(conf.LogstoreRegex)
	}
	if err != nil {
		return nil, err
	}
	conf.leader, err = conf.redisClient.ElectLeader(ctx, fmt.Sprintf(SlsLeaderKeyFormat, conf.LeaderKey),
		time.Duration(conf.LeaderExpire)*time.Second,
		time.Duration(conf.LeaderInterval)*time.Second,
		conf.hostname)
	if err != nil {
		logger.Logger.Errorf("sls input %s ElectLeader error, reason:%v", conf.LeaderKey, err)
		return nil, err
	}
	return &conf, err
}

// Start wraps the actual function starting the plugin
func (t *InputConfig) Start(ctx context.Context, msgChan chan<- logevent.LogEvent) (err error) {
	eg, ctx := errgroup.WithContext(ctx)
	eg.Go(func() error {
		return t.syncLogStoresAndShards(ctx)
	})
	eg.Go(func() error {
		return t.pullSlsLogs(ctx, msgChan)
	})
	return eg.Wait()
}

func (t *InputConfig) syncLogStoresAndShards(ctx context.Context) (err error) {
	// 上来先拉一个
	if err := t.syncLogStoresAndShardsOnce(ctx); err != nil {
		logger.Logger.Errorf("sls input %s sync logstores and shards failed: %v", t.LeaderKey, err)
		return err
	}
	ticker := time.NewTicker(time.Hour) // 一小时更新一次
	defer ticker.Stop()
	for {
		select {
		case <-ctx.Done():
			return nil
		case <-ticker.C:
			if !t.leader.IsLeader() {
				logger.Logger.Infof("sls input %s not leader, leader is [%s], sleep...", t.LeaderKey, t.leader.LeaderIP())
				continue
			}
			if err := t.syncLogStoresAndShardsOnce(ctx); err != nil {
				logger.Logger.Errorf("sls input %s sync logstores and shards failed: %v", t.LeaderKey, err)
			}
		}
	}
}

func (t *InputConfig) syncLogStoresAndShardsOnce(ctx context.Context) (err error) {
	logstores, err := t.client.ListLogStore(t.Project)
	if err != nil {
		return err
	}
	if len(logstores) == 0 {
		return nil
	}
	filteredLogstores := make([]string, 0)
	for _, ls := range logstores {
		if t.logStoreRegex != nil && t.logStoreRegex.MatchString(ls) {
			filteredLogstores = append(filteredLogstores, ls)
		} else if ls == t.Logstore {
			filteredLogstores = append(filteredLogstores, ls)
		}
	}
	m := make(map[string][]int)
	for _, ls := range filteredLogstores {
		shards, err := t.client.ListShards(t.Project, ls)
		if err != nil {
			logger.Logger.Errorf("sls input %s list shards failed: %v", t.LeaderKey, err)
			return err
		}
		for _, shard := range shards {
			if shard.Status != "readwrite" {
				continue
			}
			m[ls] = append(m[ls], shard.ShardID)
		}
	}
	t.mu.Lock()
	defer t.mu.Unlock()
	t.logStoresAndShards = m
	return nil
}

func (t *InputConfig) pullSlsLogs(ctx context.Context, msgChan chan<- logevent.LogEvent) (err error) {
	ticker := time.NewTicker(time.Duration(t.Interval) * time.Second)
	defer ticker.Stop()
	for {
		select {
		case <-ctx.Done():
			return nil
		case <-ticker.C:
			if !t.leader.IsLeader() {
				continue
			}
			t.mu.RLock()
			m := t.logStoresAndShards // 这里直接把地址对应的map拿出来直接读，由于写的时候也只是变更logStoresAndShards指向的地址，所以实际是指针的修改，不会有并发安全问题。
			t.mu.RUnlock()
			if len(m) == 0 {
				continue
			}
			eg, ctx := errgroup.WithContext(ctx)
			eg.SetLimit(t.Concurrency)
			for logStore, shards := range m {
				for _, shard := range shards {
					func(logStore string, shardID int) {
						eg.Go(func() error {
							err := t.PullLogsByLogstoreAndShardIDOnce(ctx, logStore, shardID, msgChan)
							if err != nil {
								logger.Logger.Errorf("sls input %s_%s:%d pull logs once failed: %v", t.LeaderKey, logStore, shardID, err)
							}
							return err
						})
					}(logStore, shard)
				}
			}
			_ = eg.Wait() // 上面有日志打出来了，这里忽略，只要等这波都执行完了即可
		}
	}
}

func (t *InputConfig) PullLogsByLogstoreAndShardIDOnce(ctx context.Context, logStore string, shardID int, msgChan chan<- logevent.LogEvent) (err error) {
	cursor, err := t.GetCursorByLogstoreAndShardID(ctx, logStore, shardID) // 每次都从redis里取，保证准确性
	if err != nil {
		logger.Logger.Errorf("sls input %s_%s:%d get cursor by shardID error, reason:%v", t.LeaderKey, logStore, shardID, err)
		return err
	}

	// 拉取日志
	logGroupList, nextCursor, err := t.client.PullLogs(t.Project, logStore, shardID, cursor, "", t.LogGroupMaxCount)
	if err != nil {
		logger.Logger.Errorf("sls input %s_%s:%d pull logs error, reason:%v", t.LeaderKey, logStore, shardID, err)
		return err
	}

	if logGroupList == nil || len(logGroupList.LogGroups) == 0 {
		return nil
	}
	// 处理拉取到的日志
	for _, logGroup := range logGroupList.LogGroups {
		for _, log := range logGroup.Logs {
			// 创建LogEvent
			event := logevent.LogEvent{
				Timestamp: time.Unix(int64(log.GetTime()), 0),
				Extra: map[string]interface{}{
					"sls": map[string]interface{}{
						"project":   t.Project,
						"log_store": logStore,
						"shard_id":  shardID,
					},
				},
			}

			// 添加日志内容
			for _, content := range log.Contents {
				if content.GetKey() == "message" {
					event.Message = content.GetValue()
				} else {
					event.Extra[content.GetKey()] = content.GetValue()
				}
			}

			// 添加LogGroup的元数据
			if logGroup.Topic != nil {
				event.Extra["topic"] = logGroup.GetTopic()
			}
			if logGroup.Source != nil {
				event.Extra["source"] = logGroup.GetSource()
			}

			// 添加LogGroup的标签
			for _, tag := range logGroup.LogTags {
				if event.Extra["log_tags"] == nil {
					event.Extra["log_tags"] = make(map[string]string)
				}
				event.Extra["log_tags"].(map[string]string)[tag.GetKey()] = tag.GetValue()
			}

			// 发送到消息通道
			select {
			case msgChan <- event:
			case <-ctx.Done():
				return nil
			}
		}
	}

	// 保存nextCursor到Redis
	if nextCursor != "" && nextCursor != cursor {
		err = t.SetCursorByLogstoreShardID(ctx, logStore, shardID, nextCursor)
		if err != nil {
			logger.Logger.Errorf("sls input %s:%d set cursor by shardID error, reason:%v", t.LeaderKey, shardID, err)
			// 不中断循环，继续下次拉取
			return err
		}
	}
	return nil
}

func (t *InputConfig) GetCursorByLogstoreAndShardID(ctx context.Context, logStore string, shardID int) (cursor string, err error) {
	cursorKey := fmt.Sprintf(SlsCursorOffsetKeyFormat, t.LeaderKey, logStore, shardID)
	cursorFromRedis, err := t.redisClient.Get(cursorKey)
	if err != nil && !errors.Is(err, redis.Nil) {
		logger.Logger.Errorf("sls input %s_%s:%d get cursor key %s error: %v", t.LeaderKey, logStore, shardID, cursorKey, err)
		return "", err
	} else if errors.Is(err, redis.Nil) {
		// 使用initialTimestamp查询cursor
		from := "begin"
		if t.InitialTimestamp > 0 {
			from = fmt.Sprintf("%d", t.InitialTimestamp)
		}
		// 请求sls获取cursor
		return t.client.GetCursor(t.Project, logStore, shardID, from)
	}
	return cursorFromRedis.Val(), nil
}

func (t *InputConfig) SetCursorByLogstoreShardID(ctx context.Context, logStore string, shardID int, cursor string) error {
	cursorKey := fmt.Sprintf(SlsCursorOffsetKeyFormat, t.LeaderKey, logStore, shardID)
	err := t.redisClient.Set(cursorKey, cursor, 0)
	if err != nil {
		logger.Logger.Errorf("sls input %s_%s:%d set cursor key %s to %s error: %v", t.LeaderKey, logStore, shardID, cursorKey, cursor, err)
		return err
	}
	return nil
}
