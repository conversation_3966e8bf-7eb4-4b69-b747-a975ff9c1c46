package inputhttplisten

import (
	"context"
	"crypto/tls"
	"crypto/x509"
	"fmt"
	"io/ioutil"
	"net"
	"net/http"
	"os"
	"sync"

	codecjson "gstash/codec/json"
	"gstash/config"
	"gstash/helper/logger"

	"gstash/config/logevent"
)

// ModuleName is the name used in config file
const ModuleName = "httplisten"

const invalidMethodError = "Method not allowed: '%v'"
const invalidRequestError = "Invalid request received on HTTP listener. Decoder error: %+v"
const invalidAccessToken = "Invalid access token. Access denied."

const pathKey = "log_http_path"
const queryKey = "log_http_query"
const headerKey = "log_http_header"

var once sync.Once

// InputConfig holds the configuration json fields and internal objects
type InputConfig struct {
	config.InputConfig
	Address       string   `json:"address"` // host:port to listen on
	Proto         string   `json:"proto"`   //ipv4, ipv6
	Path          string   `json:"path"`    // The path to accept json HTTP POST requests on
	ServerCert    string   `json:"cert"`
	ServerKey     string   `json:"key"`
	CA            string   `json:"ca"`            // for client certification
	RequireHeader []string `json:"requireHeader"` // Require this header to be present to accept the POST ("X-Access-Token: Potato")
	DecodeQuery   bool     `json:"decodeQuery"`
	DecodeHeader  bool     `json:"decodeHeader"`
}

// DefaultInputConfig returns an InputConfig struct with default values
func DefaultInputConfig() InputConfig {
	return InputConfig{
		InputConfig: config.InputConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
		Address:       "0.0.0.0:8080",
		Path:          "/",
		RequireHeader: []string{},
	}
}

// InitHandler initialize the input plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeInputConfig, error) {
	conf := DefaultInputConfig()
	err := config.ReflectConfig(raw, &conf)
	if err != nil {
		return nil, err
	}

	conf.Codec, err = config.GetCodecDefault(ctx, *raw, codecjson.ModuleName)
	if err != nil {
		return nil, err
	}

	return &conf, nil
}

// Start wraps the actual function starting the plugin
func (i *InputConfig) Start(ctx context.Context, msgChan chan<- logevent.LogEvent) (err error) {
	logger := logger.Logger
	mux := http.NewServeMux()
	mux.HandleFunc(i.Path, func(rw http.ResponseWriter, req *http.Request) {
		// For health check
		if req.Method == http.MethodHead {
			rw.WriteHeader(http.StatusOK)
			return
		}
		// Only allow GET/POST requests (for now).
		if req.Method != http.MethodPost && req.Method != http.MethodGet {
			logger.Warnf(invalidMethodError, req.Method)
			rw.WriteHeader(http.StatusMethodNotAllowed)
			rw.Write([]byte(fmt.Sprintf(invalidMethodError, req.Method)))
			return
		}
		// Check for header
		if len(i.RequireHeader) == 2 {
			// get returns empty string if header not found
			if req.Header.Get(i.RequireHeader[0]) != i.RequireHeader[1] {
				logger.Warnln(invalidAccessToken)
				rw.WriteHeader(http.StatusForbidden)
				rw.Write([]byte(invalidAccessToken))
				return
			}
		}
		i.requestHandler(msgChan, rw, req)
	})
	proto := "tcp"
	if i.Proto == "ipv4" {
		proto = "tcp4"
	} else if i.Proto == "ipv6" {
		proto = "tcp6"
	}
	go func() {
		logger.Infof("accepting GET/POST requests to %s%s", i.Address, i.Path)
		if i.ServerCert != "" && i.ServerKey != "" {
			var tlsConfig *tls.Config
			srvCert, err := tls.LoadX509KeyPair(i.ServerCert, i.ServerKey)
			if err != nil {
				logger.Fatal(err)
				return
			}

			if i.CA != "" {
				// enable client certificate
				f, err := os.Open(i.CA)
				if err != nil {
					logger.Fatal(err)
					return
				}
				content, err := ioutil.ReadAll(f)
				ferr := f.Close()
				if ferr != nil {
					logger.Warning(ferr)
				}
				if err != nil {
					logger.Fatal(err)
					return
				}

				certPool := x509.NewCertPool()
				certPool.AppendCertsFromPEM(content)
				tlsConfig = &tls.Config{
					ClientCAs:    certPool,
					ClientAuth:   tls.RequireAndVerifyClientCert,
					Certificates: []tls.Certificate{srvCert},
				}
			} else {
				tlsConfig = &tls.Config{
					Certificates: []tls.Certificate{srvCert},
				}
			}
			l, err := tls.Listen(proto, i.Address, tlsConfig)
			if err != nil {
				logger.Fatal(err)
				return
			}
			err = http.Serve(l, mux)
		} else {
			l, err := net.Listen(proto, i.Address)
			if err != nil {
				logger.Fatal(err)
				return
			}
			err = http.Serve(l, mux)
		}
		if err != nil {
			logger.Fatal(err)
		}
	}()
	return nil
}

// Handle HTTP POST requests
func (i *InputConfig) requestHandler(msgChan chan<- logevent.LogEvent, rw http.ResponseWriter, req *http.Request) {
	logger := logger.Logger
	logger.Debugf("Received request")
	var data []byte
	var err error
	if req.Method == http.MethodPost {
		data, err = ioutil.ReadAll(req.Body)
	}
	if err != nil {
		logger.Errorf("read request body error: %v", err)
		return
	}
	//解参数
	extraMap := map[string]interface{}{pathKey: req.URL.Path}
	if i.DecodeQuery {
		queryMap := make(map[string]interface{})
		for k, v := range req.URL.Query() {
			if len(v) == 1 {
				queryMap[k] = v[0]
			} else {
				queryMap[k] = v
			}
		}
		extraMap[queryKey] = queryMap
	}

	if i.DecodeQuery {
		headerMap := make(map[string]interface{})
		for k, v := range req.Header {
			if len(v) == 1 {
				headerMap[k] = v[0]
			} else {
				headerMap[k] = v
			}
		}
		extraMap[headerKey] = headerMap
	}

	ok, err := i.Codec.Decode(context.TODO(), data, extraMap, []string{}, msgChan)
	if err != nil {
		logger.Errorf("decode request body error: %v", err)
	}
	if !ok {
		// event not sent to msgChan
		rw.WriteHeader(http.StatusInternalServerError)
		if err != nil {
			rw.Write([]byte(err.Error()))
		}
	} else if err != nil {
		// event sent to msgChan
		rw.WriteHeader(http.StatusBadRequest)
		rw.Write([]byte(fmt.Sprintf(invalidRequestError, err)))
	}
}
