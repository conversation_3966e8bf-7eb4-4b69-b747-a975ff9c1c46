/**
 * @note
 * const.go
 *
 * <AUTHOR>
 * @date 	2020-12-17
 */
package constant

import (
	"context"
	"gstash/config"
	"gstash/config/logevent"
	"gstash/helper/logger"
	"time"
)

// ModuleName is the name used in config file
const ModuleName = "constant"

// ErrorTag tag added to event when process module failed
const ErrorTag = "gstash_input_constant_error"

// InputConfig holds the configuration json fields and internal objects
type InputConfig struct {
	config.InputConfig
	Rate  int    `json:"rate,omitempty"`
	Value string `json:"value,omitempty"`
}

// DefaultInputConfig returns an InputConfig struct with default values
func DefaultInputConfig() InputConfig {
	return InputConfig{
		InputConfig: config.InputConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
		Value: "{}",
		Rate:  1,
	}
}

// InitHandler initialize the input plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeInputConfig, error) {
	conf := DefaultInputConfig()
	err := config.ReflectConfig(raw, &conf)
	if err != nil {
		return nil, err
	}

	conf.Codec, err = config.GetCodecOrDefault(ctx, *raw)

	return &conf, err
}

// Start wraps the actual function starting the plugin
func (t *InputConfig) Start(ctx context.Context, msgChan chan<- logevent.LogEvent) (err error) {
	ticker := time.NewTicker(time.Second / time.Duration(t.Rate))
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return nil
		case <-ticker.C:
			_, err = t.Codec.Decode(ctx, t.Value, nil, nil, msgChan)
			if err != nil {
				logger.Logger.Errorln(err)
			}
		}
	}
}
