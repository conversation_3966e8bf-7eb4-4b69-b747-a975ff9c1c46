package inputcloudflare

import (
	"context"
	"errors"
	"fmt"
	json "github.com/json-iterator/go"
	"gstash/helper/redis"
	"os"
	"time"

	goResty "github.com/tmsong/go-resty"

	"gstash/config"
	"gstash/config/logevent"
	"gstash/helper/http_client"
	"gstash/helper/logger"
)

// ModuleName is the name used in config file
const ModuleName = "cloudflare"

// ErrorTag tag added to event when process module failed
const ErrorTag = "gstash_input_cloudflare_error"

const QueryBody = `{ "query":  "query ListFirewallEvents($zoneTag: string, $filter: FirewallEventsAdaptiveFilter_InputObject) {    viewer {      zones(filter: { zoneTag: $zoneTag }) {        firewallEventsAdaptive(          filter: $filter    limit:$limit      orderBy: [datetime_ASC]        ) {          action          clientAsn          clientCountryName          clientIP          clientRequestPath          clientRequestQuery          clientRequestHTTPHost          datetime          source          userAgent          ruleId               rayName        }      }    }  }",  "variables": {    "zoneTag": "0eabb92dbdc1614724fb7844445b9627",    "filter": {      "datetime_geq": "%s",      "datetime_leq": "%s"    }, "limit": 10000  }}`

const TimeFormat = time.RFC3339

// InputConfig holds the configuration json fields and internal objects
type InputConfig struct {
	config.InputConfig
	URL            string `json:"url"`
	Interval       int    `json:"interval,omitempty"`
	Timeout        int    `json:"timeout,omitempty"`
	Step           int    `json:"step"`
	Token          string `json:"token"`
	PrintLog       bool   `json:"printLog"`
	OffsetKey      string `json:"offsetKey"`
	LeaderKey      string `json:"leaderKey"`      // 标记拉取的是什么日志，用于标记选主
	LeaderExpire   int    `json:"leaderExpire"`   //leader的key的过期时间
	LeaderInterval int    `json:"leaderInterval"` //leader的key多长时间try/续期一次

	hostname    string
	client      *goResty.Client
	redisClient *redis.Client
}

// DefaultInputConfig returns an InputConfig struct with default values
func DefaultInputConfig() InputConfig {
	return InputConfig{
		InputConfig: config.InputConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
		Interval:       5,
		Timeout:        10,
		Step:           120,
		LeaderExpire:   30,
		LeaderInterval: 10,
	}
}

// InitHandler initialize the input plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeInputConfig, error) {
	conf := DefaultInputConfig()
	err := config.ReflectConfig(raw, &conf)
	if err != nil {
		return nil, err
	}

	if conf.hostname, err = os.Hostname(); err != nil {
		return nil, err
	}

	conf.Codec, err = config.GetCodecOrDefault(ctx, *raw)
	conf.client = http_client.NewHttpClient(logger.Logger, conf.PrintLog)
	conf.client.SetAuthToken(conf.Token)
	conf.client.SetTimeout(time.Duration(conf.Timeout) * time.Second)
	conf.redisClient, err = redis.DefaultClient(logger.Logger)
	conf.redisClient.PrintLog = conf.PrintLog
	return &conf, err
}

// Start wraps the actual function starting the plugin
func (t *InputConfig) Start(ctx context.Context, msgChan chan<- logevent.LogEvent) (err error) {
	ticker := time.NewTicker(time.Duration(t.Interval) * time.Second)
	defer ticker.Stop()

	leader, err := t.redisClient.ElectLeader(ctx, t.LeaderKey,
		time.Duration(t.LeaderExpire)*time.Second,
		time.Duration(t.LeaderInterval)*time.Second,
		t.hostname)
	if err != nil {
		logger.Logger.Errorf("cloudflare input ElectLeader %s error, reason:%v", t.LeaderKey, err)
		return err
	}

	for {
		select {
		case <-ctx.Done():
			return nil
		case <-ticker.C:
			if !leader.IsLeader() {
				logger.Logger.Infof("cloudflare input %s not leader, leader is [%s], sleep...", t.LeaderKey, leader.LeaderIP())
				continue
			}
			t.Request(ctx, msgChan)
		}
	}
}

func (t *InputConfig) Request(ctx context.Context, msgChan chan<- logevent.LogEvent) {
	data, err := t.SendRequest()
	extra := map[string]interface{}{
		"host": t.hostname,
	}
	tags := []string{}
	if err != nil {
		tags = append(tags, ErrorTag)
	}
	if err == nil && data == nil { // 没数据直接返回
		return
	}
	_, err = t.Codec.Decode(ctx, data,
		extra,
		tags,
		msgChan)

	if err != nil {
		logger.Logger.Errorf("cloudflare request error: %v", err)
	}
}

func (t *InputConfig) SendRequest() (data []byte, err error) {
	req := t.client.NewRequest()
	ret, err := t.redisClient.Get(t.OffsetKey)
	if err != nil && !errors.Is(err, redis.Nil) {
		return nil, err
	}
	var currentOffsetStr string
	if errors.Is(err, redis.Nil) {
		currentOffsetStr = time.Unix(time.Now().Unix(), 0).Add(-time.Second * time.Duration(t.Step)).In(time.UTC).Format(TimeFormat)
	} else {
		currentOffsetStr = ret.Val()
	}
	currentOffset, err := time.ParseInLocation(TimeFormat, currentOffsetStr, time.UTC)
	if err != nil {
		return nil, err
	}
	endTimeOffset := currentOffset.Add(time.Second * time.Duration(t.Step))
	if endTimeOffset.After(time.Now().Add(-time.Second * time.Duration(t.Step))) {
		endTimeOffset = time.Now().Add(-time.Second * time.Duration(t.Step))
	}
	if currentOffset.After(endTimeOffset) {
		return nil, nil
	}
	query := fmt.Sprintf(QueryBody, currentOffset.In(time.UTC).Format(TimeFormat), endTimeOffset.In(time.UTC).Format(TimeFormat))

	req.SetBody(query)
	req.SetHeader("Content-Type", "application/json")
	req.SetResult(&FirewallEventsResponse{})

	resp, err := req.Post(t.URL)
	if err != nil {
		return nil, err
	}
	eventsResp, ok := resp.Result().(*FirewallEventsResponse)
	if !ok {
		return nil, errors.New("cloudflare response parse error")
	}
	if len(eventsResp.Errors) > 0 {
		return nil, fmt.Errorf("cloudflare response error: %v", eventsResp.Errors)
	}
	newOffset := currentOffset
	if len(eventsResp.Data.Viewer.Zones) > 0 {
		for _, event := range eventsResp.Data.Viewer.Zones[0].FirewallEventsAdaptive {
			datetime, err := time.Parse(TimeFormat, event.Datetime)
			if err != nil {
				return nil, fmt.Errorf("cloudflare response parse datetime error: %v", err)
			}
			if newOffset.Before(datetime) {
				newOffset = datetime
			}
		}
		if newOffset.Equal(currentOffset) {
			newOffset = newOffset.Add(time.Second)
		}

		if len(eventsResp.Data.Viewer.Zones[0].FirewallEventsAdaptive) < 10000 {
			newOffset = endTimeOffset
		}
		data, err = json.Marshal(eventsResp.Data.Viewer.Zones[0].FirewallEventsAdaptive)
		if err != nil {
			return nil, err
		}
	} else {
		newOffset = endTimeOffset
	}
	if err = t.redisClient.Set(t.OffsetKey, newOffset.In(time.UTC).Format(TimeFormat), 0); err != nil {
		return nil, err
	}
	return
}

// FirewallEventsResponse 定义最外层结构
type FirewallEventsResponse struct {
	Data   Data    `json:"data"`
	Errors []Error `json:"errors,omitempty"`
}

// Data 定义数据字段
type Data struct {
	Viewer Viewer `json:"viewer"`
}

// Viewer 定义 Viewer 字段
type Viewer struct {
	Zones []Zone `json:"zones"`
}

// Zone 定义 Zone 字段
type Zone struct {
	FirewallEventsAdaptive []FirewallEvent `json:"firewallEventsAdaptive"`
}

// FirewallEvent 定义单个 Firewall Event 结构
type FirewallEvent struct {
	Action                string `json:"action"`
	ClientASN             string `json:"clientAsn"`
	ClientCountryName     string `json:"clientCountryName"`
	ClientIP              string `json:"clientIP"`
	ClientRequestPath     string `json:"clientRequestPath"`
	ClientRequestQuery    string `json:"clientRequestQuery"`
	ClientRequestHTTPHost string `json:"clientRequestHTTPHost"`
	Datetime              string `json:"datetime"`
	RayName               string `json:"rayName"`
	RuleID                string `json:"ruleId"`
	Source                string `json:"source"`
	UserAgent             string `json:"userAgent"`
}

// Error 定义错误结构（可选）
type Error struct {
	Message    string    `json:"message"`
	Path       []string  `json:"path,omitempty"`
	Extensions Extension `json:"extensions,omitempty"`
}

// Extension 定义错误扩展信息
type Extension struct {
	Timestamp string `json:"timestamp"`
	RayID     string `json:"ray_id"`
}
