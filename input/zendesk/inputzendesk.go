package inputzendesk

import (
	"context"
	"errors"
	"fmt"
	json "github.com/json-iterator/go"
	"golang.org/x/sync/errgroup"
	"gstash/helper/cache"
	"gstash/helper/redis"
	"os"
	"time"

	goResty "github.com/tmsong/go-resty"

	"gstash/config"
	"gstash/config/logevent"
	"gstash/helper/http_client"
	"gstash/helper/logger"
)

// ModuleName is the name used in config file
const ModuleName = "zendesk"

// ErrorTag tag added to event when process module failed
const ErrorTag = "gstash_input_zendesk_error"

const TimeFormat = time.RFC3339

const (
	ZendeskAuditLogCursorOffsetKeyFormat  = "gstash_zendesk_audit_log_cursor_%s"
	ZendeskAccessLogCursorOffsetKeyFormat = "gstash_zendesk_access_log_cursor_%s"
	ZendeskAuditLeaderKeyFormat           = "gstash_input_zendesk_audit_leader_key_%s"
	ZendeskAccessLeaderKeyFormat          = "gstash_input_zendesk_access_leader_key_%s"

	ZendeskUserInfoCacheSeconds = 86400
)

// InputConfig holds the configuration json fields and internal objects
type InputConfig struct {
	config.InputConfig
	URL            string `json:"url"`
	Interval       int    `json:"interval"`
	Timeout        int    `json:"timeout"`
	Email          string `json:"email"`
	Token          string `json:"token"`
	PrintLog       bool   `json:"printLog"`
	OffsetKey      string `json:"offsetKey"`
	LeaderKey      string `json:"leaderKey"`      // 标记拉取的是什么日志，用于标记选主
	LeaderExpire   int    `json:"leaderExpire"`   //leader的key的过期时间
	LeaderInterval int    `json:"leaderInterval"` //leader的key多长时间try/续期一次

	hostname    string
	client      *goResty.Client
	redisClient *redis.Client
	cache       *cache.MemoryCache
}

type AuditLogResp struct {
	AuditLogs []map[string]interface{} `json:"audit_logs"`
	Meta      Meta                     `json:"meta"`
}

type AccessLogResp struct {
	AccessLogs []map[string]interface{} `json:"access_logs"`
	Meta       Meta                     `json:"meta"`
}

type Meta struct {
	HasMore      bool    `json:"has_more"`
	AfterCursor  *string `json:"after_cursor"`
	BeforeCursor *string `json:"before_cursor"`
}

// DefaultInputConfig returns an InputConfig struct with default values
func DefaultInputConfig() InputConfig {
	return InputConfig{
		InputConfig: config.InputConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
		Interval:       3,
		Timeout:        10,
		LeaderExpire:   30,
		LeaderInterval: 10,
	}
}

// InitHandler initialize the input plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeInputConfig, error) {
	conf := DefaultInputConfig()
	err := config.ReflectConfig(raw, &conf)
	if err != nil {
		return nil, err
	}

	if conf.hostname, err = os.Hostname(); err != nil {
		return nil, err
	}

	conf.Codec, err = config.GetCodecOrDefault(ctx, *raw)
	conf.client = http_client.NewHttpClient(logger.Logger, conf.PrintLog)
	conf.client.SetBasicAuth(fmt.Sprintf("%s/token", conf.Email), conf.Token)
	conf.client.SetTimeout(time.Duration(conf.Timeout) * time.Second)
	conf.client.SetHeader("Content-Type", "application/json")
	conf.redisClient, err = redis.DefaultClient(logger.Logger)
	conf.redisClient.PrintLog = conf.PrintLog
	conf.cache = cache.NewMemoryCache(128 * cache.MB)
	return &conf, err
}

func (t *InputConfig) Start(ctx context.Context, msgChan chan<- logevent.LogEvent) (err error) {
	eg, ctx := errgroup.WithContext(ctx)
	eg.Go(func() error {
		if err := t.StartAudit(ctx, msgChan); err != nil {
			logger.Logger.Errorf("zendesk input %s pull audit log failed: %v", t.LeaderKey, err)
		}
		return nil
	})
	eg.Go(func() error {
		if err := t.StartAccess(ctx, msgChan); err != nil {
			logger.Logger.Errorf("zendesk input %s pull access log failed: %v", t.LeaderKey, err)
		}
		return nil
	})
	if err := eg.Wait(); err != nil {
		return err
	}
	return nil
}

func (t *InputConfig) StartAudit(ctx context.Context, msgChan chan<- logevent.LogEvent) (err error) {
	ticker := time.NewTicker(time.Duration(t.Interval) * time.Second)
	defer ticker.Stop()

	leaderKey := fmt.Sprintf(ZendeskAuditLeaderKeyFormat, t.LeaderKey)
	leader, err := t.redisClient.ElectLeader(ctx, leaderKey,
		time.Duration(t.LeaderExpire)*time.Second,
		time.Duration(t.LeaderInterval)*time.Second,
		t.hostname)
	if err != nil {
		logger.Logger.Errorf("zendesk input ElectLeader %s error, reason:%v", leaderKey, err)
		return err
	}

	for {
		select {
		case <-ctx.Done():
			return nil
		case <-ticker.C:
			if !leader.IsLeader() {
				logger.Logger.Infof("zendesk input %s not leader, leader is [%s], sleep...", t.LeaderKey, leader.LeaderIP())
				continue
			}
			t.RequestAudit(ctx, msgChan)
		}
	}
}

func (t *InputConfig) StartAccess(ctx context.Context, msgChan chan<- logevent.LogEvent) (err error) {
	ticker := time.NewTicker(time.Duration(t.Interval) * time.Second)
	defer ticker.Stop()

	leaderKey := fmt.Sprintf(ZendeskAccessLeaderKeyFormat, t.LeaderKey)
	leader, err := t.redisClient.ElectLeader(ctx, leaderKey,
		time.Duration(t.LeaderExpire)*time.Second,
		time.Duration(t.LeaderInterval)*time.Second,
		t.hostname)
	if err != nil {
		logger.Logger.Errorf("zendesk input ElectLeader %s error, reason:%v", leaderKey, err)
		return err
	}

	for {
		select {
		case <-ctx.Done():
			return nil
		case <-ticker.C:
			if !leader.IsLeader() {
				logger.Logger.Infof("zendesk input %s not leader, leader is [%s], sleep...", t.LeaderKey, leader.LeaderIP())
				continue
			}
			t.RequestAccess(ctx, msgChan)
		}
	}
}

func (t *InputConfig) RequestAudit(ctx context.Context, msgChan chan<- logevent.LogEvent) {
	data, err := t.SendAuditRequest()
	extra := map[string]interface{}{
		"host":     t.hostname,
		"log_type": "audit",
	}
	tags := make([]string, 0)
	if err != nil {
		tags = append(tags, ErrorTag)
	}
	if err == nil && data == nil { // 没数据直接返回
		return
	}
	_, err = t.Codec.Decode(ctx, data,
		extra,
		tags,
		msgChan)

	if err != nil {
		logger.Logger.Errorf("zendesk request audit error: %v", err)
	}
}

func (t *InputConfig) SendAuditRequest() (data []byte, err error) {
	req := t.client.NewRequest()
	offsetKey := fmt.Sprintf(ZendeskAuditLogCursorOffsetKeyFormat, t.OffsetKey)
	ret, err := t.redisClient.Get(offsetKey)
	if err != nil && !errors.Is(err, redis.Nil) {
		return nil, err
	}
	var currentCursorStr string
	if !errors.Is(err, redis.Nil) {
		currentCursorStr = ret.Val()
	}

	req.SetQueryParams(map[string]string{
		"sort":        "created_at",
		"page[size]":  "100",
		"page[after]": currentCursorStr,
	})
	req.SetResult(&AuditLogResp{})

	resp, err := req.Get(fmt.Sprintf("%s/api/v2/audit_logs", t.URL))
	if err != nil {
		return nil, err
	}
	eventsResp, ok := resp.Result().(*AuditLogResp)
	if !ok {
		return nil, errors.New("zendesk audit response parse error")
	}
	if len(eventsResp.AuditLogs) > 0 {
		data, err = json.Marshal(eventsResp.AuditLogs)
		if err != nil {
			return nil, err
		}
	}
	if eventsResp.Meta.AfterCursor != nil && len(*eventsResp.Meta.AfterCursor) > 0 {
		if err = t.redisClient.Set(offsetKey, *eventsResp.Meta.AfterCursor, 0); err != nil {
			return nil, err
		}
	}
	return data, nil
}

func (t *InputConfig) RequestAccess(ctx context.Context, msgChan chan<- logevent.LogEvent) {
	data, err := t.SendAccessRequest()
	extra := map[string]interface{}{
		"host":     t.hostname,
		"log_type": "access",
	}
	tags := make([]string, 0)
	if err != nil {
		tags = append(tags, ErrorTag)
	}
	if err == nil && data == nil { // 没数据直接返回
		return
	}
	_, err = t.Codec.Decode(ctx, data,
		extra,
		tags,
		msgChan)

	if err != nil {
		logger.Logger.Errorf("zendesk request access error: %v", err)
	}
}

func (t *InputConfig) SendAccessRequest() (data []byte, err error) {
	req := t.client.NewRequest()
	offsetKey := fmt.Sprintf(ZendeskAccessLogCursorOffsetKeyFormat, t.OffsetKey)
	ret, err := t.redisClient.Get(offsetKey)
	if err != nil && !errors.Is(err, redis.Nil) {
		return nil, err
	}
	var currentCursorStr string
	if !errors.Is(err, redis.Nil) {
		currentCursorStr = ret.Val()
	}

	req.SetQueryParams(map[string]string{
		"page[size]":  "100",
		"page[after]": currentCursorStr,
	})
	req.SetResult(&AccessLogResp{})

	resp, err := req.Get(fmt.Sprintf("%s/api/v2/access_logs", t.URL))
	if err != nil {
		return nil, err
	}
	eventsResp, ok := resp.Result().(*AccessLogResp)
	if !ok {
		return nil, errors.New("zendesk access response parse error")
	}
	if len(eventsResp.AccessLogs) > 0 {
		// 2025-06-20 在accessLogs里添加user
		for _, accessLog := range eventsResp.AccessLogs {
			var userID int64
			if userIDIface, ok := accessLog["user_id"]; ok {
				if id, ok := userIDIface.(int64); ok && id > 0 {
					userID = id
				} else if id, ok := userIDIface.(float64); ok && id > 0 {
					userID = int64(id)
				}
			}
			if userID == 0 {
				continue
			}
			if userInfo, err := t.getUserInfoByUserID(userID); err != nil {
				logger.Logger.Errorf("zendesk access user info error: %v", err)
			} else if userInfo.Name != "" {
				accessLog["user_name"] = userInfo.Name
			}
		}
		data, err = json.Marshal(eventsResp.AccessLogs)
		if err != nil {
			return nil, err
		}
	}
	if eventsResp.Meta.AfterCursor != nil && len(*eventsResp.Meta.AfterCursor) > 0 {
		if err = t.redisClient.Set(offsetKey, *eventsResp.Meta.AfterCursor, 0); err != nil {
			return nil, err
		}
	}
	return data, nil
}

type UserInfoResp struct {
	User  *UserInfo   `json:"user"`
	Error interface{} `json:"error"`
}
type UserInfo struct {
	ID    int64  `json:"id"`
	Name  string `json:"name"`
	Email string `json:"email"`
}

func (t *InputConfig) getUserInfoByUserID(userID int64) (userInfo *UserInfo, err error) {
	cacheKey := fmt.Sprintf("zendesk_user_info_%d", userID)
	userInfo = &UserInfo{}
	if err = t.cache.Get(cacheKey, &userInfo); err == nil {
		return userInfo, nil
	}
	req := t.client.NewRequest()
	req.SetResult(&UserInfoResp{})
	resp, err := req.Get(fmt.Sprintf("%s/api/v2/users/%d.json", t.URL, userID))
	if err != nil {
		return nil, err
	}
	userResp, ok := resp.Result().(*UserInfoResp)
	if !ok {
		return nil, errors.New("zendesk user response type error")
	} else if userResp == nil || userResp.User == nil {
		userInfo = &UserInfo{}
	} else {
		userInfo = userResp.User
	}
	_ = t.cache.Set(cacheKey, userInfo, ZendeskUserInfoCacheSeconds)
	return userInfo, nil
}
