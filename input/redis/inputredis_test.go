package inputredis

import (
	"context"
	"fmt"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/alicebob/miniredis"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	codecjson "gstash/codec/json"
	"gstash/config"
	"gstash/helper/logger"
)

var s *miniredis.Miniredis
var timeNow time.Time

func init() {
	logger.Logger.SetLevel(logrus.DebugLevel)
	config.RegistInputHandler(ModuleName, InitHandler)
	config.RegistCodecHandler(codecjson.ModuleName, codecjson.InitHandler)
}

func TestMain(m *testing.M) {
	// initialize redis server
	s = miniredis.NewMiniRedis()
	err := s.StartAddr("localhost:6380") // change the port
	if err != nil {
		panic(err)
	}
	defer s.Close()

	timeNow = time.Now().UTC()
	ret := m.Run()

	os.Exit(ret)
}

func Test_input_redis_module_batch(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	for i := 0; i < 10; i++ {
		_, err := s.Lpush("gstash-test", fmt.Sprintf("{\"@timestamp\":\"%s\",\"message\":\"inputredis test message\"}", timeNow.Format(time.RFC3339Nano)))
		require.NoError(err)
	}

	ctx := context.Background()
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()
	conf, err := config.LoadFromYAML([]byte(strings.TrimSpace(`
debugch: true
input:
  - type: redis
    host: localhost:6380
    key: gstash-test
    connections: 1
    batch_count: 10
    codec: json
	`)))
	require.NoError(err)
	require.NoError(conf.Worker[0].Start(ctx))

	time.Sleep(500 * time.Millisecond)
	for i := 0; i < 10; i++ {
		if event, err := conf.Worker[0].TestGetOutputEvent(100 * time.Millisecond); assert.NoError(err) {
			require.Equal(timeNow.UnixNano(), event.Timestamp.UnixNano())
			require.Equal("inputredis test message", event.Message)
		}
	}
}

func Test_input_redis_module_single(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	_, err := s.Lpush("gstash-test", fmt.Sprintf("{\"@timestamp\":\"%s\",\"message\":\"inputredis test message\"}", timeNow.Format(time.RFC3339Nano)))
	require.NoError(err)

	ctx := context.Background()
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()
	conf, err := config.LoadFromYAML([]byte(strings.TrimSpace(`
debugch: true
input:
  - type: redis
    host: localhost:6380
    key: gstash-test
    batch_count: 1
    blocking_timeout: 5s
    codec: json
	`)))
	require.NoError(err)
	require.NoError(conf.Worker[0].Start(ctx))

	time.Sleep(500 * time.Millisecond)
	if event, err := conf.Worker[0].TestGetOutputEvent(100 * time.Millisecond); assert.NoError(err) {
		require.Equal(timeNow.UnixNano(), event.Timestamp.UnixNano())
		require.Equal("inputredis test message", event.Message)
	}
}
