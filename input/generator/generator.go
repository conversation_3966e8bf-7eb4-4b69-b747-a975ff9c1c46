/**
 * @note
 * generator
 *
 * <AUTHOR>
 * @date 	2020-10-28
 */
package generator

import (
	"context"

	"gstash/config"
	"gstash/config/logevent"
)

// ModuleName is the name used in config file
const ModuleName = "generator"

// ErrorTag tag added to event when process module failed
const ErrorTag = "gstash_input_http_error"

// InputConfig holds the configuration json fields and internal objects
type InputConfig struct {
	config.InputConfig
	Message string `json:"message"`
	Count   int    `json:"count"`
}

// DefaultInputConfig returns an InputConfig struct with default values
func DefaultInputConfig() InputConfig {
	return InputConfig{
		InputConfig: config.InputConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
	}
}

// InitHandler initialize the input plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeInputConfig, error) {
	conf := DefaultInputConfig()
	err := config.ReflectConfig(raw, &conf)
	if err != nil {
		return nil, err
	}
	return &conf, err
}

// Start wraps the actual function starting the plugin
func (t *InputConfig) Start(ctx context.Context, msgChan chan<- logevent.LogEvent) (err error) {
	for i := 0; i < t.Count; i++ {
		msgChan <- logevent.LogEvent{Message: t.Message}
	}
	for {
		select {
		case <-ctx.Done():
			return nil
		default:
		}
	}
}
