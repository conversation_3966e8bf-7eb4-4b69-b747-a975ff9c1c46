package inputdockerlog

import (
	"context"
	"strings"
	"testing"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gstash/config"
	"gstash/helper/logger"
)

func init() {
	logger.Logger.SetLevel(logrus.DebugLevel)
	config.RegistInputHandler(ModuleName, InitHandler)
	config.RegistCodecHandler(config.DefaultCodecName, config.DefaultCodecInitHandler)
}

func Test_input_dockerlog_module(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	ctx := context.Background()
	conf, err := config.LoadFromYAML([]byte(strings.TrimSpace(`
debugch: true
input:
  - type: dockerlog
    dockerurl: "unix:///var/run/docker.sock"
    sincepath: "sincedb-test"
	`)))
	require.NoError(err)
	err = conf.Worker[0].Start(ctx)
	if err != nil {
		require.True(ErrorPingFailed.In(err))
		t.Skip("skip test input dockerlog module")
	}

	time.Sleep(500 * time.Millisecond)
	if event, err := conf.Worker[0].TestGetOutputEvent(100 * time.Millisecond); assert.NoError(err) {
		t.Log(event)
	}
}
