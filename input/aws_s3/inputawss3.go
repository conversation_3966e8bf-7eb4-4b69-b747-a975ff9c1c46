package aws_s3

import (
	"bufio"
	"compress/gzip"
	"context"
	"errors"
	"fmt"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"gstash/config"
	"gstash/config/logevent"
	"gstash/helper/logger"
	"gstash/helper/redis"
	"io"
	"net"
	"time"
)

// ModuleName is the name used in config file
const ModuleName = "s3"

// InputConfig holds the configuration json fields and internal objects
type InputConfig struct {
	config.InputConfig
	// s3配置
	Region           string `json:"region"`
	Bucket           string `json:"bucket"`
	Prefix           string `json:"prefix"`
	ArchivePrefix    string `json:"archivePrefix"`
	AccessKeyId      string `json:"accessKeyId"`
	SecretAccessKey  string `json:"secretAccessKey"`
	StartAfter       string `json:"startAfter"`
	Limit            int64  `json:"limit"` // 一次最多请求多少文件
	QueryInterval    int    `json:"queryInterval"`
	IgnoreBeforeTime int64  `json:"ignoreBeforeTime"` // 忽略多少秒前的文件
	// 选主+记录同步信息的配置
	LeaderKey            string `json:"leaderKey"`      // 标记拉取的是什么日志，用于标记选主
	LeaderExpire         int    `json:"leaderExpire"`   //leader的key的过期时间
	LeaderInterval       int    `json:"leaderInterval"` //leader的key多长时间try/续期一次
	SyncingKeyExpireTime int64  `json:"syncingKeyExpireTime"`
	SyncedKeyExpireTime  int64  `json:"syncedKeyExpireTime"`
	// 压缩相关
	Decompress string `json:"decompress"`

	syncingHashKey string
	syncedHashKey  string
}

// DefaultInputConfig returns an InputConfig struct with default values
func DefaultInputConfig() InputConfig {
	return InputConfig{
		InputConfig: config.InputConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
		Bucket:          "",
		AccessKeyId:     "",
		SecretAccessKey: "",
		Region:          "",
	}
}

// InitHandler initialize the input plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeInputConfig, error) {
	conf := DefaultInputConfig()
	err := config.ReflectConfig(raw, &conf)
	if err != nil {
		return nil, err
	}

	if conf.QueryInterval <= 0 {
		conf.QueryInterval = 180
	}

	if conf.SyncingKeyExpireTime <= 60 {
		conf.SyncingKeyExpireTime = 60 // 没syncing完，最少60秒就清理
	}

	if conf.SyncedKeyExpireTime <= 300 {
		conf.SyncedKeyExpireTime = 300 // 同步完成的，最少5分钟清理
	}

	// 如果不执行归档逻辑，那么忽略的时间不可以长过syncedKey清理的时间
	if conf.IgnoreBeforeTime > conf.SyncedKeyExpireTime && conf.ArchivePrefix == "" {
		conf.IgnoreBeforeTime = conf.SyncedKeyExpireTime
	}

	if conf.Limit <= 0 {
		conf.Limit = 1000
	}

	if conf.LeaderExpire <= 0 {
		conf.LeaderExpire = 60
	}

	if conf.LeaderInterval <= 0 {
		conf.LeaderInterval = 10
	}

	if conf.LeaderKey == "" {
		conf.LeaderKey = "default"
	}

	conf.syncedHashKey = fmt.Sprintf(S3LogSyncedKeyFormat, conf.LeaderKey)
	conf.syncingHashKey = fmt.Sprintf(S3LogSyncingKeyFormat, conf.LeaderKey)

	conf.Codec, err = config.GetCodecOrDefault(ctx, *raw)

	if err != nil {
		return nil, err
	}

	return &conf, err
}

// Start wraps the actual function starting the plugin
func (t *InputConfig) Start(ctx context.Context, msgChan chan<- logevent.LogEvent) (err error) {
	ticker := time.NewTicker(time.Second * time.Duration(t.QueryInterval))
	defer ticker.Stop()
	redisCli, err := redis.DefaultClient(logger.Logger)
	if err != nil {
		logger.Logger.Errorf("s3 %s input get redis client error:%v", t.LeaderKey, err)
		return err
	}

	awsConfig := &aws.Config{
		DisableSSL: aws.Bool(true),
		Region:     aws.String(t.Region),
	}

	if t.AccessKeyId != "" && t.SecretAccessKey != "" {
		awsConfig.Credentials = credentials.NewStaticCredentials(t.AccessKeyId, t.SecretAccessKey, "")
	}
	sess, err := session.NewSession(awsConfig)
	if err != nil {
		logger.Logger.Errorf("s3 %s input NewSession error: %v", t.LeaderKey, err)
		return err
	}

	s3Service := s3.New(sess)

	host, err := getHost()
	if err != nil {
		logger.Logger.Errorf("s3 %s input getHost error, reason: %v", t.LeaderKey, err)
		return
	}

	leader, err := redisCli.ElectLeader(ctx, fmt.Sprintf(GstashInputS3LeaderKey, t.LeaderKey),
		time.Duration(t.LeaderExpire)*time.Second,
		time.Duration(t.LeaderInterval)*time.Second,
		host)
	if err != nil {
		logger.Logger.Errorf("s3 %s input input ElectLeader error: %v", t.LeaderKey, err)
		return err
	}
	logger.Logger.Printf("s3 %s input begin getting log.", t.LeaderKey)

	t.getLogs(ctx, redisCli, s3Service, msgChan)
	if leader.IsLeader() {
		//清理过期的key
		t.cleanExpiredHistoryKey(redisCli, t.syncingHashKey, time.Duration(t.SyncingKeyExpireTime)*time.Second)
		t.cleanExpiredHistoryKey(redisCli, t.syncedHashKey, time.Duration(t.SyncedKeyExpireTime)*time.Second)
	}

	for {
		select {
		case <-ctx.Done():
			return nil
		case <-ticker.C:
			t.getLogs(ctx, redisCli, s3Service, msgChan)
			if !leader.IsLeader() {
				logger.Logger.Infof("s3 %s input not leader, leader is [%s], skip cleaning expired history key", t.LeaderKey, leader.LeaderIP())
				continue
			}
			//清理过期的key
			t.cleanExpiredHistoryKey(redisCli, t.syncingHashKey, time.Duration(t.SyncingKeyExpireTime)*time.Second)
			t.cleanExpiredHistoryKey(redisCli, t.syncedHashKey, time.Duration(t.SyncedKeyExpireTime)*time.Second)
		}
	}
}

func (t *InputConfig) getLogs(ctx context.Context, redisCli *redis.Client, s3Service *s3.S3, msgChan chan<- logevent.LogEvent) {
	listInput := &s3.ListObjectsV2Input{
		Bucket:  aws.String(t.Bucket),
		Prefix:  aws.String(t.Prefix),
		MaxKeys: aws.Int64(t.Limit),
	}

	// 设置文件列表的起始位置，如果已经配置，直接用，没有配置，根据redis缓存决定
	if t.StartAfter != "" {
		listInput.StartAfter = aws.String(t.StartAfter)
	}

	for {
		// 取文件列表
		out, err := s3Service.ListObjectsV2WithContext(ctx, listInput)
		if err != nil {
			logger.Logger.Errorf("s3 %s input ListObjectsWithContext error: %v", t.LeaderKey, err)
			return
		}
		// 特殊情况处理
		if out == nil || out.Contents == nil {
			return
		}
		logger.Logger.Printf("s3 %s input ListObjectsWithContext resp length: %d", t.LeaderKey, len(out.Contents))
		for _, content := range out.Contents {
			// 过滤一下
			if t.IgnoreBeforeTime > 0 && time.Now().Sub(*content.LastModified).Seconds() > float64(t.IgnoreBeforeTime) {
				continue
			}
			// 特殊情况处理
			if content.Key == nil || *content.Key == "" {
				logger.Logger.Errorf("s3 %s input content.key is empty.", t.LeaderKey)
				continue
			}

			// 获取已经同步过的日志
			synced, err := redisCli.HGet(t.syncedHashKey, *content.Key)
			if err != nil && !errors.Is(err, redis.Nil) {
				logger.Logger.Errorf("s3 %s input get synced hash key error: %v", t.LeaderKey, err)
				continue
			}
			// 已经同步过
			if err == nil && synced.Val() != "" {
				continue
			}

			// 抢锁
			suc, err := redisCli.HSetNX(t.syncingHashKey, *content.Key, time.Now().Format(TimestampFormat))
			if !suc || err != nil { //获取失败，放弃
				logger.Logger.Infof("s3 %s input get syncing lock not success, ret: %v, error: %v", t.LeaderKey, suc, err)
				continue
			}

			// 下载文件
			err = t.getLog(ctx, *content.Key, redisCli, s3Service, msgChan)

			if err != nil {
				logger.Logger.Errorf("s3 %s input get log %s error: %v", t.LeaderKey, *content.Key, err)
			} else if t.ArchivePrefix != "" {
				// 需要归档文件，执行归档
				err = t.archiveS3Object(ctx, s3Service, *content.Key)
				if err != nil {
					logger.Logger.Errorf("s3 %s input archive log %s error: %v", t.LeaderKey, *content.Key, err)
				}
			}
		}
		if out.IsTruncated != nil && *out.IsTruncated && out.NextContinuationToken != nil && *out.NextContinuationToken != "" { // 如果还有后续，就继续拉取
			listInput.ContinuationToken = out.NextContinuationToken
		} else { // 拉取完了，结束
			return
		}
	}
}

func (t *InputConfig) getLog(ctx context.Context, objectKey string, redisCli *redis.Client, s3Service *s3.S3, msgChan chan<- logevent.LogEvent) (err error) {
	syncedHashKey := fmt.Sprintf(S3LogSyncedKeyFormat, t.LeaderKey)
	rawObject, err := s3Service.GetObjectWithContext(ctx,
		&s3.GetObjectInput{
			Bucket: aws.String(t.Bucket),
			Key:    aws.String(objectKey),
		})

	if err != nil {
		return err
	}
	if rawObject == nil || rawObject.Body == nil {
		return
	}
	defer rawObject.Body.Close()
	// 如果需要解压缩
	body := rawObject.Body
	if t.Decompress == "gzip" {
		gr, err := gzip.NewReader(rawObject.Body)
		if err != nil {
			return errors.New("gzip read error " + err.Error())
		}
		defer gr.Close()
		body = gr
	}

	// 按行读
	reader := bufio.NewReader(body)
	for {
		line, errRead := reader.ReadString('\n')
		if errRead != nil && errRead != io.EOF {
			return errRead
		}
		if len(line) > 0 {
			_, err = t.Codec.Decode(ctx, line, map[string]interface{}{"aws_s3_object_key": objectKey}, nil, msgChan)
			if err != nil {
				logger.Logger.Errorf("s3 %s input scanner decode error: %v, continue next row.", t.LeaderKey, err)
			}
		}
		if errRead == io.EOF {
			break
		}
	}
	// 文件同步完成，记录缓存
	err = redisCli.HSet(syncedHashKey, map[string]interface{}{objectKey: time.Now().Format(TimestampFormat)})
	if err != nil {
		logger.Logger.Errorf("s3 %s input hset syncedHashKey %s error: %v", t.LeaderKey, objectKey, err)
	}
	return nil
}

func (t *InputConfig) cleanExpiredHistoryKey(redisCli *redis.Client, hashKey string, duration time.Duration) {
	kvMap, err := redisCli.HGetAll(hashKey)
	if err != nil {
		logger.Logger.Errorf("s3 %s input cleanExpiredHistoryKey hgetall %s error: %v", t.LeaderKey, hashKey, err)
		return
	}
	for k, v := range kvMap {
		timestamp, err := time.Parse(TimestampFormat, v)
		if err != nil {
			logger.Logger.Errorf("s3 %s input cleanExpiredHistoryKey %s:%s parse timestamp error: %v", t.LeaderKey, k, v, err)
			continue
		}

		if time.Now().Sub(timestamp) > duration {
			_, err = redisCli.HDel(hashKey, k)
			if err != nil {
				logger.Logger.Errorf("s3 %s input cleanExpiredHistoryKey hdel %s:%s error: %v", t.LeaderKey, k, v, err)
				continue
			}
		}
	}
}

func (t *InputConfig) archiveS3Object(ctx context.Context, s3Service *s3.S3, objectKey string) error {
	newObjectKey := t.ArchivePrefix + objectKey // 新的objectKey
	_, err := s3Service.CopyObjectWithContext(ctx, &s3.CopyObjectInput{
		Bucket:     aws.String(t.Bucket),
		CopySource: aws.String(fmt.Sprintf("%s/%s", t.Bucket, objectKey)),
		Key:        aws.String(newObjectKey),
	})
	if err != nil {
		return err
	}
	_, err = s3Service.DeleteObjectWithContext(ctx, &s3.DeleteObjectInput{
		Bucket: aws.String(t.Bucket),
		Key:    aws.String(objectKey),
	})
	return err
}

func getHost() (string, error) {
	gInnerIP := ""
	addrs, err := net.InterfaceAddrs()
	if err != nil {
		return gInnerIP, err
	}

	for _, address := range addrs {
		if ipnet, ok := address.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
			if ipnet.IP.To4() != nil {
				gInnerIP = ipnet.IP.String()
				break
			}
		}
	}
	return gInnerIP, nil
}
