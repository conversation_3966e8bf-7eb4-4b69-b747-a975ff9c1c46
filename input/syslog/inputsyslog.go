package inputsyslog

import (
	"context"
	"net"
	"strconv"
	"strings"

	"github.com/tmsong/go-syslog"
	errutil "github.com/tmsong/utils/error"

	codecjson "gstash/codec/json"
	"gstash/config"
	"gstash/config/logevent"
	"gstash/helper/logger"
)

// ModuleName is the name used in config file
const ModuleName = "syslog"

const (
	PROTO_TCP  = "tcp"
	PROTO_TCP4 = "tcp4"
	PROTO_TCP6 = "tcp6"
	PROTO_UDP  = "udp"
	PROTO_UDP4 = "udp4"
	PROTO_UDP6 = "udp6"
)

var (
	errInvalidAddr  = errutil.NewFactory("address is invalid: %v")
	errInvalidProto = errutil.NewFactory("proto is invalid: %v")
)

// InputConfig holds the configuration json fields and internal objects
type InputConfig struct {
	config.InputConfig
	Address string `json:"address"` // host:port to listen on
	Proto   string `json:"proto"`
}

// DefaultInputConfig returns an InputConfig struct with default values
func DefaultInputConfig() InputConfig {
	return InputConfig{
		InputConfig: config.InputConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
		Address: "0.0.0.0:514",
		Proto:   "udp",
	}
}

// InitHandler initialize the input plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeInputConfig, error) {
	conf := DefaultInputConfig()
	err := config.ReflectConfig(raw, &conf)
	if err != nil {
		return nil, err
	}
	//校验ip和port
	if ipPort := strings.Split(conf.Address, ":"); len(ipPort) != 2 {
		return nil, errInvalidAddr.New(nil, conf.Address)
	} else if port, err := strconv.Atoi(ipPort[1]); err != nil || port < 1 || port > 65535 {
		return nil, errInvalidAddr.New(nil, conf.Address)
	} else if ipPort[0] != "" && net.ParseIP(ipPort[0]) == nil {
		return nil, errInvalidAddr.New(nil, conf.Address)
	}
	conf.Proto = strings.ToLower(conf.Proto)
	//校验proto
	if conf.Proto != PROTO_UDP && conf.Proto != PROTO_TCP &&
		conf.Proto != PROTO_TCP4 && conf.Proto != PROTO_TCP6 &&
		conf.Proto != PROTO_UDP4 && conf.Proto != PROTO_UDP6 {
		return nil, errInvalidProto.New(nil, conf.Proto)
	}
	conf.Codec, err = config.GetCodecDefault(ctx, *raw, codecjson.ModuleName)
	if err != nil {
		return nil, err
	}

	return &conf, nil
}

// Start wraps the actual function starting the plugin
func (t *InputConfig) Start(ctx context.Context, msgChan chan<- logevent.LogEvent) (err error) {
	logChan := make(syslog.LogPartsChannel)
	handler := syslog.NewChannelHandler(logChan)

	server := syslog.NewServer()
	server.SetFormat(syslog.Automatic)
	server.SetHandler(handler)
	if t.Proto == PROTO_TCP || t.Proto == PROTO_TCP4 || t.Proto == PROTO_TCP6 {
		err = server.ListenTCP(t.Address, t.Proto)
	} else {
		err = server.ListenUDP(t.Address, t.Proto)
	}
	if err != nil {
		return err
	} else if err = server.Boot(); err != nil {
		return err
	}
	logger.Logger.Infof("syslog input: start listening %s on %s", t.Proto, t.Address)

	for {
		select {
		case <-ctx.Done():
			err = server.Kill()
			logger.Logger.Infoln("input syslog stopped")
			server.Wait()
			return err
		case data := <-logChan:
			_, err = t.Codec.Decode(ctx, map[string]interface{}(data), nil, nil, msgChan)
			if err != nil {
				logger.Logger.Errorf("input syslog failed to decode %v using codec %v", data, t.Codec)
			}
		}
	}
}
