gstash input mysql
====================

## Synopsis
DataSourceName string `json:"dataSourceName"`
TableName      string `json:"tableName"`
Target         string `json:"target"`
Condition      string `json:"condition"`
SelectInterval int    `json:"pushInterval"`
LeaderExpire   int    `json:"leaderExpire"`   //leader的key的过期时间
LeaderInterval int    `json:"leaderInterval"` //leader的key多长时间try/续期一次

	OffsetField      string `json:"offsetField"`
	OffsetFieldType  string `json:"offsetFieldType"`
	OffsetTimeFormat string `json:"offsetTimeFormat"`
```yaml
input:
  # type Must be "mysql"
  - type: mysql

    # mysql config, (required)
    dataSourceName: USER:PASSWORD@tcp(HOST:PORT)/DATABASES

    # mysql table name, (required)
    tableName: TBALENAME

    # needed field, (required)
    target: field1,dield2

    # query condition (optional)
    condition: status=1

    # Kafka consumer consume initial offset from oldest
    offset_oldest: true

    # Consumer group partition assignment strategy (range, roundrobin)
    assignor: roundrobin

    # use SASL authentication (required)
    offsetField: id
    offsetFieldType: int64

    # use SASL authentication (optional)
    offsetTimeFormat: 
```
