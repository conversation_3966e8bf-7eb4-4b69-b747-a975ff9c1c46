package inputmysql

import (
	"context"
	"errors"
	"fmt"
	jsoniter "github.com/json-iterator/go"
	errutil "github.com/tmsong/utils/error"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	codecjson "gstash/codec/json"
	hmysql "gstash/helper/mysql"
	"gstash/helper/redis"
	"net"
	"strconv"
	"strings"
	"time"

	"gstash/config"
	"gstash/config/logevent"
	"gstash/helper/logger"
)

// ModuleName is the name used in config file
const ModuleName = "mysql"

var (
	errNotContainsOffsetField = errutil.NewFactory("mysql input target:%s not contains offsetField:%s")
)

const (
	GstashInputMysqlHostRedisKey = "gstash_input_mysql_host_key:%s"
	GstashInputMysqlLatestOffset = "gstash_input_latest_offset_key:%s"
)

// InputConfig holds the configuration json fields and internal objects
type InputConfig struct {
	config.InputConfig
	DataSourceName string   `json:"dataSourceName"`
	TableName      string   `json:"tableName"`
	Select         string   `json:"select"`
	Condition      string   `json:"condition"`
	Joins          []string `json:"joins"`
	RedisKey       string   `json:"redisKey"`

	QueryInterval  int `json:"queryInterval"`
	LeaderExpire   int `json:"leaderExpire"`   //leader的key的过期时间
	LeaderInterval int `json:"leaderInterval"` //leader的key多长时间try/续期一次

	OffsetField        string `json:"offsetField"`
	OffsetFieldInEvent string `json:"offsetFieldInEvent"` // 这个是以防多表join的时候，offsetKey是一个类似a.id的结构，会导致在查询结果中取不到对应的value
	OffsetFieldType    string `json:"offsetFieldType"`
	OffsetTimeFormat   string `json:"offsetTimeFormat"`

	Limit int `json:"limit"`

	// 2025-08-08 新功能，通过一个正则来匹配表名
	TableNameRegex       string `json:"tableNameRegex"`
	TableRefreshInterval int    `json:"tableRefreshInterval"` // 单位：分钟
	MaxTableCount        int64  `json:"maxTableCount"`        // 大于0时，只取最后N个表来收集

	tableNameInfoMap map[string]*tableInfo

	db         *gorm.DB
	redisValue string
}

type tableInfo struct {
	TableName    string      `json:"tableName"`
	OffsetKey    string      `json:"offsetKey"`
	LatestOffset interface{} `json:"latestOffset"`
	RedisValue   string      `json:"redisValue"`
}

// DefaultInputConfig returns an InputConfig struct with default values
func DefaultInputConfig() InputConfig {
	return InputConfig{
		InputConfig: config.InputConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
		DataSourceName:     "",
		TableName:          "",
		Select:             "*",
		QueryInterval:      30,
		OffsetFieldType:    "int64",
		OffsetField:        "id",
		OffsetFieldInEvent: "id",
		OffsetTimeFormat:   time.RFC3339Nano,
		Limit:              100,

		TableRefreshInterval: 30,
	}
}

// InitHandler initialize the input plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeInputConfig, error) {
	conf := DefaultInputConfig()
	err := config.ReflectConfig(raw, &conf)
	if err != nil {
		return nil, err
	}

	if conf.LeaderExpire <= 0 {
		conf.LeaderExpire = 60
	}
	if conf.LeaderInterval <= 0 {
		conf.LeaderInterval = 10
	}
	if conf.TableRefreshInterval <= 0 {
		conf.TableRefreshInterval = 30
	}
	if conf.Limit <= 0 {
		conf.Limit = 100
	}

	if conf.OffsetFieldInEvent == "" {
		conf.OffsetFieldInEvent = conf.OffsetField
	}

	conf.tableNameInfoMap = make(map[string]*tableInfo)

	if conf.TableName != "" {
		conf.tableNameInfoMap[conf.TableName] = &tableInfo{
			TableName: conf.TableName,
		}
		if conf.RedisKey == "" {
			conf.RedisKey = conf.TableName
		}
		conf.tableNameInfoMap[conf.TableName].OffsetKey = fmt.Sprintf(GstashInputMysqlLatestOffset, conf.RedisKey)
	}

	conf.db, err = gorm.Open(mysql.Open(conf.DataSourceName), &gorm.Config{Logger: hmysql.NewGormLogger(logger.Logger)})
	if err != nil {
		return nil, err
	}

	conf.Codec, err = config.GetCodecDefault(ctx, *raw, codecjson.ModuleName)
	if err != nil {
		return nil, err
	}
	return &conf, err
}

// Start wraps the actual function starting the plugin
func (i *InputConfig) Start(ctx context.Context, msgChan chan<- logevent.LogEvent) (err error) {
	if !strings.Contains(i.Select, i.OffsetField) && i.Select != "*" {
		return errNotContainsOffsetField.New(nil, i.Select, i.OffsetField)
	}

	redisCli, err := redis.DefaultClient(logger.Logger)
	redisCli.PrintLog = true
	if err != nil {
		logger.Logger.Errorf("mysql input get redis client error, reason:%v", err)
		return err
	}

	host, err := getHost()
	if err != nil {
		logger.Logger.Errorf("mysql input get host error, reason:%v", err)
		return err
	}

	leaderKey := fmt.Sprintf(GstashInputMysqlHostRedisKey, i.RedisKey)
	leader, err := redisCli.ElectLeader(ctx, leaderKey,
		time.Duration(i.LeaderExpire)*time.Second,
		time.Duration(i.LeaderInterval)*time.Second,
		host)
	if err != nil {
		logger.Logger.Errorf("mysql input %s ElectLeader error, reason:%v", i.RedisKey, err)
		return err
	}

	ticker := time.NewTicker(time.Duration(i.QueryInterval) * time.Second)
	defer ticker.Stop()

	tableRefreshTicker := time.NewTicker(time.Duration(i.TableRefreshInterval) * time.Minute) // 15分钟refresh一下是不是有新的表了
	defer tableRefreshTicker.Stop()

	for {
		select {
		case <-ctx.Done():
			return nil

		case <-ticker.C:
			if !leader.IsLeader() {
				logger.Logger.Infof("mysql input %s not leader, leader is [%s], sleep...", i.RedisKey, leader.LeaderIP())
				continue
			}

			for _, tInfo := range i.tableNameInfoMap {
				i.Query(ctx, msgChan, redisCli, tInfo)
			}
		case <-tableRefreshTicker.C:

		}
	}
}

// RefreshTables 2025-08-08 新功能，针对动态表名的情况，通过一个正则来匹配表名
func (i *InputConfig) RefreshTables(ctx context.Context) {

}

func (i *InputConfig) Query(ctx context.Context, msgChan chan<- logevent.LogEvent, redisCli *redis.Client, tInfo *tableInfo) {
	dest := make([]map[string]interface{}, 0)
	cmd, err := redisCli.Get(tInfo.OffsetKey)
	if err != nil && !errors.Is(err, redis.Nil) {
		logger.Logger.Errorln("mysql input redis get error:", err, "abort")
		return
	}

	switch i.OffsetFieldType {
	case "time":
		if cmd.Val() == "" {
			tInfo.LatestOffset = time.Unix(0, 0)
			tInfo.RedisValue = tInfo.LatestOffset.(time.Time).Format(i.OffsetTimeFormat)
		} else {
			tInfo.LatestOffset, err = time.Parse(i.OffsetTimeFormat, cmd.Val())
			if err != nil {
				logger.Logger.Errorln("mysql input redis get time offset error:", err)
				return
			}
			tInfo.RedisValue = tInfo.LatestOffset.(time.Time).Format(i.OffsetTimeFormat)
		}

	case "int64":
		if offsetInt, err := cmd.Int64(); err != nil {
			if errors.Is(err, redis.Nil) {
				tInfo.LatestOffset = interface{}(0)
			} else {
				logger.Logger.Errorln("mysql input redis get int64 offset error:", err)
				return
			}
		} else {
			tInfo.LatestOffset = interface{}(offsetInt)
		}
	}

	mysqlClient := i.db.Table(tInfo.TableName).Select(i.Select)
	if i.Condition != "" {
		mysqlClient = mysqlClient.Where(i.Condition)
	}
	for _, join := range i.Joins {
		mysqlClient = mysqlClient.Joins(join)
	}
	err = mysqlClient.Where(i.OffsetField+" > ?", tInfo.LatestOffset).
		Order(i.OffsetField + " ASC").Limit(i.Limit).Scan(&dest).Error
	if err != nil {
		logger.Logger.Errorln("mysql input scan error:", err)
		return
	}

	if len(dest) == 0 {
		return
	}

	// 开始更新并记录offset
	for _, v := range dest {
		switch i.OffsetFieldType {
		case "time":
			if t, ok := v[i.OffsetFieldInEvent].(time.Time); ok {
				if lt, ok := tInfo.LatestOffset.(time.Time); ok {
					if t.After(lt) {
						tInfo.LatestOffset = v[i.OffsetFieldInEvent]
						tInfo.RedisValue = tInfo.LatestOffset.(time.Time).Format(i.OffsetTimeFormat)
					}
				}
			}
		case "int64":
			compare(v[i.OffsetFieldInEvent], tInfo)
		}

		data, err := jsoniter.Marshal(v)
		if err != nil {
			logger.Logger.Errorln("mysql input marshal error:", err)
			return
		}
		ok, err := i.Codec.Decode(ctx, data, nil, []string{}, msgChan)
		if !ok {
			logger.Logger.Errorf("mysql input decode message to msg chan error : %v", err)
		}
	}
	err = redisCli.Set(tInfo.OffsetKey, tInfo.RedisValue, 0)
	if err != nil {
		logger.Logger.Errorln("mysql input redis set error:", err)
		return
	}
}

func getHost() (string, error) {
	gInnerIP := ""
	addrs, err := net.InterfaceAddrs()
	if err != nil {
		return gInnerIP, err
	}

	for _, address := range addrs {
		if ipnet, ok := address.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
			if ipnet.IP.To4() != nil {
				gInnerIP = ipnet.IP.String()
				break
			}
		}
	}
	return gInnerIP, nil
}

func compare(offsetFieldIface interface{}, info *tableInfo) {
	lastOffset := int64(0)
	offsetField := int64(0)
	switch t := offsetFieldIface.(type) {
	case int:
		offsetField = int64(t)
	case uint64:
		offsetField = int64(t)
	case int64:
		offsetField = t
	case int32:
		offsetField = int64(t)
	case uint32:
		offsetField = int64(t)
	}

	switch t := info.LatestOffset.(type) {
	case int:
		lastOffset = int64(t)
	case uint64:
		lastOffset = int64(t)
	case int64:
		lastOffset = t
	case int32:
		lastOffset = int64(t)
	case uint32:
		lastOffset = int64(t)
	}

	if lastOffset < offsetField {
		info.LatestOffset = offsetField
		info.RedisValue = strconv.FormatInt(offsetField, 10)
	}
}
