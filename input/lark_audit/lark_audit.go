package inputlarkaudit

import (
	"context"
	"errors"
	"fmt"
	"os"
	"strconv"
	"time"

	json "github.com/json-iterator/go"
	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkadmin "github.com/larksuite/oapi-sdk-go/v3/service/admin/v1"
	larkcontact "github.com/larksuite/oapi-sdk-go/v3/service/contact/v3"

	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/masker"

	"gstash/config"
	"gstash/config/logevent"
	"gstash/helper/cache"
	"gstash/helper/logger"
	"gstash/helper/redis"
)

// ModuleName is the name used in config file
const ModuleName = "lark_audit"

// ErrorTag tag added to event when process module failed
const ErrorTag = "gstash_input_lark_audit_error"

const (
	LarkAuditLogTimestampKeyFormat = "gstash_lark_audit_log_timestamp_%s"
	LarkAuditLeaderKeyFormat       = "gstash_input_lark_audit_leader_key_%s"
	LarkAuditUserCacheKeyFormat    = "gstash_input_lark_audit_user_cache_%s"
	LarkAuditUserInfoCacheSeconds  = 86400 * 7

	OperatorTypeUser = 1
)

// InputConfig holds the configuration json fields and internal objects
type InputConfig struct {
	config.InputConfig
	URL              string `json:"url"`
	Interval         int    `json:"interval"` // 多久请求一次lark
	AppID            string `json:"appID"  mask:"crypt"`
	AppSecret        string `json:"appSecret"  mask:"crypt"`
	PrintLog         bool   `json:"printLog"`
	InitialTimestamp int64  `json:"initialTimestamp"`
	BufferTime       int64  `json:"bufferTime"` // 由于lark的审计数据有延迟，所以需要一个延迟时间，给lark反应一下
	OffsetKey        string `json:"offsetKey"`
	LeaderKey        string `json:"leaderKey"`      // 标记拉取的是什么日志，用于标记选主
	LeaderExpire     int    `json:"leaderExpire"`   //leader的key的过期时间
	LeaderInterval   int    `json:"leaderInterval"` //leader的key多长时间try/续期一次

	cache       *cache.MemoryCache
	hostname    string
	client      *lark.Client
	redisClient *redis.Client
}

type Meta struct {
	HasMore      bool    `json:"has_more"`
	AfterCursor  *string `json:"after_cursor"`
	BeforeCursor *string `json:"before_cursor"`
}

// DefaultInputConfig returns an InputConfig struct with default values
func DefaultInputConfig() InputConfig {
	return InputConfig{
		InputConfig: config.InputConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
		Interval:       30,  // 默认30秒拉一次
		BufferTime:     300, // 默认5分钟
		LeaderExpire:   30,
		LeaderInterval: 10,
	}
}

// InitHandler initialize the input plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeInputConfig, error) {
	conf := DefaultInputConfig()
	err := config.ReflectConfig(raw, &conf)
	if err != nil {
		return nil, err
	}
	confIface, err := masker.PropCrypt(&conf) // 解密
	if err != nil {
		return nil, err
	}

	conf = *confIface.(*InputConfig)
	if conf.hostname, err = os.Hostname(); err != nil {
		return nil, err
	}

	conf.Codec, err = config.GetCodecOrDefault(ctx, *raw)
	conf.client = lark.NewClient(conf.AppID, conf.AppSecret, lark.WithOpenBaseUrl(lark.LarkBaseUrl))
	conf.redisClient, err = redis.DefaultClient(logger.Logger)
	conf.redisClient.PrintLog = conf.PrintLog
	conf.cache = cache.NewMemoryCache(128 * cache.MB)
	return &conf, err
}

func (t *InputConfig) Start(ctx context.Context, msgChan chan<- logevent.LogEvent) (err error) {
	ticker := time.NewTicker(time.Duration(t.Interval) * time.Second)
	defer ticker.Stop()

	leaderKey := fmt.Sprintf(LarkAuditLeaderKeyFormat, t.LeaderKey)
	leader, err := t.redisClient.ElectLeader(ctx, leaderKey,
		time.Duration(t.LeaderExpire)*time.Second,
		time.Duration(t.LeaderInterval)*time.Second,
		t.hostname)
	if err != nil {
		logger.Logger.Errorf("lark_audit input ElectLeader %s error, reason:%v", leaderKey, err)
		return err
	}

	for {
		select {
		case <-ctx.Done():
			return nil
		case <-ticker.C:
			if !leader.IsLeader() {
				logger.Logger.Infof("lark_audit input %s not leader, leader is [%s], sleep...", t.LeaderKey, leader.LeaderIP())
				continue
			}
			err = t.RequestAudit(ctx, msgChan)
			if err != nil {
				logger.Logger.Infof("lark_audit input %s request audit error: %v", t.LeaderKey, err)
			}
		}
	}
}

type AuditInfo struct {
	EventId         *string                          `json:"event_id,omitempty"`          // 事件id
	UniqueId        *string                          `json:"unique_id,omitempty"`         // 唯一id
	EventName       *string                          `json:"event_name,omitempty"`        // 事件名称
	EventNameDesc   string                           `json:"eventNameDesc,omitempty"`     // 事件名称描述
	DepartmentIds   []string                         `json:"department_ids,omitempty"`    // 用户所属部门的ID列表
	EventModule     *int                             `json:"event_module,omitempty"`      // 模块
	EventModuleName string                           `json:"eventModuleName,omitempty"`   // 模块名称
	OperatorType    *int                             `json:"operator_type,omitempty"`     // 操作人类型
	OperatorValue   *string                          `json:"operator_value,omitempty"`    // 操作人id
	OperatorInfo    *UserInfo                        `json:"operatorInfo,omitempty"`      // 如果是操作人，给出信息
	Objects         []*AuditObjectEntity             `json:"objects,omitempty"`           // 操作对象列表
	Recipients      []*AuditRecipientEntity          `json:"recipients,omitempty"`        // 接收者对象列表
	EventTime       *int                             `json:"event_time,omitempty"`        // 事件时间
	Ip              *string                          `json:"ip,omitempty"`                // ip信息
	OperatorApp     *string                          `json:"operator_app,omitempty"`      // 第三方isvID
	AuditContext    *larkadmin.AuditContext          `json:"audit_context,omitempty"`     // 环境信息
	Extend          *larkadmin.AuditEventExtend      `json:"extend,omitempty"`            // 事件级别的扩展
	OperatorAppName *string                          `json:"operator_app_name,omitempty"` // 第三方isv名称
	CommonDrawers   *larkadmin.ApiAuditCommonDrawers `json:"common_drawers,omitempty"`    // 扩展字段信息
	AuditDetail     *larkadmin.AuditDetail           `json:"audit_detail,omitempty"`      // 日志扩展信息
	OperatorTenant  *string                          `json:"operator_tenant,omitempty"`   // 操作人企业编号
	OperatorDetail  *larkadmin.OperatorDetail        `json:"operator_detail,omitempty"`   // 操作人详情
}

type AuditObjectEntity struct {
	ObjectTypeName string `json:"objectTypeName,omitempty"`
	*larkadmin.AuditObjectEntity
}

type AuditRecipientEntity struct {
	RecipientTypeName string `json:"recipientTypeName,omitempty"`
	*larkadmin.AuditRecipientEntity
}

func (t *InputConfig) RequestAudit(ctx context.Context, msgChan chan<- logevent.LogEvent) (err error) {
	latestTimestamp := time.Now().Unix() - t.BufferTime
	auditReqBuilder := larkadmin.NewListAuditInfoReqBuilder().PageSize(100).Latest(int(latestTimestamp))
	timestampKey := fmt.Sprintf(LarkAuditLogTimestampKeyFormat, t.LeaderKey)
	ret, err := t.redisClient.Get(timestampKey)
	if err != nil && !errors.Is(err, redis.Nil) { // redis有问题，直接返回
		return err
	} else if errors.Is(err, redis.Nil) && t.InitialTimestamp > 0 { // 使用initialTimestamp
		auditReqBuilder = auditReqBuilder.Oldest(int(t.InitialTimestamp))
	} else if timestamp, err := ret.Int(); err != nil {
		return err
	} else {
		auditReqBuilder = auditReqBuilder.Oldest(timestamp)
	}
	for hasMore := true; hasMore; {
		auditInfos := make([]*AuditInfo, 0)
		resp, err := t.client.Admin.AuditInfo.List(context.Background(), auditReqBuilder.Build())
		if err != nil {
			return err
		} else {
			hasMore = false
			if resp.Data == nil {
				continue
			}
			for _, item := range resp.Data.Items {
				if item != nil {
					auditInfos = append(auditInfos, t.richAuditInfo(item))
				}
			}
			if resp.Data.HasMore != nil && *resp.Data.HasMore && resp.Data.PageToken != nil && *resp.Data.PageToken != "" {
				auditReqBuilder = auditReqBuilder.PageToken(*resp.Data.PageToken)
				hasMore = true
			}
		}
		if data, err := json.Marshal(auditInfos); err != nil {
			return err
		} else if _, err = t.Codec.Decode(ctx, data,
			nil,
			nil,
			msgChan); err != nil {
			return err
		}
	}
	if err = t.redisClient.Set(timestampKey, strconv.FormatInt(latestTimestamp, 10), 0); err != nil {
		return err
	}
	return nil
}

type UserInfo struct {
	UnionID *string `json:"union_id,omitempty"`
	UserID  *string `json:"user_id,omitempty"`
	OpenID  *string `json:"open_id,omitempty"`
	Name    *string `json:"name,omitempty"`
	EnName  *string `json:"en_name,omitempty"`
}

func (t *InputConfig) richAuditInfo(auditInfo *larkadmin.AuditInfo) (newInfo *AuditInfo) {
	newInfo = &AuditInfo{
		EventId:         auditInfo.EventId,
		UniqueId:        auditInfo.UniqueId,
		EventName:       auditInfo.EventName,
		EventModule:     auditInfo.EventModule,
		OperatorType:    auditInfo.OperatorType,
		OperatorValue:   auditInfo.OperatorValue,
		EventTime:       auditInfo.EventTime,
		Ip:              auditInfo.Ip,
		OperatorApp:     auditInfo.OperatorApp,
		AuditContext:    auditInfo.AuditContext,
		Extend:          auditInfo.Extend,
		OperatorAppName: auditInfo.OperatorAppName,
		CommonDrawers:   auditInfo.CommonDrawers,
		AuditDetail:     auditInfo.AuditDetail,
		OperatorTenant:  auditInfo.OperatorTenant,
		OperatorDetail:  auditInfo.OperatorDetail,
	}

	// 映射事件名称描述
	if auditInfo.EventName != nil {
		newInfo.EventNameDesc = eventNameDescMap[*auditInfo.EventName]
	}

	// 映射事件模块名称
	if auditInfo.EventModule != nil {
		newInfo.EventModuleName = eventModuleNameMap[*auditInfo.EventModule]
	}

	// 处理部门ID列表 - 如果需要部门信息，可以通过用户信息获取
	// 注意：larkadmin.OperatorDetail 中没有 DepartmentIds 字段
	// 如果需要部门信息，需要通过其他方式获取

	// 处理操作对象列表
	if auditInfo.Objects != nil {
		newInfo.Objects = make([]*AuditObjectEntity, 0, len(auditInfo.Objects))
		for _, obj := range auditInfo.Objects {
			if obj == nil {
				continue
			}
			newObj := &AuditObjectEntity{
				AuditObjectEntity: obj,
			}
			// 映射对象类型名称
			if obj.ObjectType != nil {
				newObj.ObjectTypeName = objectTypeNameMap[*obj.ObjectType]
			}
			newInfo.Objects = append(newInfo.Objects, newObj)
		}
	}

	// 处理接收者对象列表
	if auditInfo.Recipients != nil {
		newInfo.Recipients = make([]*AuditRecipientEntity, 0, len(auditInfo.Recipients))
		for _, recipient := range auditInfo.Recipients {
			if recipient == nil {
				continue
			}
			newRecipient := &AuditRecipientEntity{
				AuditRecipientEntity: recipient,
			}
			// 映射接收者类型名称
			if recipient.RecipientType != nil {
				newRecipient.RecipientTypeName = recipientTypeNameMap[*recipient.RecipientType]
			}
			newInfo.Recipients = append(newInfo.Recipients, newRecipient)
		}
	}

	/*
		// 如果是用户操作，查询用户信息
		if auditInfo.OperatorType != nil && *auditInfo.OperatorType == OperatorTypeUser && auditInfo.OperatorValue != nil {
			if userInfo, err := t.getUserInfoByUserID(*auditInfo.OperatorValue); err != nil {
				logger.Logger.Errorf("lark_audit %s get user %s info error: %v", t.LeaderKey, *auditInfo.OperatorValue, err)
			} else {
				newInfo.OperatorInfo = userInfo
			}
		}
	*/

	return newInfo
}

func (t *InputConfig) getUserInfoByUserID(userID string) (userInfo *UserInfo, err error) {
	cacheKey := fmt.Sprintf(LarkAuditUserCacheKeyFormat, userID)
	if err = t.cache.Get(cacheKey, &userInfo); err == nil {
		return userInfo, nil
	}
	if ret, err := t.redisClient.Get(cacheKey); err == nil {
		if err = json.UnmarshalFromString(ret.Val(), &userInfo); err == nil {
			return userInfo, nil
		}
	}
	resp, err := t.client.Contact.User.Get(context.Background(), larkcontact.NewGetUserReqBuilder().UserId(userID).UserIdType("user_id").Build())
	if err != nil {
		return nil, err
	}
	if resp.Data == nil || resp.Data.User == nil {
		return nil, common.ErrRecordNotFound
	}
	userInfo = &UserInfo{
		UnionID: resp.Data.User.UnionId,
		UserID:  resp.Data.User.UserId,
		OpenID:  resp.Data.User.OpenId,
		Name:    resp.Data.User.Name,
		EnName:  resp.Data.User.EnName,
	}
	_ = t.cache.Set(cacheKey, userInfo, LarkAuditUserInfoCacheSeconds)
	if userInfoStr, err := json.MarshalToString(userInfo); err == nil {
		_ = t.redisClient.Set(cacheKey, userInfoStr, time.Duration(LarkAuditUserInfoCacheSeconds)*time.Second)
	}
	return userInfo, nil
}

var eventModuleNameMap = map[int]string{
	1:  "云文档",
	2:  "即时消息",
	3:  "通讯录",
	4:  "工作台",
	5:  "音视频会话",
	6:  "账号行为",
	8:  "设备",
	9:  "公司圈",
	13: "OKR",
	16: "邮箱",
	18: "审批",
	21: "日历",
	22: "URL",
}

var eventNameDescMap = map[string]string{
	"space_create_doc":                               "创建云文档",
	"space_read_doc":                                 "访问云文档",
	"space_delete_doc":                               "删除云文档",
	"space_edit_doc":                                 "编辑云文档",
	"space_comment_doc":                              "评论云文档",
	"space_update_collaborator_doc":                  "更新文档协作者（包括内/外部协作者）",
	"space_update_share_setting_doc":                 "更新文档分享设置",
	"space_export_doc":                               "导出云文档",
	"space_download_file":                            "下载文件",
	"space_import_doc":                               "导入云文档",
	"space_share_to_rdApp":                           "分享到第三方应用",
	"space_front_export_csv":                         "电子表格导出CSV",
	"space_front_export_image":                       "移动端导出图片并下载",
	"space_open_with_rdApp":                          "用第三方应用打开预览文件",
	"space_print_doc":                                "打印云文档",
	"space_copy_content":                             "复制内容",
	"set_doc_sec_label":                              "设置文档密级标签",
	"space_add_comment":                              "添加评论",
	"space_add_reply":                                "添加回复",
	"space_create_copy":                              "创建副本",
	"space_create_spacecopy":                         "创建知识空间副本",
	"space_delete_doccurrent":                        "删除文件/文件夹（不影响子文件/文件夹）",
	"space_delete_reply":                             "删除回复",
	"space_delete_retlabel":                          "清除保留标签",
	"space_demoslides":                               "演示幻灯片",
	"space_download_history":                         "下载历史版本",
	"space_edit_reply":                               "编辑回复",
	"space_edit_retlabel":                            "修改保留标签",
	"space_edit_space_member":                        "编辑知识空间成员",
	"space_edit_space_member_auth":                   "修改知识空间成员权限",
	"space_edit_space_owner":                         "编辑知识空间管理员",
	"space_finish_comment":                           "解决评论",
	"space_modfiy_security_setting":                  "修改知识空间安全设置",
	"space_move":                                     "移动到文件夹/知识库",
	"space_move_owner":                               "转移所有权",
	"space_recover_history":                          "还原历史版本",
	"space_reopen_comment":                           "恢复评论",
	"space_save_module":                              "保存为我的模板",
	"space_set_retlabel":                             "设置保留标签",
	"space_share":                                    "分享文件/文件夹",
	"space_share_content":                            "分享文件中内容",
	"space_update_collaborator_auth":                 "修改协作者权限",
	"space_update_spaceshare_status":                 "开启/关闭空间链接分享",
	"space_view_history":                             "查看历史版本",
	"turn_down_doc_sec_label":                        "调低密级标签敏感等级",
	"turn_up_doc_sec_label":                          "调高密级标签敏感等级",
	"space_authapprove":                              "云文档申请权限",
	"im_download_image":                              "下载图片",
	"im_download_video":                              "下载视频",
	"im_open_link":                                   "对话内打开链接",
	"im_start_external_chat":                         "对陌生人发起单聊",
	"im_load_file_to_local":                          "服务端文件加载到本地（第一次点击下载会触发，相当于缓存到了本地）",
	"im_forward_file":                                "转发文件",
	"im_create_chat":                                 "创建群组",
	"im_delete_chat":                                 "解散群组",
	"im_join_chat":                                   "加入群组",
	"im_quit_chat":                                   "退出群组",
	"im_add_chatadmin":                               "添加群组的群管理员",
	"im_addtochat":                                   "添加群组成员",
	"im_admin_no_restrict_ctrl":                      "管理员免受群聊保密模式限制成员",
	"im_chat_editimage":                              "编辑图片",
	"im_chat_previewfile":                            "在线预览非图片或视频文件",
	"im_chat_uploadfile":                             "上传文件",
	"im_chat_withdraw":                               "撤回",
	"im_delete_chatadmin":                            "删除群组的群管理员",
	"im_deletefromchat":                              "删除群组成员",
	"im_download":                                    "下载文件",
	"im_forward_chatadmin":                           "转让群主",
	"im_ocr":                                         "截图提取文字",
	"im_savetospace":                                 "保存到我的空间",
	"im_screencap":                                   "即时消息录屏",
	"im_send_link":                                   "在聊天中发送url",
	"im_snaphot":                                     "即时消息截图",
	"im_copy_image":                                  "复制图片",
	"im_read_doc":                                    "访问聊天窗口中的文档",
	"im_download_file":                               "下载聊天窗口中的文档",
	"im_open_with_rdApp":                             "用第三方应用打开聊天窗口中的预览文件",
	"im_chat_pin_show_pin_list":                      "展开置顶看板",
	"im_chat_pin_show_in_chat":                       "查看置顶内容",
	"im_chat_pin_stick":                              "将置顶内容“固定在首位”",
	"im_chat_pin_update_permission":                  "更改置顶管理权限",
	"im_chat_pin_update":                             "修改置顶链接的标题",
	"im_chat_pin_sidebar_search":                     "点击工具栏中的“搜索”",
	"im_chat_pin_click_open_browser":                 "在置顶链接菜单内点击“在浏览器中打开”",
	"im_chat_pin_sidebar_file":                       "点击工具栏中的“文件”",
	"im_chat_pin_unstick":                            "将置顶内容“取消固定”",
	"im_chat_pin_hover_show PC Hover":                "查看置顶预览卡片",
	"im_chat_pin_reorder":                            "对置顶进行拖拽排序",
	"im_chat_pin_create":                             "添加置顶",
	"im_chat_pin_click_copy_url":                     "在置顶链接菜单内点击“复制链接”",
	"im_chat_pin_sidebar_add_link":                   "点击工具栏中的“添加置顶链接”",
	"im_chat_pin_click_back_to_chat":                 "点击置顶消息 “回到原文”",
	"im_export_chat_chatter":                         "导出群成员信息",
	"im_chat_pin_sidebar_announce":                   "点击工具栏中的“添加/查看群公告”",
	"im_chat_pin_click_open_url":                     "点击置顶链接卡片后打开链接",
	"im_chat_pin_delete":                             "移除置顶",
	"contact_obtain_mobile":                          "获取手机号",
	"contact_view_department_struct":                 "查看部门组织架构",
	"contact_view_personal_info":                     "查看个人信息页",
	"contact_add_external_friend":                    "添加外部联系人为好友",
	"contact_personal_avatar_update":                 "更新个人头像",
	"workplace_open_app":                             "打开应用",
	"workplace_app_download_doc":                     "下载工作台应用中的文档",
	"workplace_app_open_with_rdApp":                  "用第三方应用打开工作台应用中的预览文件",
	"vc_create_video_meeting":                        "创建音视频通话",
	"vc_finish_video_meeting":                        "结束音视频通话",
	"vc_quit_video_meeting":                          "退出音视频通话",
	"vc_join_video_meeting":                          "加入音视频通话",
	"vc_changerange":                                 "修改入会权限",
	"vc_invite_video_meeting":                        "邀请其他用户进入视频会议",
	"vc_share_video_meeting":                         "分享视频会议",
	"vc_sharescreen_video_meeting":                   "共享屏幕/文档",
	"vc_createclip":                                  "创建妙记片段",
	"vc_deleteclip":                                  "删除妙记片段",
	"vc_deletemeta":                                  "将某篇妙记移入回收站",
	"vc_deletemetafromtrash":                         "永久删除某篇妙记",
	"vc_drivebatchtranscribe":                        "通过导入Lark云端文件生成妙记",
	"vc_exportmeta":                                  "导出妙记",
	"vc_revertmeta":                                  "从回收站恢复某篇妙记",
	"vc_setaudiostatus":                              "通过移动端录音生成妙记文件",
	"vc_share_to_room_video_meeting":                 "会议室投屏",
	"vc_sharebychat":                                 "通过会话分享妙记",
	"vc_sharebylink":                                 "通过链接修改妙记权限",
	"vc_shareclipbychat":                             "通过会话分享妙记片段",
	"vc_sharecollaborator":                           "管理妙记协作者",
	"vc_uploadbymeeting":                             "通过会中录制生成妙记文件",
	"vc_uploadmeta":                                  "通过上传本地文件生成妙记",
	"vc_viewclip":                                    "访问某篇片段妙记",
	"vc_viewmeta":                                    "访问某篇妙记",
	"vc_start_transcript":                            "会议转录开始",
	"vc_stop_transcript":                             "停止会议转录",
	"vc_request_transcript":                          "申请会议转录",
	"vc_access_transcript":                           "同意会议转录",
	"vc_refuse_transcript":                           "拒绝会议转录",
	"vc_access_local_record":                         "同意本地录制",
	"vc_refuse_local_record":                         "拒绝本地录制",
	"vc_refuse_record":                               "拒绝会议录制",
	"vc_access_record":                               "同意会议录制",
	"vc_stop_record":                                 "会议录制结束",
	"vc_request_record":                              "申请会议录制",
	"vc_start_local_record":                          "开始本地录制",
	"vc_stop_local_record":                           "停止本地录制",
	"vc_request_local_record":                        "请求本地录制",
	"vc_manage_stop_local_record":                    "停止参会人的本地录制",
	"vc_start_record":                                "会议录制开始",
	"vc_start_screen_control":                        "开启远程屏幕控制",
	"vc_finish_screen_control":                       "关闭远程屏幕控制",
	"account_login":                                  "登录",
	"account_passport_changeaccount":                 "切换账号",
	"account_passport_logout":                        "登出",
	"account_passport_renew_logoncre":                "登录凭证更新",
	"account_passport_renew_password":                "登录密码更新",
	"user_active_exit_team":                          "注销账号",
	"account_passport_update_otp":                    "更新动态口令设置",
	"account_passport_update_fa":                     "更新两步验证设置",
	"account_passport_updatesparecre":                "更新备用验证方式设置",
	"account_passport_revoke_appauth":                "管理应用授权",
	"account_passport_renew_fidocre":                 "更新通行密钥设置",
	"device_screen_recording":                        "录屏",
	"device_screenshot":                              "截屏",
	"moments_copy_comment":                           "复制评论",
	"moments_copy_image":                             "复制图片",
	"moments_copy_link":                              "复制链接",
	"moments_copy_post":                              "复制动态",
	"moments_create_comment":                         "发布评论",
	"moments_create_official":                        "创建官方号",
	"moments_create_post":                            "发布动态",
	"moments_create_reaction":                        "表情回复",
	"moments_delete_comment":                         "删除评论",
	"moments_delete_post":                            "删除帖子",
	"moments_delete_reaction":                        "取消表情回复",
	"moments_download_image":                         "下载图片",
	"moments_edit_official":                          "修改官方号",
	"moments_enter_post_detail":                      "点击动态详情页",
	"moments_forward_post":                           "转发动态",
	"moments_login_official":                         "登录官方号",
	"moments_preview_image":                          "预览图片",
	"moments_preview_video":                          "预览视频",
	"moments_view_feed_post":                         "浏览动态",
	"moments_viewmoment":                             "查看动态",
	"email_batchexport":                              "批量导出邮件",
	"email_copy":                                     "复制邮件内容",
	"email_delete":                                   "永久删除邮件",
	"email_downloadfile":                             "下载附件",
	"email_downloadmail":                             "下载邮件",
	"email_editforward":                              "设置自动转发",
	"email_foawardasatt":                             "将邮件作为附件转发",
	"email_localopen":                                "本地应用打开附件",
	"email_login":                                    "登录公共邮箱",
	"email_loginthird":                               "登录第三方邮箱客户端",
	"email_move":                                     "移动邮件",
	"email_print":                                    "打印邮件",
	"email_read":                                     "阅读邮件",
	"email_reply":                                    "回复/转发邮件",
	"email_send":                                     "发送邮件",
	"email_sharefile":                                "分享附件至会话",
	"email_sharemail":                                "分享邮件至会话",
	"email_urlclick":                                 "点击邮件正文链接",
	"officeapprove_operate_clear":                    "操作删除",
	"officeapprove_add_submanager":                   "管理后台添加子管理员",
	"officeapprove_process_intervene":                "流程干预",
	"officeapprove_hand_over":                        "审批交接",
	"officeapprove_operate_save":                     "操作保存",
	"officeapprove_bitable_add_data":                 "添加审批数据",
	"officeapprove_admin_export_data":                "管理后台导出数据",
	"officeapprove_create_approval_group":            "新建审批流程分组",
	"officeapprove_enable_approval":                  "启用审批流程",
	"officeapprove_update_approval_group":            "更新审批流程分组",
	"officeapprove_update_approval":                  "修改审批流程",
	"officeapprove_delete_approval":                  "删除审批流程",
	"officeapprove_delete_approval_group":            "删除审批流程分组",
	"officeapprove_disable_approval":                 "停用审批流程",
	"officeapprove_export_data":                      "客户端导出数据",
	"officeapprove_create_approval":                  "新建审批流程",
	"officeapprove_export_efficiency_task_detail":    "导出任务明细",
	"officeapprove_process_diagnosis":                "查看流程诊断",
	"officeapprove_export_efficiency_process_detail": "导出流程明细",
	"officeapprove_efficiency":                       "查看个人效率",
	"officeapprove_share_approval":                   "分享流程提交入口",
	"officeapprove_operate_submit_again":             "操作重新提交",
	"officeapprove_operate_recall":                   "操作撤回",
	"officeapprove_operate_upload_attachment":        "上传附件",
	"officeapprove_operate_upload_image":             "上传图片",
	"officeapprove_operate_share":                    "分享审批单",
	"officeapprove_operate_urgent":                   "催办",
	"officeapprove_operate_discuss":                  "操作讨论",
	"officeapprove_operate_addsign":                  "操作加签",
	"officeapprove_operate_rollback":                 "操作退回",
	"officeapprove_operate_removesign":               "操作减签",
	"officeapprove_operate_comment":                  "操作评论",
	"officeapprove_operate_cc":                       "操作抄送",
	"officeapprove_operate_reject":                   "操作拒绝",
	"officeapprove_operate_forward":                  "操作转交",
	"officeapprove_operate_approve":                  "操作同意",
	"officeapprove_modify_instance":                  "修改审批单",
	"officeapprove_create_instance":                  "发起审批单",
	"officeapprove_detail_instance":                  "查看审批单",
	"cal_create_event":                               "创建日程",
	"cal_add_attachment":                             "添加日程附件",
	"cal_remove_attachment":                          "移除日程附件",
	"cal_delete_event":                               "删除日程",
	"cal_add_remove_participants":                    "添加/移除日程参与者",
	"cal_add_remove_meeting_room":                    "添加/移除会议室",
	"cal_mod_event_permission":                       "更改日程参与者权限",
	"cal_create_meeting_group":                       "创建会议群",
	"cal_transfer_event":                             "日程转让",
	"cal_share_event":                                "分享日程至会话",
	"cal_join_event":                                 "加入日程",
	"calendar_create_scheduler":                      "创建预约活动",
	"calendar_edit_scheduler":                        "修改预约活动",
	"calendar_del_scheduler":                         "删除预约活动",
	"cal_delete_calendar":                            "新建日历",
	"cal_create_calendar":                            "删除日历",
	"cal_change_def_cal_permissions":                 "更改日历默认权限",
	"cal_add_remove_calendar_sharee":                 "新增/删除日历共享人",
	"cal_modify_sharee_permissions":                  "更改日历共享人权限",
	"cal_add_party_account":                          "添加第三方日历账号",
	"cal_subscribe_calendar":                         "订阅日历",
	"cal_unsubscribe_calendar":                       "取消订阅日历",
	"cal_share_calendar_to_session":                  "分享日历至会话",
	"cal_remove_party_account":                       "移除第三方日历账号",
}

var objectTypeNameMap = map[string]string{
	"1":      "用户",
	"2":      "部门",
	"3":      "租户",
	"4":      "聊天群",
	"5":      "doc云文档",
	"6":      "sheet云文档",
	"7":      "file云文档",
	"8":      "folder云文档",
	"9":      "mindnote云文档",
	"10":     "bitable云文档",
	"11":     "slide云文档",
	"12":     "机器人",
	"13":     "音视频通话",
	"14":     "普通文件，值类型为app_id与app_file_id，一般为聊天框中的预览文件",
	"15":     "链接",
	"16":     "IM用户标识类型",
	"17":     "图片",
	"18":     "视频",
	"19":     "普通文件，值类型为fileKey，一般为聊天框中用户发送的普通文件",
	"20":     "小程序和H5应用",
	"21":     "打开的本地文件",
	"29":     "OKR",
	"31":     "docx云文档",
	"106":    "slides云文档",
	"107":    "知识空间",
	"121":    "IM场景文件",
	"425":    "妙记",
	"426":    "同步块",
	"210001": "日历",
	"210002": "日历事件",
	"210003": "日历预约",
	"210004": "第三方日历账号",
}

var recipientTypeNameMap = map[string]string{
	"1": "用户",
	"2": "部门",
	"4": "会话",
}
