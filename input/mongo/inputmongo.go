package mongo

import (
	"context"
	"fmt"
	jsoniter "github.com/json-iterator/go"
	errutil "github.com/tmsong/utils/error"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	codecjson "gstash/codec/json"
	"gstash/config"
	"gstash/config/logevent"
	"gstash/helper/logger"
	"gstash/helper/redis"
	"gstash/helper/utils"
	"log"
	"net"
	"strconv"
	"strings"
	"time"
)

const ModuleName = "mongo"

var (
	errNotContainsOffsetField = errutil.NewFactory("mongo input target:%s not contains offsetField:%s")
)

const (
	GSTASH_INPUT_MONGO_HOST_REDIS_KEY = "gstash_input_mongo_host_key:%s%s"
	GSTASH_INPUT_MONGO_LATEST_OFFSET  = "gstash_input_mongo_latest_offset_key:%s%s"
)

type InputConfig struct {
	config.InputConfig
	URI              string                   `json:"uri"`
	DBName           string                   `json:"dbName"`
	CollectionName   string                   `json:"collectionName"`
	Projection       string                   `json:"projection"`
	Limit            int64                    `json:"limit"`
	Condition        []map[string]interface{} `json:"condition"`
	OffsetField      string                   `json:"offsetField"`
	OffsetFieldType  string                   `json:"offsetFieldType"`
	OffsetTimeFormat string                   `json:"offsetTimeFormat"`
	redisKey         string
	LatestOffset     interface{}

	client *mongo.Client

	QueryInterval  int `json:"queryInterval"`
	LeaderExpire   int `json:"leaderExpire"`   //leader的key的过期时间
	LeaderInterval int `json:"leaderInterval"` //leader的key多长时间try/续期一次
}

// DefaultInputConfig returns an InputConfig struct with default values
func DefaultInputConfig() InputConfig {
	return InputConfig{
		InputConfig: config.InputConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
		URI:              "",
		DBName:           "",
		CollectionName:   "",
		Limit:            100,
		OffsetTimeFormat: time.RFC3339Nano,
	}
}

// InitHandler initialize the input plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeInputConfig, error) {
	conf := DefaultInputConfig()
	err := config.ReflectConfig(raw, &conf)
	if err != nil {
		return nil, err
	}

	if conf.LeaderExpire <= 0 {
		conf.LeaderExpire = 60
	}
	if conf.LeaderInterval <= 0 {
		conf.LeaderInterval = 10
	}

	if conf.Limit <= 0 {
		conf.Limit = 100
	}

	conf.client, err = mongo.NewClient(options.Client().ApplyURI(conf.URI))
	if err != nil {
		log.Fatal(err)
	}

	err = conf.client.Connect(ctx)
	if err != nil {
		log.Fatal(err)
	}

	conf.Codec, err = config.GetCodecDefault(ctx, *raw, codecjson.ModuleName)
	if err != nil {
		return nil, err
	}
	return &conf, err
}

// Start wraps the actual function starting the plugin
func (i *InputConfig) Start(ctx context.Context, msgChan chan<- logevent.LogEvent) (err error) {
	if !strings.Contains(i.Projection, i.OffsetField) && i.Projection != "*" {
		return errNotContainsOffsetField.New(nil, i.Projection, i.OffsetField)
	}

	redisCli, err := redis.DefaultClient(logger.Logger)
	if err != nil {
		logger.Logger.Errorf("mongo input get redis client error, reason:%v", err)
		return err
	}

	host, err := getHost()
	if err != nil {
		logger.Logger.Errorf("ERROR: mongo input get host error, reason:%v", err)
		return err
	}

	leaderKey := fmt.Sprintf(GSTASH_INPUT_MONGO_HOST_REDIS_KEY, i.DBName, i.CollectionName)
	leader, err := redisCli.ElectLeader(ctx, leaderKey,
		time.Duration(i.LeaderExpire)*time.Second,
		time.Duration(i.LeaderInterval)*time.Second,
		host)
	if err != nil {
		logger.Logger.Errorf("mongo input ElectLeader  error, reason:%v", err)
		return err
	}

	ticker := time.NewTicker(time.Duration(i.QueryInterval) * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			err = i.client.Disconnect(ctx)
			if err != nil {
				i.client.Disconnect(ctx)
			}
			return nil

		case <-ticker.C:
			if !leader.IsLeader() {
				logger.Logger.Infof("mongo input not leader, leader is [%s], sleep...", leader.LeaderIP())
				continue
			}

			i.Find(ctx, msgChan, redisCli)
		}
	}
}

func (i *InputConfig) Find(ctx context.Context, msgChan chan<- logevent.LogEvent, redisCli *redis.Client) {
	offsetKey := fmt.Sprintf(GSTASH_INPUT_MONGO_LATEST_OFFSET, i.DBName, i.CollectionName)
	cmd, err := redisCli.Get(offsetKey)
	if err != nil && err != redis.Nil {
		logger.Logger.Errorln("mongo input redis get error:", err, "abort")
		return
	}

	switch i.OffsetFieldType {
	case "time":
		if cmd.Val() == "" {
			i.LatestOffset = time.Unix(0, 0)
		} else {
			i.LatestOffset, err = time.Parse(i.OffsetTimeFormat, cmd.Val())
			if err != nil {
				logger.Logger.Errorln("mongo input time.Parse error:", err)
				return
			}
		}

	case "int64":
		if cmd.Val() == "" {
			i.LatestOffset = interface{}(0)
		} else {
			i.LatestOffset, err = strconv.ParseInt(cmd.Val(), 10, 64)
			if err != nil {
				logger.Logger.Errorln("mongo input ParseInt error:", err)
				return
			}
		}
	}

	// 增量分批查询，sort -1 代表倒叙
	opts := options.Find().SetLimit(i.Limit).SetSort(bson.M{i.OffsetField: -1})
	// 投影，即你想显示的值，key为 1 代表显示
	if i.Projection != "" && i.Projection != "*" {
		keys := strings.Split(i.Projection, ",")
		if len(keys) > 0 {
			projection := bson.M{}
			for _, v := range keys {
				projection[v] = 1
			}
			opts.SetProjection(projection)
		}
	}

	filter := bson.M{i.OffsetField: bson.M{"$gt": i.LatestOffset}}
	err = i.filterHandler(filter)
	if err != nil {
		return
	}
	collection := i.client.Database(i.DBName).Collection(i.CollectionName)
	cursor, err := collection.Find(context.Background(), filter, opts)
	if err != nil {
		logger.Logger.Errorln(err)
		return
	}
	var results []bson.M
	if cursor != nil {
		if err = cursor.All(context.TODO(), &results); err != nil {
			logger.Logger.Errorln(err)
			return
		}
	}

	for _, v := range results {
		data, err := jsoniter.Marshal(v)
		if err != nil {
			logger.Logger.Errorln("mongo input Marshal error:", err)
			return
		}
		ok, err := i.Codec.Decode(ctx, data, nil, []string{}, msgChan)
		if !ok {
			logger.Logger.Errorf("mongo input decode message to msg chan error : %v", err)
		}
	}
	if len(results) <= 0 {
		return
	}

	i.LatestOffset = results[0][i.OffsetField]

	i.convertRedisKey()

	err = redisCli.Set(offsetKey, i.redisKey, 0)
	if err != nil {
		logger.Logger.Errorln("mongo input redis set  error:", err)
		return
	}

}

func (i *InputConfig) filterHandler(filter bson.M) error {
	if i.Condition != nil {
		for _, v := range i.Condition {
			for key, condition := range v {
				conditionMap, err := parseCondition(condition)
				if err != nil {
					logger.Logger.Errorln("mongo input filterHandler $and  $or  error:", err)
					return err
				}

				switch key {
				case "$and", "$or":
					var andOrFilter []bson.M
					filter[key] = handlerAndOrCondition(conditionMap, andOrFilter)
				case "$in":
					handlerInCondition(conditionMap, filter)
				case "$not":
					handlerNotCondition(conditionMap, filter)
				}
			}
		}
	}

	return nil
}

func parseCondition(condition interface{}) ([]map[string]interface{}, error) {
	var conditionMap []map[string]interface{}
	err := utils.InterfaceToStruct(condition, &conditionMap)
	if err != nil {
		logger.Logger.Errorln("mongo input filterHandler InterfaceToStruct  error:", err)
		return conditionMap, err
	}

	return conditionMap, err
}

func handlerInCondition(conditions []map[string]interface{}, filter bson.M) {
	for _, condition := range conditions {
		variable, ok := condition["variable"].(string)
		if !ok {
			continue
		}
		op, ok := condition["op"].(string)
		if !ok {
			continue
		}
		if op == "=" {
			filter[variable] = condition["value"]
			continue
		}

		filter[variable] = bson.M{op: condition["value"]}
	}
}

func handlerNotCondition(conditions []map[string]interface{}, filter bson.M) {
	for _, condition := range conditions {
		variable, ok := condition["variable"].(string)
		if !ok {
			continue
		}
		op, ok := condition["op"].(string)
		if !ok {
			continue
		}
		if op == "=" {
			filter[variable] = condition["value"]
			continue
		}

		filter[variable] = bson.M{"$not": bson.M{op: condition["value"]}}
	}
}

func handlerAndOrCondition(conditions []map[string]interface{}, filter []bson.M) []bson.M {
	for _, condition := range conditions {
		b := bson.M{}
		variable, ok := condition["variable"].(string)
		if !ok {
			continue
		}
		op, ok := condition["op"].(string)
		if !ok {
			continue
		}
		if op == "=" {
			b[variable] = condition["value"]
			filter = append(filter, b)
			continue
		}

		if ok {
			value, ok := condition["value"].(string)
			if ok {
				timeValue, err := time.ParseInLocation(time.RFC3339, value, time.Local)
				if err != nil {
					logger.Logger.Errorln("mongo input handlerAndOrCondition ParseInLocation  error:", err)
					continue
				}
				b[variable] = bson.M{op: timeValue}
				filter = append(filter, b)
				continue
			}

		}
		b[variable] = bson.M{op: condition["value"]}
		filter = append(filter, b)
	}
	return filter
}

func (i *InputConfig) convertRedisKey() {
	switch t := i.LatestOffset.(type) {
	case time.Time:
		i.redisKey = t.Format(i.OffsetTimeFormat)
	case int64:
		i.redisKey = strconv.FormatInt(t, 10)
	case int32:
		i.redisKey = strconv.FormatInt(int64(t), 10)
	case int:
		i.redisKey = strconv.Itoa(t)
	case uint:
		i.redisKey = strconv.FormatInt(int64(t), 10)
	case uint32:
		i.redisKey = strconv.FormatInt(int64(t), 10)
	case uint64:
		i.redisKey = strconv.FormatInt(int64(t), 10)
	case primitive.DateTime:
		i.redisKey = t.Time().Format(i.OffsetTimeFormat)
	case float64:
		i.redisKey = strconv.FormatFloat(t, 'g', 5, 32)
	case float32:
		i.redisKey = strconv.FormatFloat(float64(t), 'g', 5, 32)
	}
}

func getHost() (string, error) {
	gInnerIP := ""
	addrs, err := net.InterfaceAddrs()
	if err != nil {
		return gInnerIP, err
	}

	for _, address := range addrs {
		if ipnet, ok := address.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
			if ipnet.IP.To4() != nil {
				gInnerIP = ipnet.IP.String()
				break
			}
		}
	}
	return gInnerIP, nil
}
