package outputbeats

import (
	"context"
	"crypto/tls"
	"github.com/elastic/go-lumber/client/v2"
	beatsLog "github.com/elastic/go-lumber/log"
	json "github.com/json-iterator/go"
	"gstash/helper/logger"
	"time"

	"gstash/config"
	"gstash/config/logevent"
)

// ModuleName is the name used in config file
const ModuleName = "beats"

// OutputConfig holds the configuration json fields and internal objects
type OutputConfig struct {
	config.OutputConfig

	// The IP address to connect
	Servers []string `json:"servers"`
	// Buffer channel length
	ChannelSize int `json:"channelSize"`
	// Batch send messages
	Batch int `json:"batch"`
	// flush interval, millisecond
	FlushInterval int `json:"flushInterval"`
	// Enable ssl transport, defaults to false
	SSL bool `json:"ssl"`
	// SSL certificate to use.
	SSLCertificate string `json:"sslCertificate"`
	// SSL key to use.
	SSLKey string `json:"sslKey"`
	// SSL Verify, defaults to false
	SSLVerify bool `json:"sslVerify"`

	msgChan   chan interface{}
	tlsConfig *tls.Config
	clients   []*beatsClient
}

type beatsClient struct {
	client  *v2.SyncClient
	batch   int
	buf     []interface{}
	bufSize int
}

func (bc *beatsClient) start(ctx context.Context, addr string, msgChan <-chan interface{}, batch int, flushInterval int, tlsConfig *tls.Config) {
	bc.buf = make([]interface{}, batch)
	bc.batch = batch
	err := bc.connect(ctx, addr, tlsConfig) //block if not connected
	if err != nil {
		return
	}
	flushTicker := time.NewTicker(time.Millisecond * time.Duration(flushInterval))
	defer flushTicker.Stop()
	for {
		select {
		case msg, ok := <-msgChan: // receive
			if !ok { // channel closed, need send immediately
				err = bc.send()
				if err != nil {
					logger.Logger.Errorf("output beats client send message to server [%s] error [%v], and msgChan is closed, abort", addr, err)
					return
				}
				logger.Logger.Errorf("output beats client [%s] msgChan is closed, return", addr)
				return
			}
			bc.addMsg(msg)
			for bc.isFull() { // need send
				err = bc.send()
				if err != nil { // error, reconnect anyway
					logger.Logger.Errorf("output beats client send message to server %s error %v, try to reconnect...", addr, err)
					err = bc.connect(ctx, addr, tlsConfig) //block if not connected
					if err != nil {
						return
					}
				}
			}
		case <-flushTicker.C:
			_ = bc.send()
		}
	}
}

func (bc *beatsClient) send() error {
	if bc.bufSize == 0 {
		return nil
	}
	seq, err := bc.client.Send(bc.buf[:bc.bufSize])
	if seq > 0 && seq < bc.bufSize { // not finished，refresh the buffer
		copy(bc.buf, bc.buf[seq:bc.bufSize])
	}
	bc.bufSize -= seq
	return err
}

func (bc *beatsClient) addMsg(msg interface{}) {
	bc.buf[bc.bufSize] = msg // store
	bc.bufSize++
}

func (bc *beatsClient) isFull() bool {
	return bc.bufSize == bc.batch
}

func (bc *beatsClient) connect(ctx context.Context, addr string, tlsConfig *tls.Config) (err error) {
	if bc.client != nil {
		_ = bc.client.Close()
	}
	err = bc.dial(addr, tlsConfig)
	if err != nil {
		ticker := time.NewTicker(time.Second * 5)
		defer ticker.Stop()
		for err != nil {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				logger.Logger.Errorf("output beats client connect server [%s] error [%v], try again...", addr, err)
				err = bc.dial(addr, tlsConfig)
			}
		}
	}
	logger.Logger.Infof("output beats client connect server [%s] success", addr)
	return nil
}

func (bc *beatsClient) dial(addr string, tlsConfig *tls.Config) (err error) {
	if tlsConfig == nil {
		bc.client, err = v2.SyncDial(addr, v2.JSONEncoder(json.Marshal))
	} else {
		dialer := &tls.Dialer{Config: tlsConfig}
		bc.client, err = v2.SyncDialWith(dialer.Dial, addr, v2.JSONEncoder(json.Marshal))
	}
	return err
}

// DefaultOutputConfig returns an OutputConfig struct with default values
func DefaultOutputConfig() OutputConfig {
	return OutputConfig{
		OutputConfig: config.OutputConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
		Servers:       []string{"127.0.0.1:5044"},
		FlushInterval: 1000,
		ChannelSize:   100,
		Batch:         20,
	}
}

// InitHandler initialize the output plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeOutputConfig, error) {
	conf := DefaultOutputConfig()
	if err := config.ReflectConfig(raw, &conf); err != nil {
		return nil, err
	}
	if conf.FlushInterval <= 0 {
		conf.FlushInterval = 1000
	}
	if conf.ChannelSize < 0 {
		conf.ChannelSize = 100
	}
	if conf.Batch < 0 {
		conf.Batch = 20
	}

	if !conf.SSL {
		if conf.SSLCertificate != "" {
			logger.Logger.Warnln("beats input: SSL Certificate will not be used")
		}
		if conf.SSLKey != "" {
			logger.Logger.Warnln("beats input: SSL Key will not be used")
		}
	} else {
		// SSL enabled
		cer, err := tls.LoadX509KeyPair(conf.SSLCertificate, conf.SSLKey)
		if err != nil {
			return nil, err
		}
		conf.tlsConfig = &tls.Config{Certificates: []tls.Certificate{cer}}
		if !conf.SSLVerify {
			conf.tlsConfig.InsecureSkipVerify = true
		}
	}
	//set logger
	beatsLog.Logger = logger.Logger

	conf.msgChan = make(chan interface{}, conf.ChannelSize)

	for _, addr := range conf.Servers {
		bc := &beatsClient{}
		go bc.start(ctx, addr, conf.msgChan, conf.Batch, conf.FlushInterval, conf.tlsConfig)
		conf.clients = append(conf.clients, bc)
	}

	go func() {
		defer close(conf.msgChan)
		<-ctx.Done()
		for len(conf.msgChan) > 0 {
			time.Sleep(time.Second * 1) //等待1秒
		}
	}()

	return &conf, nil
}

// Output event
func (t *OutputConfig) Output(ctx context.Context, event logevent.LogEvent) (err error) {
	m := event.GetJSONMap()
	t.msgChan <- m
	return nil
}
