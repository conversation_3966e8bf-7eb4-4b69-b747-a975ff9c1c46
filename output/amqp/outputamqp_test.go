package outputamqp

import (
	"context"
	"strings"
	"testing"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	errutil "github.com/tmsong/utils/error"
	"gstash/config"
	"gstash/helper/logger"

	"gstash/config/logevent"
)

func init() {
	logger.Logger.SetLevel(logrus.DebugLevel)
	config.RegistOutputHandler(ModuleName, InitHandler)
}

func Test_output_amqp_module(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	ctx := context.Background()
	conf, err := config.LoadFromYAML([]byte(strings.TrimSpace(`
debugch: true
output:
  - type: amqp
    urls: ["amqp://guest:guest@localhost:5672/"]
    exchange: "amq.topic"
    exchange_type: "topic"
	`)))
	require.NoError(err)
	err = conf.Worker[0].Start(ctx)
	if err != nil {
		require.True(config.ErrorInitOutputFailed1.Match(err))
		require.True(ErrorNoValidConn.In(err))
		require.Implements((*errutil.ErrorObject)(nil), err)
		require.True(ErrorNoValidConn.Match(err.(errutil.ErrorObject).Parent()))
		t.Skip("skip test output amqp module")
	}

	conf.Worker[0].TestInputEvent(logevent.LogEvent{
		Timestamp: time.Now(),
		Message:   "outputstdout test message",
		Extra: map[string]interface{}{
			"fieldstring": "ABC",
			"fieldnumber": 123,
		},
	})

	if event, err := conf.Worker[0].TestGetOutputEvent(300 * time.Millisecond); assert.NoError(err) {
		t.Log(event)
	}
}
