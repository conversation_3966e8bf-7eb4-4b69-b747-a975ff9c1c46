/**
 * @note
 * outputelastic_v8
 *
 * <AUTHOR>
 * @date 	2025-01-17
 */
package outputelastic

import (
	"bytes"
	"context"
	"io"

	"github.com/elastic/go-elasticsearch/v8/esutil"
	json "github.com/json-iterator/go"
	"gstash/config/logevent"
	"gstash/helper/logger"
)

func (t *OutputConfig) OutputV8(ctx context.Context, index string, event logevent.LogEvent) (err error) {
	var dataBytes []byte
	if t.Action == "update" { // 更新时处理成upsert逻辑
		docMap := event.GetJSONMap()
		upsertBody := map[string]interface{}{
			"doc":           docMap,
			"doc_as_upsert": true,
		}
		dataBytes, err = json.Marshal(upsertBody)
	} else {
		dataBytes, err = event.MarshalJSON()
	}
	if err != nil {
		return err
	}
	id := event.Format(t.DocumentID)
	indexRequest := esutil.BulkIndexerItem{
		Action:     t.Action,
		Index:      index,
		DocumentID: id,
		Body:       bytes.NewReader(dataBytes),
		OnFailure:  RecordFailure,
	}

	return t.v8Processor.Add(ctx, indexRequest)
}

func RecordFailure(ctx context.Context, item esutil.BulkIndexerItem, itemResp esutil.BulkIndexerResponseItem, err error) {
	reqBody, _ := io.ReadAll(item.Body)
	if err != nil {
		logger.Logger.Errorf("%s: bulk processor request %s failed: %s", ModuleName, string(reqBody), err.Error())
	} else {
		errString, _ := json.MarshalToString(itemResp.Error)
		logger.Logger.Errorf("%s: bulk processor request %s failed: %s", ModuleName, string(reqBody), errString)
	}
}
