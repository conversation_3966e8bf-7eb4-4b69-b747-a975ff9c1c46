/**
 * @note
 * logger
 *
 * <AUTHOR>
 * @date 	2025-01-17
 */
package outputelastic

import (
	"errors"
	"io"
	"net/http"
	"time"

	"github.com/sirupsen/logrus"

	"gstash/helper/logger"
)

type esCustomLogger struct {
	conf   *OutputConfig
	logger logrus.FieldLogger
}

// Printf log format string to error level
func (l *esCustomLogger) Printf(format string, args ...interface{}) {
	l.logger.Errorf(format, args...)
}

// LogRoundTrip should not modify the request or response, except for consuming and closing the body.
// Implementations have to check for nil values in request and response.
func (l *esCustomLogger) LogRoundTrip(req *http.Request, resp *http.Response, err error, time time.Time, cost time.Duration) error {
	if err == nil { // 无错误就不打日志了
		return nil
	}
	var statusCode int
	var reqUrl string
	var reqBody, respBody []byte
	var reqBodyErr, respBodyErr error
	if req != nil {
		reqUrl = req.URL.String()
		if req.Body != nil {
			reqBody, reqBodyErr = io.ReadAll(req.Body)
			if reqBodyErr != nil {
				return errors.New("error reading request body: " + reqBodyErr.Error())
			}
		}
	}
	if resp != nil {
		statusCode = resp.StatusCode
		if resp.Body != nil {
			respBody, respBodyErr = io.ReadAll(resp.Body)
			if respBodyErr != nil {
				return errors.New("error reading response body: " + respBodyErr.Error())
			}
		}
	}
	logger.Logger.Errorf("%s: index %s | req url: %s | req body: %s | resp code: %d | resp body: %s | error: %s | cost: %v",
		ModuleName, l.conf.Index, reqUrl, string(reqBody), statusCode, string(respBody), err.Error(), cost)
	return nil
}

// RequestBodyEnabled makes the client pass a copy of request body to the logger.
func (l *esCustomLogger) RequestBodyEnabled() bool {
	return true
}

// ResponseBodyEnabled makes the client pass a copy of response body to the logger.
func (l *esCustomLogger) ResponseBodyEnabled() bool {
	return true
}
