/**
 * @note
 * outputelastic_v7
 *
 * <AUTHOR>
 * @date 	2025-01-20
 */
package outputelastic

import (
	"context"

	esv7 "github.com/olivere/elastic/v7"
	
	"gstash/config/logevent"
	"gstash/helper/logger"
)

// Output event
func (t *OutputConfig) OutputV7(ctx context.Context, index string, event logevent.LogEvent) (err error) {

	id := event.Format(t.DocumentID)

	indexRequest := esv7.NewBulkIndexRequest().
		Index(index).
		RetryOnConflict(t.RetryOnConflict).
		Id(id).
		Doc(event)
	t.v7Processor.Add(indexRequest)

	return
}

// BulkAfter execute after a commit to Elasticsearch
func (t *OutputConfig) BulkAfterV7(executionID int64, requests []esv7.BulkableRequest, response *esv7.BulkResponse, err error) {
	if err == nil && response.Errors {
		// find failed requests, and log it
		for i, item := range response.Items {
			for _, v := range item {
				if v.Error != nil {
					logger.Logger.Errorf("%s: bulk processor request %s failed: %s", ModuleName, requests[i].String(), v.Error.Reason)
				}
			}
		}
	}
}
