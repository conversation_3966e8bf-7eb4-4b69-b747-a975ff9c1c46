package outputelastic

import (
	"context"
	"crypto/tls"
	"net/http"
	"strconv"
	"strings"
	"time"

	esv8 "github.com/elastic/go-elasticsearch/v8"
	"github.com/elastic/go-elasticsearch/v8/esutil"
	jsoniter "github.com/json-iterator/go"
	esv6 "github.com/olivere/elastic/v6"
	esv7 "github.com/olivere/elastic/v7"
	errutil "github.com/tmsong/utils/error"

	"gstash/config"
	"gstash/config/logevent"
	"gstash/helper/logger"
)

// ModuleName is the name used in config file
const ModuleName = "elastic"

// OutputConfig holds the configuration json fields and internal objects
type OutputConfig struct {
	config.OutputConfig
	Version         int      `json:"version"` //version 6 or 7 or 8, default 8
	URL             []string `json:"url"`     // elastic API entrypoints
	resolvedURLs    []string // URLs after resolving environment vars
	Index           string   `json:"index"`           // index name to log
	Action          string   `json:"action"`          // index(not available in data stream mode), create, update, delete
	Suffix          string   `json:"suffix"`          // index suffix, year: xxx-2012, month: xxx-2012.03, day: xxx-2012.03.04
	SuffixInterval  int      `json:"suffixInterval"`  // index suffix interval
	DocumentType    string   `json:"documentType"`    // type name to log
	DocumentID      string   `json:"documentId"`      // id to log, used if you want to control id format
	RetryOnConflict int      `json:"retryOnConflict"` // the number of times Elasticsearch should internally retry an update/upserted document

	Sniff bool `json:"sniff"` // find all nodes of your cluster, https://github.com/olivere/elastic/wiki/Sniffing

	// BulkActions specifies when to flush based on the number of actions
	// currently added. Defaults to 1000 and can be set to -1 to be disabled.
	BulkActions int `json:"bulkActions,omitempty"`

	// BulkSize specifies when to flush based on the size (in bytes) of the actions
	// currently added. Defaults to 5 MB and can be set to -1 to be disabled.
	BulkSize int `json:"bulkSize,omitempty"`

	// BulkFlushInterval specifies when to flush at the end of the given interval.
	// Defaults to 30 seconds. If you want the bulk processor to
	// operate completely asynchronously, set both BulkActions and BulkSize to
	// -1 and set the FlushInterval to a meaningful interval.
	BulkFlushInterval time.Duration `json:"bulkFlushInterval"`

	// ExponentialBackoffInitialTimeout used to set the first/minimal interval in elastic.ExponentialBackoff
	// Defaults to 10s
	ExponentialBackoffInitialTimeout string `json:"exponentialBackoffInitialTimeout,omitempty"`
	exponentialBackoffInitialTimeout time.Duration

	// ExponentialBackoffMaxTimeout used to set the maximum wait interval in elastic.ExponentialBackoff
	// Defaults to 5m
	ExponentialBackoffMaxTimeout string `json:"exponentialBackoffMaxTimeout,omitempty"`
	exponentialBackoffMaxTimeout time.Duration

	// SSLCertValidation Option to validate the server's certificate. Disabling this severely compromises security.
	// For more information on disabling certificate verification please read https://www.cs.utexas.edu/~shmat/shmat_ccs12.pdf
	SSLCertValidation bool   `json:"sslCertificateValidation,omitempty"`
	User              string `json:"user,omitempty"`     //basic auth user
	Password          string `json:"password,omitempty"` //basic auth password

	v6Client    *esv6.Client        // elastic client instance v6
	v6Processor *esv6.BulkProcessor // elastic bulk processor v6

	v7Client    *esv7.Client        // elastic client instance v7
	v7Processor *esv7.BulkProcessor // elastic bulk processor v7

	v8Client    *esv8.Client       // elastic client instance v8
	v8Processor esutil.BulkIndexer // elastic bulk indexer v8

	OutputFunc func(ctx context.Context, index string, event logevent.LogEvent) (err error)
}

// DefaultOutputConfig returns an OutputConfig struct with default values
func DefaultOutputConfig() OutputConfig {
	return OutputConfig{
		OutputConfig: config.OutputConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
		Action:                           "create",
		RetryOnConflict:                  1,
		BulkActions:                      1000,    // 1000 actions
		BulkSize:                         5 << 20, // 5 MB
		BulkFlushInterval:                30 * time.Second,
		ExponentialBackoffInitialTimeout: "10s",
		ExponentialBackoffMaxTimeout:     "5m",
		SSLCertValidation:                true,
	}
}

// errors
var (
	ErrorCreateClientFailed1 = errutil.NewFactory("create elastic client failed: %q")
)

type jsonDecoder struct{}

// Decode decodes with jsoniter.Unmarshal
func (u *jsonDecoder) Decode(data []byte, v interface{}) error {
	return jsoniter.Unmarshal(data, v)
}

// InitHandler initialize the output plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeOutputConfig, error) {
	conf := DefaultOutputConfig()
	err := config.ReflectConfig(raw, &conf)
	if err != nil {
		return nil, err
	}

	// map Printf to error level
	esLogger := &esCustomLogger{logger: logger.Logger}

	// replace env var names with values on URL config
	for _, url := range conf.URL {
		newURL := logevent.FormatWithEnv(url)
		conf.resolvedURLs = append(conf.resolvedURLs, newURL)
	}

	conf.exponentialBackoffInitialTimeout, err = time.ParseDuration(conf.ExponentialBackoffInitialTimeout)
	if err != nil {
		return nil, err
	}

	conf.exponentialBackoffMaxTimeout, err = time.ParseDuration(conf.ExponentialBackoffMaxTimeout)
	if err != nil {
		return nil, err
	}

	// set httpclient explicitly if we need to avoid https cert checks
	var httpClient *http.Client
	var transport http.RoundTripper
	if !conf.SSLCertValidation {
		transport = &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		}
		httpClient = &http.Client{Transport: transport}
	}

	newCtx := context.Background()

	if conf.Version == 6 {
		options := []esv6.ClientOptionFunc{
			esv6.SetURL(conf.resolvedURLs...),
			esv6.SetSniff(conf.Sniff),
			esv6.SetErrorLog(esLogger),
			esv6.SetDecoder(&jsonDecoder{}),
		}

		if httpClient != nil {
			options = append(options, esv6.SetHttpClient(httpClient))
		}

		if conf.User != "" && conf.Password != "" {
			options = append(options, esv6.SetBasicAuth(conf.User, conf.Password))
		}

		if conf.v6Client, err = esv6.NewClient(options...); err != nil {
			return nil, ErrorCreateClientFailed1.New(err, conf.URL)
		}

		conf.v6Processor, err = conf.v6Client.BulkProcessor().
			Name("gstash-output-elastic").
			BulkActions(conf.BulkActions).
			BulkSize(conf.BulkSize).
			FlushInterval(conf.BulkFlushInterval).
			Backoff(esv6.NewExponentialBackoff(conf.exponentialBackoffInitialTimeout, conf.exponentialBackoffMaxTimeout)).
			After(conf.BulkAfterV6).
			Do(newCtx)

		conf.OutputFunc = conf.OutputV6
	} else if conf.Version == 7 {
		options := []esv7.ClientOptionFunc{
			esv7.SetURL(conf.resolvedURLs...),
			esv7.SetSniff(conf.Sniff),
			esv7.SetErrorLog(esLogger),
			esv7.SetDecoder(&jsonDecoder{}),
		}

		if httpClient != nil {
			options = append(options, esv7.SetHttpClient(httpClient))
		}

		if conf.User != "" && conf.Password != "" {
			options = append(options, esv7.SetBasicAuth(conf.User, conf.Password))
		}

		if conf.v7Client, err = esv7.NewClient(options...); err != nil {
			return nil, ErrorCreateClientFailed1.New(err, conf.URL)
		}

		conf.v7Processor, err = conf.v7Client.BulkProcessor().
			Name("gstash-output-elastic").
			BulkActions(conf.BulkActions).
			BulkSize(conf.BulkSize).
			FlushInterval(conf.BulkFlushInterval).
			Backoff(esv7.NewExponentialBackoff(conf.exponentialBackoffInitialTimeout, conf.exponentialBackoffMaxTimeout)).
			After(conf.BulkAfterV7).
			Do(newCtx)

		conf.OutputFunc = conf.OutputV7
	} else {
		confV8 := esv8.Config{
			Addresses: conf.resolvedURLs,
			Logger:    esLogger,
		}
		if transport != nil {
			confV8.Transport = transport
		}
		if conf.User != "" && conf.Password != "" {
			confV8.Username = conf.User
			confV8.Password = conf.Password
		}
		if conf.v8Client, err = esv8.NewClient(confV8); err != nil {
			return nil, ErrorCreateClientFailed1.New(err, conf.URL)
		}
		conf.v8Client.Bulk.WithIndex(conf.Index)
		conf.v8Processor, err = esutil.NewBulkIndexer(esutil.BulkIndexerConfig{
			Index:         conf.Index,             // The default index name
			Client:        conf.v8Client,          // The Elasticsearch client
			NumWorkers:    3,                      // The number of worker goroutines
			FlushBytes:    conf.BulkSize,          // The flush threshold in bytes
			FlushInterval: conf.BulkFlushInterval, // The periodic flush interval
		})
		conf.OutputFunc = conf.OutputV8
	}

	if err != nil {
		return nil, err
	}

	return &conf, nil

}

func (t *OutputConfig) BulkAfterV6(executionID int64, requests []esv6.BulkableRequest, response *esv6.BulkResponse, err error) {
	if err == nil && response.Errors {
		// find failed requests, and log it
		for i, item := range response.Items {
			for _, v := range item {
				if v.Error != nil {
					logger.Logger.Errorf("%s: bulk processor request %s failed: %s", ModuleName, requests[i].String(), v.Error.Reason)
				}
			}
		}
	}
}

// Output event
func (t *OutputConfig) Output(ctx context.Context, event logevent.LogEvent) (err error) {
	// elastic index name should be lowercase
	index := strings.ToLower(event.Format(t.Index))
	if t.Suffix != "" {
		index += getSuffix(event.Timestamp, t.Suffix, t.SuffixInterval)
	}

	return t.OutputFunc(ctx, index, event)
}

// Output event
func (t *OutputConfig) OutputV6(ctx context.Context, index string, event logevent.LogEvent) (err error) {

	id := event.Format(t.DocumentID)

	indexRequest := esv6.NewBulkIndexRequest().
		Index(index).
		Type(t.DocumentType).
		RetryOnConflict(t.RetryOnConflict).
		Id(id).
		Doc(event)
	t.v6Processor.Add(indexRequest)

	return
}

func getSuffix(t time.Time, suffix string, suffixInterval int) string {
	if suffixInterval < 1 {
		return ""
	}
	y, m, d := t.Date()
	if suffix == "year" {
		if suffixInterval > 1 {
			y = ((y-1)/suffixInterval)*suffixInterval + 1
		}
		yStr := strconv.Itoa(y)
		return "-" + yStr
	} else if suffix == "month" {
		if suffixInterval > 1 {
			m = time.Month(((int(m)-1)/suffixInterval)*suffixInterval + 1)
		}
		yStr := strconv.Itoa(y)
		mStr := strconv.Itoa(int(m))
		if len(mStr) == 1 {
			mStr = "0" + mStr
		}
		return "-" + yStr + "." + mStr
	} else if suffix == "day" {
		if suffixInterval > 1 {
			d = ((d-1)/suffixInterval)*suffixInterval + 1
		}
		yStr := strconv.Itoa(y)
		mStr := strconv.Itoa(int(m))
		dStr := strconv.Itoa(d)
		if len(mStr) == 1 {
			mStr = "0" + mStr
		}
		if len(dStr) == 1 {
			dStr = "0" + dStr
		}
		return "-" + yStr + "." + mStr + "." + dStr
	} else {
		return ""
	}
}
