package outputkafka

import (
	"context"
	"github.com/IBM/sarama"
	"gstash/config"
	"gstash/config/logevent"
	"gstash/helper/logger"
	"strings"
)

// ModuleName is the name used in config file
const ModuleName = "kafka"

// OutputConfig holds the configuration json fields and internal objects
type OutputConfig struct {
	config.OutputConfig
	Version          string   `json:"version"`                    // Kafka cluster version, eg: 0.10.2.0
	Brokers          []string `json:"brokers"`                    // Kafka bootstrap brokers to connect to, as a comma separated list
	Topics           []string `json:"topics"`                     // Kafka topics to be consumed, as a comma seperated list
	ClientId         string   `json:"clientId"`                   // Kafka client id
	SecurityProtocol string   `json:"securityProtocol,omitempty"` // use SASL authentication
	Compression      string   `json:"compression,omitempty"`      // use compression
	Partitioner      string   `json:"partitioner,omitempty"`      // custom partitioner
	User             string   `json:"saslUsername,omitempty"`     // SASL authentication username
	Password         string   `json:"saslPassword,omitempty"`     // SASL authentication password

	client sarama.AsyncProducer
}

// DefaultOutputConfig returns an OutputConfig struct with default values
func DefaultOutputConfig() OutputConfig {
	return OutputConfig{
		OutputConfig: config.OutputConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
		SecurityProtocol: "",
		Compression:      "",
		User:             "",
		Password:         "",
	}
}

// InitHandler initialize the output plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeOutputConfig, error) {
	conf := DefaultOutputConfig()
	if err := config.ReflectConfig(raw, &conf); err != nil {
		return nil, err
	}

	sarama.Logger = logger.Logger

	version, err := sarama.ParseKafkaVersion(conf.Version)
	if err != nil {
		logger.Logger.Errorf("Error parsing Kafka version: %v", err)
		return nil, err
	}

	/**
	 * Construct a new Sarama configuration.
	 * The Kafka cluster version has to be defined before the consumer/producer is initialized.
	 */
	sarConfig := sarama.NewConfig()
	sarConfig.Version = version
	sarConfig.ClientID = "gstash-sarama"

	if conf.ClientId != "" {
		sarConfig.ClientID = conf.ClientId
	}
	if len(conf.Topics) < 0 {
		logger.Logger.Errorln("topics should not be empty")
		return nil, err
	}

	if len(conf.Brokers) == 0 {
		logger.Logger.Errorln("topics should not be empty")
		return nil, err
	}

	if conf.SecurityProtocol == "SASL" {
		sarConfig.Net.SASL.Enable = true
		sarConfig.Net.SASL.User = conf.User
		sarConfig.Net.SASL.Password = conf.Password
	}

	if conf.Partitioner != "" {
		switch strings.ToLower(conf.Partitioner) {
		case "hash":
			sarConfig.Producer.Partitioner = sarama.NewHashPartitioner
		case "referencehash":
			sarConfig.Producer.Partitioner = sarama.NewReferenceHashPartitioner
		case "random":
			sarConfig.Producer.Partitioner = sarama.NewRandomPartitioner
		case "roundrobin":
			sarConfig.Producer.Partitioner = sarama.NewRoundRobinPartitioner
		}
	}

	if conf.Compression != "" {
		switch strings.ToLower(conf.Compression) {
		case "lz4":
			sarConfig.Producer.Compression = sarama.CompressionLZ4
		case "gzip":
			sarConfig.Producer.Compression = sarama.CompressionGZIP
		case "snappy":
			sarConfig.Producer.Compression = sarama.CompressionSnappy
		case "zstd":
			sarConfig.Producer.Compression = sarama.CompressionZSTD
		}
	}

	conf.client, err = sarama.NewAsyncProducer(conf.Brokers, sarConfig)
	if err != nil {
		logger.Logger.Errorf("Error creating producer client: %v", err)
		return nil, err
	}

	return &conf, nil
}

// Output event
func (t *OutputConfig) Output(ctx context.Context, event logevent.LogEvent) (err error) {

	raw, err := event.MarshalJSON()
	if err != nil {
		return err
	}

	ch := t.client.Input()
	for _, topic := range t.Topics {
		msg := &sarama.ProducerMessage{Topic: topic, Value: sarama.ByteEncoder(raw)}
		ch <- msg
	}

	return nil
}
