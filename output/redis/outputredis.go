package outputredis

import (
	"context"
	"time"

	"github.com/go-redis/redis/v8"
	errutil "github.com/tmsong/utils/error"
	"gstash/config"
	"gstash/helper/logger"

	"gstash/config/logevent"
)

// ModuleName is the name used in config file
const ModuleName = "redis"

// ErrorTag tag added to event when process module failed
const ErrorTag = "gstash_output_redis_error"

// OutputConfig holds the configuration json fields and internal objects
type OutputConfig struct {
	config.OutputConfig
	Host          []string `json:"host"`
	Key           string   `json:"key"`
	DataType      string   `json:"dataType,omitempty"` // one of ["list", "channel"]
	Timeout       int      `json:"timeout,omitempty"`
	RetryInterval int      `json:"retryInterval,omitempty"` // maximum seconds of retry, default: 1
	RetryTimes    int      `json:"retryTimes,omitempty"`    // maximum number of retry, default: 2

	Connections int `json:"connections"` // maximum number of socket connections, default: 10

	client *redis.Client
}

// DefaultOutputConfig returns an OutputConfig struct with default values
func DefaultOutputConfig() OutputConfig {
	return OutputConfig{
		OutputConfig: config.OutputConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
		Host:          []string{"localhost:6379"},
		Key:           "gstash",
		DataType:      "list",
		Timeout:       5,
		RetryInterval: 1,
		RetryTimes:    2,
		Connections:   10,
	}
}

// errors
var (
	ErrorPingFailed           = errutil.NewFactory("ping redis server failed")
	ErrorEventMarshalFailed1  = errutil.NewFactory("event Marshal failed: %v")
	ErrorUnsupportedDataType1 = errutil.NewFactory("unsupported data type: %q")
)

// InitHandler initialize the output plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeOutputConfig, error) {
	conf := DefaultOutputConfig()
	err := config.ReflectConfig(raw, &conf)
	if err != nil {
		return nil, err
	}

	if len(conf.Host) > 1 {
		logger.Logger.Warnln("deprecated: host number should be only 1")
	}

	conf.client = redis.NewClient(&redis.Options{
		Addr:        conf.Host[0],
		PoolSize:    conf.Connections,
		DialTimeout: time.Duration(conf.Timeout) * time.Second,
	})

	if _, err = conf.client.Ping(ctx).Result(); err != nil {
		return nil, ErrorPingFailed.New(err)
	}

	return &conf, nil
}

// Output event
func (t *OutputConfig) Output(ctx context.Context, event logevent.LogEvent) (err error) {
	raw, err := event.MarshalJSON()
	if err != nil {
		return ErrorEventMarshalFailed1.New(err, event)
	}

	key := event.Format(t.Key)

	// try to log forever
	retryTimes := t.RetryTimes
	for {
		select {
		case <-ctx.Done():
			return nil
		default:
		}

		switch t.DataType {
		case "list":
			if _, err = t.client.RPush(ctx, key, raw).Result(); err == nil {
				return
			}
		case "channel":
			if _, err = t.client.Publish(ctx, key, string(raw)).Result(); err == nil {
				return
			}
		default:
			return ErrorUnsupportedDataType1.New(nil, t.DataType)
		}

		select {
		case <-ctx.Done():
			return err
		case <-time.After(time.Duration(t.RetryInterval) * time.Second):
			if retryTimes <= 0 {
				return err
			}
			retryTimes--
		}
	}
}
