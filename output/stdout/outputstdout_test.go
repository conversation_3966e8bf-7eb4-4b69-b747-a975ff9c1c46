package outputstdout

import (
	"context"
	"strings"
	"testing"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gstash/config"
	"gstash/helper/logger"

	"gstash/config/logevent"
)

func init() {
	logger.Logger.SetLevel(logrus.DebugLevel)
	config.RegistOutputHandler(ModuleName, InitHandler)
}

func Test_output_stdout_module(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	ctx := context.Background()
	conf, err := config.LoadFromYAML([]byte(strings.TrimSpace(`
debugch: true
output:
  - type: stdout
	`)))
	require.NoError(err)
	require.NoError(conf.Worker[0].Start(ctx))

	conf.Worker[0].TestInputEvent(logevent.LogEvent{
		Timestamp: time.Now(),
		Message:   "outputstdout test message",
	})

	if event, err := conf.Worker[0].TestGetOutputEvent(300 * time.Millisecond); assert.NoError(err) {
		require.Equal("outputstdout test message", event.Message)
	}
}
