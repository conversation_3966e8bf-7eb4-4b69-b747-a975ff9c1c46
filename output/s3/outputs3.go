package outputs3

import (
	"context"
	"io"
	"sync"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	awsConf "github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	errutil "github.com/tmsong/utils/error"

	"gstash/config"
	"gstash/config/logevent"
	"gstash/helper/logger"
)

const (
	// ModuleName is the name used in config file
	ModuleName         = "s3"
	msgChanConcurrency = 100
	reqChanConcurrency = 10
	heartbeatInterval  = 60 // 60秒没写入，写一个空字符进去
	defaultCodec       = "%{message}"
	defaultMaxLines    = 10000
	defaultMaxSize     = 10 * 1024 * 1024 // 10MB
)

// errors
var (
	ErrorNoPath     = errutil.NewFactory("no path defined for output s3 cache file")
	ErrorNoS3Bucket = errutil.NewFactory("no s3 bucket defined for output s3")
)

// OutputConfig holds the configuration json fields and internal objects
type OutputConfig struct {
	config.OutputConfig
	// 本地缓存文件配置
	FilePath      string `json:"filePath"`       // S3上文件的path，支持%{xxx}取event内字段
	FlushInterval int    `json:"flushInterval"`  // 刷新间隔（秒），0表示每条消息都刷新
	Codec         string `json:"codec"`          // 写入文件的表达式，例如: "%{log}"，如果为空则会写入整个事件
	MaxLines      int    `json:"maxLines"`       // 每个缓存文件最大行数，达到后上传
	MaxSize       int    `json:"maxSize"`        // 每个缓存文件最大大小（字节），达到后上传
	ReqConcurrent int    `json:"reqConcurrency"` // s3请求并发度

	// AWS S3配置
	Region          string `json:"region"`          // AWS区域
	Bucket          string `json:"bucket"`          // S3存储桶名称
	AccessKeyID     string `json:"accessKeyID"`     // AWS访问密钥ID
	SecretAccessKey string `json:"secretAccessKey"` // AWS秘密访问密钥
	RetryTimes      int    `json:"retryTimes"`      // 上传失败重试次数
	RetryInterval   int    `json:"retryInterval"`   // 重试间隔（秒）

	// 内部使用

	msgChan  chan logevent.LogEvent
	reqChan  chan *uploadRequest
	s3Client *s3.Client

	errGroup sync.WaitGroup
}

type uploadRequest struct {
	objectKey string
	file      *io.PipeReader
}

// fileWriter 管理单个缓存文件的写入和上传
// DefaultOutputConfig returns an OutputConfig struct with default values
func DefaultOutputConfig() OutputConfig {
	return OutputConfig{
		OutputConfig: config.OutputConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
		Codec:    defaultCodec,
		MaxLines: defaultMaxLines,
		MaxSize:  defaultMaxSize,
	}
}

// InitHandler initialize the output plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeOutputConfig, error) {
	conf := DefaultOutputConfig()
	err := config.ReflectConfig(raw, &conf)
	if err != nil {
		return nil, err
	}

	// 验证必要的配置
	if conf.FilePath == "" {
		return nil, ErrorNoPath.New(nil)
	}
	if conf.Bucket == "" {
		return nil, ErrorNoS3Bucket.New(nil)
	}

	var awsConfig aws.Config

	if conf.AccessKeyID != "" && conf.SecretAccessKey != "" { // 指定了aksk
		awsConfig = aws.Config{
			Region: conf.Region,
		}
		awsConfig.Credentials = credentials.NewStaticCredentialsProvider(conf.AccessKeyID, conf.SecretAccessKey, "")
	} else {
		awsConfig, _ = awsConf.LoadDefaultConfig(context.TODO(),
			awsConf.WithRegion(conf.Region),
		)
	}

	conf.s3Client = s3.NewFromConfig(awsConfig)

	conf.msgChan = make(chan logevent.LogEvent, msgChanConcurrency)
	conf.reqChan = make(chan *uploadRequest, reqChanConcurrency)

	go func() {
		defer close(conf.msgChan)
		defer close(conf.reqChan)
		<-ctx.Done()
		for len(conf.msgChan) > 0 {
			time.Sleep(time.Second * 1) //等待1秒
		}
		for len(conf.reqChan) > 0 {
			time.Sleep(time.Second * 1) //等待1秒
		}
	}()

	go conf.start(ctx)

	for i := 0; i < conf.ReqConcurrent; i++ {
		go conf.uploadToS3(ctx)
	}

	return &conf, nil
}

// uploadToS3 上传文件到S3
func (t *OutputConfig) uploadToS3(ctx context.Context) {
	for req := range t.reqChan {
		_, err := t.s3Client.PutObject(ctx, &s3.PutObjectInput{
			Bucket: aws.String(t.Bucket),
			Key:    aws.String(req.objectKey),
			Body:   req.file,
		})
		if err != nil {
			logger.Logger.Errorf("error uploading object %s to s3 bucket %s: %v", req.objectKey, t.Bucket, err)
		}
		// TODO 失败重试
	}
}

func (t *OutputConfig) start(ctx context.Context) {
	var pReader *io.PipeReader
	var pWriter *io.PipeWriter
	var lines, size int // 已经写入的line和size
	var objectKey string
	var err error
	lastWriteTime := time.Now().Unix()
	defer func() {
		if pWriter != nil {
			_ = pWriter.Close()
		}
	}()
	go func() {
		heartbeatTicker := time.NewTicker(time.Second)
		defer heartbeatTicker.Stop()
		for {
			select {
			case <-heartbeatTicker.C:
				if time.Now().Unix()-lastWriteTime > heartbeatInterval {
					_, err := pWriter.Write([]byte(" "))
					if err != nil {
						logger.Logger.Errorf("error writing heartbeat to pipeWriter for object %s of s3 bucket %s: %v", objectKey, t.Bucket, err)
					}
					lastWriteTime = time.Now().Unix()
				}
			case <-ctx.Done():
				return
			}
		}
	}()
	for event := range t.msgChan {
		// writer为空，需要新建一个文件
		if pWriter == nil {
			pReader, pWriter = io.Pipe()
			lines, size = 0, 0 // 重置写入值
			objectKey = event.Format(t.FilePath)
			t.reqChan <- &uploadRequest{
				objectKey: objectKey,
				file:      pReader,
			}
		}
		// 格式化消息
		msg := event.Format(t.Codec)
		var msgBytes []byte
		if len(msg) == 0 {
			msgBytes, err = event.MarshalJSON()
			if err != nil {
				logger.Logger.Errorf("s3 output marshal event to json error: %v", err)
				continue
			}
			msgBytes = append(msgBytes, '\n') // 增加换行符
		} else {
			msgBytes = []byte(msg + "\n")
		}
		// 写入文件
		n, err := pWriter.Write(msgBytes)
		lastWriteTime = time.Now().Unix()
		if err != nil {
			_ = pWriter.CloseWithError(err)
			pWriter = nil
			logger.Logger.Errorf("error writing to pipeWriter for object %s of s3 bucket %s: %v", objectKey, t.Bucket, err)
			lines, size = 0, 0 // 产生错误了，关闭文件，重置
		}
		// 统计写入量
		lines++
		size += n
		if lines >= t.MaxLines || size >= t.MaxSize {
			_ = pWriter.Close()
			pWriter = nil
			lines, size = 0, 0
		}
	}
}

// Output event
func (t *OutputConfig) Output(ctx context.Context, event logevent.LogEvent) (err error) {
	t.msgChan <- event
	// 写入事件
	return nil
}
