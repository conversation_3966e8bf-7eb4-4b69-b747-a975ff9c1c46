package outputs3

import "time"

const (
	// 默认配置常量
	DefaultMaxCacheSize  = 10 * 1024 * 1024 // 10MB
	DefaultMaxCacheLines = 10000
	DefaultRetryTimes    = 3
	DefaultRetryInterval = 5 * time.Second
	DefaultFlushInterval = 2 * time.Second

	// 错误消息常量
	ErrMsgNoPath          = "未指定缓存文件路径"
	ErrMsgNoS3Bucket      = "未指定S3存储桶"
	ErrMsgInvalidFileMode = "无效的文件权限模式: %s"
	ErrMsgInvalidDirMode  = "无效的目录权限模式: %s"
	ErrMsgCreatingDir     = "创建目录失败: %s"
	ErrMsgUploadingToS3   = "上传到S3失败: %s"
	ErrMsgReopenFile      = "重新打开文件失败: %v, 原始错误: %v"
)
