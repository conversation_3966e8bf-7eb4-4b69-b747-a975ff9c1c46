package mysql

import (
	"context"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gstash/config"
	"gstash/config/logevent"
	"gstash/helper/logger"
	hmysql "gstash/helper/mysql"
	"time"
)

const ModuleName = "mysql"

// OutputConfig holds the configuration json fields and internal objects
type OutputConfig struct {
	config.OutputConfig
	DataSourceName string     `json:"dataSourceName"`
	TableName      string     `json:"tableName"`
	Relations      []Relation `json:"relations"`
	Batch          int        `json:"batch"`
	FlushInterval  int        `json:"flushInterval"`

	ChannelSize int `json:"channelSize"`

	buf []map[string]interface{}

	msgChan chan logevent.LogEvent
	db      *gorm.DB
}

type Relation struct {
	Source string `json:"source"`
	Target string `json:"target"`
}

// DefaultOutputConfig returns an OutputConfig struct with default values
func DefaultOutputConfig() OutputConfig {
	return OutputConfig{
		OutputConfig: config.OutputConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
		DataSourceName: "",
		TableName:      "",
	}
}

// InitHandler initialize the output plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeOutputConfig, error) {
	conf := DefaultOutputConfig()
	err := config.ReflectConfig(raw, &conf)
	if err != nil {
		return nil, err
	}
	if conf.FlushInterval <= 0 {
		conf.FlushInterval = 1000
	}
	if conf.ChannelSize < 0 {
		conf.ChannelSize = 100
	}
	if conf.Batch < 0 {
		conf.Batch = 20
	}
	conf.msgChan = make(chan logevent.LogEvent, conf.ChannelSize)

	conf.db, err = gorm.Open(mysql.Open(conf.DataSourceName), &gorm.Config{Logger: hmysql.NewGormLogger(logger.Logger)})
	if err != nil {
		return nil, err
	}

	go conf.start(ctx, conf.msgChan, conf.Batch, conf.FlushInterval)

	return &conf, nil
}

func (o *OutputConfig) start(ctx context.Context, msgChan <-chan logevent.LogEvent, batch int, flushInterval int) {
	o.buf = make([]map[string]interface{}, 0)
	flushTicker := time.NewTicker(time.Millisecond * time.Duration(flushInterval))
	defer flushTicker.Stop()
	for {
		select {
		case event := <-msgChan: // receive
			o.addValues(event)
			select {
			case <-ctx.Done():
				err := o.send()
				if err != nil {
					logger.Logger.Errorf("output mysql, msgChan is closed, need send immediately, err[%s] ", err)
				}
				//logger.Logger.Errorf("output mysql, msgChan is closed, return")
			default:
				for o.isFull() { // need send
					err := o.send()
					if err != nil { // error, reconnect anyway
						logger.Logger.Errorf("output mysql exec error %v", err)
					}
				}
			}
		case <-flushTicker.C:
			_ = o.send()
		}
	}
}

func (o *OutputConfig) send() (err error) {
	if len(o.buf) == 0 {
		return nil
	}
	defer func() {
		o.buf = o.buf[:0]
	}()
	for retry := 3; retry > 0; retry-- {
		err = o.batchInsert()
		if err == nil {
			return
		}
	}
	return err
}

func (o *OutputConfig) isFull() bool {
	return len(o.buf) >= o.Batch
}

func (o *OutputConfig) Output(ctx context.Context, event logevent.LogEvent) (err error) {
	defer func() {
		if err := recover(); err != nil { //产生了panic异常
			logger.Logger.Errorln(err)
		}
	}()

	o.msgChan <- event
	return nil
}

func (o *OutputConfig) addValues(event logevent.LogEvent) {
	ret := make(map[string]interface{})
	for _, v := range o.Relations {
		data, ok := event.GetValue(v.Source)
		if ok {
			ret[v.Target] = data
		}
	}
	if len(ret) != 0 {
		o.buf = append(o.buf, ret)
	}
}

func (o *OutputConfig) batchInsert() (err error) {
	err = o.db.Table(o.TableName).Create(o.buf).Error
	if err != nil {
		return err
	}
	return nil
}
