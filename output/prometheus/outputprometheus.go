package outputprometheus

import (
	"context"
	"github.com/prometheus/client_golang/prometheus"
	"gstash/config"
	"gstash/config/logevent"
)

// ModuleName is the name used in config file
const ModuleName = "prometheus"

// OutputConfig holds the configuration json fields and internal objects
type OutputConfig struct {
	config.OutputConfig
	CounterVecs []*CounterVec
}

type CounterVec struct {
	prometheus.CounterOpts
	LabelToField map[string]string `json:"LabelToField"` // key：label name, value: source field to get label value
	counterVec   *prometheus.CounterVec
}

// DefaultOutputConfig returns an OutputConfig struct with default values
func DefaultOutputConfig() OutputConfig {
	return OutputConfig{
		OutputConfig: config.OutputConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
	}
}

// InitHandler initialize the output plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeOutputConfig, error) {
	conf := DefaultOutputConfig()
	if err := config.ReflectConfig(raw, &conf); err != nil {
		return nil, err
	}

	for _, counterVec := range conf.CounterVecs {
		labels := make([]string, 0)
		for label := range counterVec.LabelToField {
			labels = append(labels, label)
		}
		counterVec.counterVec = prometheus.NewCounterVec(counterVec.CounterOpts, labels)
		if err := prometheus.Register(counterVec.counterVec); err != nil {
			return nil, err
		}
	}

	return &conf, nil
}

// Output event
func (o *OutputConfig) Output(ctx context.Context, event logevent.LogEvent) (err error) {
	for _, c := range o.CounterVecs {
		labels := make(map[string]string)
		for labelName, field := range c.LabelToField {
			labels[labelName] = event.GetString(field)
		}
		c.counterVec.With(labels).Inc()
	}
	return
}
