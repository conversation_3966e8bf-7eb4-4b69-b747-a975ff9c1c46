package outputworker

import (
	"context"
	"github.com/tsaikd/KDGoLib/errutil"
	"gstash/config"
	"gstash/config/logevent"
	"gstash/helper/logger"
	"gstash/helper/utils"
)

// ModuleName is the name used in config file
const ModuleName = "worker"

// OutputConfig holds the configuration json fields and internal objects
type OutputConfig struct {
	config.OutputConfig
	WorkerIds []string `json:"workerIDs"`
	channels  []config.MsgChan
}

// DefaultOutputConfig returns an OutputConfig struct with default values
func DefaultOutputConfig() OutputConfig {
	return OutputConfig{
		OutputConfig: config.OutputConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
	}
}

// InitHandler initialize the output plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeOutputConfig, error) {
	conf := DefaultOutputConfig()
	err := config.ReflectConfig(raw, &conf)
	if err != nil {
		return nil, err
	}
	for _, workerId := range conf.WorkerIds {
		workerConfig := parent.GetParent().GetWorkerById(workerId)
		if workerConfig != nil {
			conf.channels = append(conf.channels, workerConfig.GetChInFilter())
		} else {
			logger.Logger.Errorf("can't find worker config by id: %q", workerId)
		}
	}
	return &conf, nil
}

// errors
var (
	ErrorWorkerNotFound = errutil.NewFactory("can't find worker config by id: %q")
)

// Output event
func (t *OutputConfig) Output(ctx context.Context, event logevent.LogEvent) (err error) {
	for _, ch := range t.channels {
		newEvent := logevent.LogEvent{
			Timestamp: event.Timestamp,
			Message:   event.Message,
		}
		if len(event.Tags) > 0 {
			newEvent.Tags = append(newEvent.Tags, event.Tags...)
		}

		if event.Extra != nil {
			newExtraIface := utils.CopyJsonMap(event.Extra)
			if newExtra, ok := newExtraIface.(map[string]interface{}); ok {
				newEvent.Extra = newExtra
			} else {
				newExtra = make(map[string]interface{})
			}
		}
		ch <- newEvent
	}
	return
}
