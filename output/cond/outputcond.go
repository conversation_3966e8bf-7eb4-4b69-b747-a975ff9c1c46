package outputcond

import (
	"context"
	"github.com/tmsong/govaluate"
	"golang.org/x/sync/errgroup"
	"gstash/config"
	"gstash/helper/logger"

	"gstash/config/logevent"
	"gstash/filter/cond"
)

// ModuleName is the name used in config file
const ModuleName = "cond"

const ErrorTag = "gstash_output_cond_error"

// OutputConfig holds the configuration json fields and internal objects
type OutputConfig struct {
	config.OutputConfig

	CondOutput []CondOutputRaw `json:"condOutput"`

	ElseOutputRaw []config.ConfigRaw `json:"elseOutput"` // filters when does not met any condition
	elseOutputs   []config.TypeOutputConfig
}

type CondOutputRaw struct {
	Condition  string             `json:"condition"` // condition need to test
	OutputRaw  []config.ConfigRaw `json:"output"`    // filters when satisfy the condition
	outputs    []config.TypeOutputConfig
	expression *govaluate.EvaluableExpression
}

// DefaultOutputConfig returns an OutputConfig struct with default values
func DefaultOutputConfig() OutputConfig {
	return OutputConfig{
		OutputConfig: config.OutputConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
	}
}

// InitHandler initialize the output plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeOutputConfig, error) {
	conf := DefaultOutputConfig()
	err := config.ReflectConfig(raw, &conf)
	if err != nil {
		return nil, err
	}
	for idx := range conf.CondOutput {
		if conf.CondOutput[idx].Condition == "" {
			logger.Logger.Warnln("output cond config condition empty, ignored")
			continue
		}
		conf.CondOutput[idx].expression, err = govaluate.NewEvaluableExpressionWithFunctions(conf.CondOutput[idx].Condition, filtercond.BuiltInFunctions)
		if err != nil {
			return nil, err
		}
		conf.CondOutput[idx].outputs, err = config.GetOutputs(parent, ctx, conf.CondOutput[idx].OutputRaw)
		if err != nil {
			return nil, err
		}
		if len(conf.CondOutput[idx].outputs) <= 0 {
			logger.Logger.Warnln("output cond config outputs empty, ignored")
			continue
		}
	}
	if len(conf.ElseOutputRaw) > 0 {
		conf.elseOutputs, err = config.GetOutputs(parent, ctx, conf.ElseOutputRaw)
		if err != nil {
			return nil, err
		}
	}
	return &conf, err
}

// Output event
func (t *OutputConfig) Output(ctx context.Context, event logevent.LogEvent) (err error) {
	var hit bool
	for idx := range t.CondOutput { //挨个匹配，有一个满足条件就输出
		if t.CondOutput[idx].expression != nil {
			ep := filtercond.EventParameters{Event: &event}
			ret, err := t.CondOutput[idx].expression.Eval(&ep)
			if err != nil {
				event.AddTag(ErrorTag)
				continue
			} else if r, ok := ret.(bool); !ok {
				event.AddTag(ErrorTag)
				continue
			} else if !r {
				continue
			}
			hit = true
			if len(t.CondOutput[idx].outputs) == 1 {
				return t.CondOutput[idx].outputs[0].Output(ctx, event)
			} else {
				eg, ctx2 := errgroup.WithContext(ctx)
				for _, output := range t.CondOutput[idx].outputs {
					func(output config.TypeOutputConfig) {
						eg.Go(func() error {
							if err2 := output.Output(ctx2, event); err2 != nil {
								logger.Logger.Errorf("output module %q failed: %v\n", output.GetType(), err2)
							}
							return nil
						})
					}(output)
				}
				return eg.Wait()
			}
		}
	}
	if !hit && len(t.elseOutputs) > 0 { //没有一个满足条件的，向elseOutput输出
		if len(t.elseOutputs) == 1 {
			return t.elseOutputs[0].Output(ctx, event)
		} else {
			eg, ctx2 := errgroup.WithContext(ctx)
			for _, output := range t.elseOutputs {
				func(output config.TypeOutputConfig) {
					eg.Go(func() error {
						if err2 := output.Output(ctx2, event); err2 != nil {
							logger.Logger.Errorf("output module %q failed: %v\n", output.GetType(), err2)
						}
						return nil
					})
				}(output)
			}
			return eg.Wait()
		}
	}
	return nil
}
