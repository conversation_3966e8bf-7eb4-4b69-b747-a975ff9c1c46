gstash output email
=======================

## Synopsis

```
{
    "output": [
        {
            "type": "email",

            // (required)
            "address": "smtp.xxx.com",

            // (required)
            "username": "your_user_name",

            // (required)
            "password": "your_password",

            // (required)
            "subject": "your subject",

            // (required)
            "from": "from@youremail",

            // (required)
            "to": "<EMAIL>;<EMAIL>",

            // (optional)
            "cc": "<EMAIL>;<EMAIL>",

            // (optional)
            "port": 25,

            // (optional)
            "use_tls": true,

            // (optional)
            "attachments": ["/your_path/your_file"],
        }
    ]
}
```