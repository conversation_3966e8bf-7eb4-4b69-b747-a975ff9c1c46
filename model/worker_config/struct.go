package worker_config

import (
	"github.com/tmsong/hlog"
	"gorm.io/gorm"
	"gstash/helper/mysql"
)

type WorkerConfigStatus int

const (
	WORKER_CONFIG_TABLE_NAME = "gstash_worker_config"

	WORKER_CONFIG_STATUS_DISABLED WorkerConfigStatus = 0
	WORKER_CONFIG_STATUS_ENABLED  WorkerConfigStatus = 1
)

type WorkerConfigModel struct {
	logger   *hlog.Logger
	gormDB   *gorm.DB
	readOnly bool
}

func NewWorkConfigModel(logger *hlog.Logger) (model *WorkerConfigModel, err error) {
	model = &WorkerConfigModel{
		logger: logger,
	}

	model.gormDB, err = mysql.GetDefaultDB(logger)

	return
}

type WorkerConfigTable struct {
	gorm.Model
	WorkerName string `json:"worker_name"` //worker 名称
	WorkerID   string `json:"worker_id"`   //worker ID
	Config     string `json:"config"`      //配置内容
	Status     int    `json:"status"`      //配置状态 0-未启用，1-启用
}

func (m *WorkerConfigModel) ShardingWorkerConfigTable() (db *gorm.DB) {
	db = m.gormDB.Table(WORKER_CONFIG_TABLE_NAME).Model(&WorkerConfigTable{})
	return db
}
