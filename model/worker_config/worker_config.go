package worker_config

import "time"

func (m *WorkerConfigModel) QueryWorkerConfigByStatus(status []WorkerConfigStatus) ([]*WorkerConfigTable, error) {
	db := m.ShardingWorkerConfigTable()
	if len(status) > 1 {
		db = db.Where("status in (?)", status)
	} else if len(status) == 1 {
		db = db.Where("status = ?", status[0])
	}
	tmp := make([]*WorkerConfigTable, 0)
	db = db.Find(&tmp)

	return tmp, db.Error
}

func (m *WorkerConfigModel) QueryWorkerConfigByUpdateTime(latestUpdateTime time.Time) ([]*WorkerConfigTable, error) {
	db := m.ShardingWorkerConfigTable()

	db = db.Where("updated_at > ? and status = ?", latestUpdateTime, WORKER_CONFIG_STATUS_ENABLED)

	tmp := make([]*WorkerConfigTable, 0)
	db = db.Find(&tmp)

	return tmp, db.Error
}
