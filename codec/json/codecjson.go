package codecjson

import (
	"context"
	jsoniter "github.com/json-iterator/go"
	"gstash/config"
	"gstash/helper/logger"
	"strings"
	"time"

	"gstash/config/logevent"
)

// ModuleName is the name used in config file
const ModuleName = "json"

// ErrorTag tag added to event when process module failed
const ErrorTag = "gstash_codec_json_error"

// Codec default struct for codec
type Codec struct {
	config.CodecConfig
}

// InitHandler initialize the codec plugin
func InitHandler(context.Context, *config.ConfigRaw) (config.TypeCodecConfig, error) {
	return &Codec{
		CodecConfig: config.CodecConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
	}, nil
}

// Decode returns an event from 'data' as JSON format, adding provided 'eventExtra'
func (c *Codec) Decode(ctx context.Context, data interface{},
	eventExtra map[string]interface{}, tags []string,
	msg<PERSON>han chan<- logevent.LogEvent) (ok bool, err error) {

	event := logevent.LogEvent{
		Timestamp: time.Now(),
		Extra:     eventExtra,
	}
	event.AddTag(tags...)

	switch v := data.(type) {
	case string:
		if strings.HasPrefix(strings.TrimSpace(v), "[") {
			if ok, err := c.DecodeArray(ctx, []byte(v), eventExtra, tags, msgChan); !ok {
				event.Message = v
			} else {
				return true, err
			}
		}
		if len(v) > 0 {
			if err = jsoniter.Unmarshal([]byte(v), &event.Extra); err != nil {
				event.Message = v
			}
		}
	case []byte:
		if startWith(v, '[') {
			if ok, err := c.DecodeArray(ctx, v, eventExtra, tags, msgChan); !ok {
				event.Message = string(v)
			} else {
				return true, err
			}
		}
		if len(v) > 0 {
			if err = jsoniter.Unmarshal(v, &event.Extra); err != nil {
				event.Message = string(v)
			}
		}
	case map[string]interface{}:
		if event.Extra != nil {
			for k, val := range v {
				event.Extra[k] = val
			}
		} else {
			event.Extra = v
		}
	default:
		err = config.ErrDecodeData
	}
	if err != nil {
		event.AddTag(ErrorTag)
		logger.Logger.Errorln(err)
	}

	if event.Extra != nil {
		// try to fill basic log event by json message
		if value, ok := event.Extra["message"]; ok {
			switch v := value.(type) {
			case string:
				event.Message = v
				delete(event.Extra, "message")
			}
		}
		if value, ok := event.Extra["@timestamp"]; ok {
			switch v := value.(type) {
			case string:
				if timestamp, err2 := time.Parse(time.RFC3339Nano, v); err2 == nil {
					event.Timestamp = timestamp
					delete(event.Extra, "@timestamp")
				}
			}
		}
		if value, ok := event.Extra[logevent.TagsField]; ok {
			if event.ParseTags(value) {
				delete(event.Extra, logevent.TagsField)
			} else {
				logger.Logger.Warnf("malformed tags: %v", value)
			}
		}
	}

	msgChan <- event
	ok = true

	return
}

func (c *Codec) DecodeArray(ctx context.Context, data []byte,
	eventExtra map[string]interface{}, tags []string,
	msgChan chan<- logevent.LogEvent) (ok bool, err error) {
	msgs := make([]interface{}, 0)
	if err = jsoniter.Unmarshal(data, &msgs); err != nil {
		return false, err
	} else {
		var subErr error
		for _, msg := range msgs {
			_, subErr = c.Decode(ctx, msg, copyMap(eventExtra), tags, msgChan)
			if subErr != nil {
				err = subErr
			}
		}
		return true, subErr
	}
}

// DecodeEvent decodes 'data' as JSON format to event
func (c *Codec) DecodeEvent(data []byte, v interface{}) error {
	event := logevent.LogEvent{
		Timestamp: time.Now(),
	}

	if err := jsoniter.Unmarshal(data, &event.Extra); err != nil {
		event.Message = string(data)
		event.AddTag(ErrorTag)
		logger.Logger.Errorln(err)
	}

	if event.Extra != nil {
		// try to fill basic log event by json message
		if value, ok := event.Extra["message"]; ok {
			switch v := value.(type) {
			case string:
				event.Message = v
				delete(event.Extra, "message")
			}
		}
		if value, ok := event.Extra["@timestamp"]; ok {
			switch v := value.(type) {
			case string:
				if timestamp, err2 := time.Parse(time.RFC3339Nano, v); err2 == nil {
					event.Timestamp = timestamp
					delete(event.Extra, "@timestamp")
				}
			}
		}
		if value, ok := event.Extra[logevent.TagsField]; ok {
			if event.ParseTags(value) {
				delete(event.Extra, logevent.TagsField)
			} else {
				logger.Logger.Warnf("malformed tags: %v", value)
			}
		}
	}

	switch e := v.(type) {
	case *interface{}:
		*e = event
	case *logevent.LogEvent:
		*e = event
	default:
		return config.ErrorUnsupportedTargetEvent
	}
	return nil
}

// Encode function not implement (TODO)
func (c *Codec) Encode(ctx context.Context, event logevent.LogEvent, dataChan chan<- []byte) (ok bool, err error) {
	return false, config.ErrorNotImplement1.New(nil)
}

func copyMap(m map[string]interface{}) map[string]interface{} {
	cp := make(map[string]interface{})
	for k, v := range m {
		vm, ok := v.(map[string]interface{})
		if ok {
			cp[k] = copyMap(vm)
		} else {
			cp[k] = v
		}
	}

	return cp
}

func startWith(bytes []byte, target byte) bool {
	for _, b := range bytes {
		switch b {
		case '\t', '\n', '\v', '\f', '\r', ' ', 0x85, 0xA0:
			continue
		case target:
			return true
		default:
			return false
		}
	}
	return false
}
