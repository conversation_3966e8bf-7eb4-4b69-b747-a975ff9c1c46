package filtermutate

import (
	"context"
	errutil "github.com/tmsong/utils/error"
	"reflect"
	"strconv"
	"strings"

	"gstash/config"
	"gstash/helper/logger"

	"gstash/config/logevent"
)

const (
	// ModuleName is the name used in config file
	ModuleName = "mutate"
	// ErrorTag tag added to event when process module failed
	ErrorTag = "gstash_filter_mutate_error"
)

// errors
var (
	ErrNotConfigured = errutil.NewFactory("filter mutate not configured")
)

// FilterConfig holds the configuration json fields and internal objects
type FilterConfig struct {
	config.FilterConfig

	Split   [][2]string `yaml:"split"`
	Replace [][3]string `yaml:"replace"`
	Merge   [][2]string `yaml:"merge"`  // merge string value into existing string slice field
	Rename  [][2]string `yaml:"rename"` // rename field name into new field name
	Copy    [][2]string `yaml:"copy"`   // copy field name to new field name
	Slice   [][3]string `yaml:"slice"`  // cut a part of slice, [field,a,b], -1 means last, -2 means the one before last
	slice   []SliceConfig
}

type SliceConfig struct {
	Field string `json:"field"`
	Start int    `json:"start"`
	End   int    `json:"end"`
}

// DefaultFilterConfig returns an FilterConfig struct with default values
func DefaultFilterConfig() FilterConfig {
	return FilterConfig{
		FilterConfig: config.FilterConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
	}
}

// InitHandler initialize the filter plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeFilterConfig, error) {
	conf := DefaultFilterConfig()
	err := config.ReflectConfig(raw, &conf)
	if err != nil {
		return nil, err
	}

	if len(conf.Split) == 0 &&
		len(conf.Replace) == 0 &&
		len(conf.Merge) == 0 &&
		len(conf.Rename) == 0 &&
		len(conf.Copy) == 0 &&
		len(conf.Slice) == 0 &&
		!conf.IsConfigured() {
		return nil, ErrNotConfigured.New(nil)
	}

	for _, s := range conf.Slice {
		if s[0] != "" {
			sc := SliceConfig{Field: s[0]}
			if sc.Start, err = strconv.Atoi(s[1]); err != nil {
				continue
			}
			if sc.End, err = strconv.Atoi(s[2]); err != nil {
				continue
			}
			if sc.End < sc.Start {
				continue
			}
			conf.slice = append(conf.slice, sc)
		}
	}

	return &conf, nil
}

// Event the main filter event
func (f *FilterConfig) Event(ctx context.Context, event logevent.LogEvent) ([]logevent.LogEvent, error) {
	for _, split := range f.Split {
		if split[0] != "" {
			event.SetValue(split[0], strings.Split(event.GetString(split[0]), split[1]))
		}
	}
	for _, replace := range f.Replace {
		if replace[0] != "" {
			event.SetValue(replace[0], strings.Replace(event.GetString(replace[0]), replace[1], replace[2], -1))
		}
	}
	for _, merge := range f.Merge {
		if merge[0] != "" {
			event = mergeField(event, merge[0], merge[1])
		}
	}
	for _, rename := range f.Rename {
		if rename[0] != "" {
			value, ok := event.GetValue(rename[0])
			if ok {
				event.SetValue(rename[1], value)
				event.Remove(rename[0])
			}
		}
	}
	for _, cop := range f.Copy {
		if cop[0] != "" {
			value, ok := event.GetValue(cop[0])
			if ok {
				event.SetValue(cop[1], value)
			}
		}
	}
	for _, slice := range f.slice {
		value, ok := event.GetValue(slice.Field)
		if !ok {
			continue
		}
		values := reflect.ValueOf(value)
		if values.Kind() != reflect.Slice {
			continue
		}
		l := values.Len()
		start := slice.Start
		end := slice.End
		if slice.Start < 0 {
			start = l + start + 1
		}
		if slice.End < 0 {
			end = l + end + 1
		}
		if start < 0 {
			start = 0
		}
		if start > l {
			start = l
		}
		if end < 0 {
			end = 0
		}
		if end > l {
			end = l
		}
		event.SetValue(slice.Field, values.Slice(start, end).Interface())
	}
	// always return true here for configured filter
	return []logevent.LogEvent{event}, nil
}

func mergeField(event logevent.LogEvent, destinationName, source string) logevent.LogEvent {
	destinationValue, ok := event.GetValue(destinationName)
	value := event.Format(source)
	if !ok {
		destinationValue = []string{value}
		event.SetValue(destinationName, destinationValue)
		return event
	}
	switch currentDestination := destinationValue.(type) {
	case string:
		var newDestination []string
		if currentDestination != "" {
			newDestination = append(newDestination, currentDestination)
		}
		newDestination = append(newDestination, value)
		event.SetValue(destinationName, newDestination)
	case []string:
		currentDestination = append(currentDestination, value)
		event.SetValue(destinationName, currentDestination)
	default:
		logger.Logger.Warnf("mutate: destination field %s is not string nor []string", destinationName)
		event.AddTag(ErrorTag)
	}
	return event
}
