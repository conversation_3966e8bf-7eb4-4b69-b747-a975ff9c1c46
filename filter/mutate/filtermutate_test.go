package filtermutate

import (
	"context"
	"strings"
	"testing"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gstash/config"
	"gstash/helper/logger"

	"gstash/config/logevent"
)

func init() {
	logger.Logger.SetLevel(logrus.DebugLevel)
	config.RegistFilterHandler(ModuleName, InitHandler)
}

func Test_filter_mutate_module_error(t *testing.T) {
	require := require.New(t)
	require.NotNil(require)

	ctx := context.Background()
	conf, err := config.LoadFromYAML([]byte(strings.TrimSpace(`
debugch: true
filter:
  - type: mutate
	`)))
	require.NoError(err)
	require.Error(conf.Worker[0].Start(ctx))
}

func Test_filter_mutate_module_rename(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	ctx := context.Background()
	conf, err := config.LoadFromYAML([]byte(strings.TrimSpace(`
debugch: true
filter:
  - type: mutate
    rename: ["key", "key2"]
	`)))
	require.NoError(err)
	require.NoError(conf.Worker[0].Start(ctx))

	expectedEvent := logevent.LogEvent{
		Extra: map[string]interface{}{
			"key2": "foo,bar",
		},
	}

	conf.Worker[0].TestInputEvent(logevent.LogEvent{
		Extra: map[string]interface{}{
			"key": "foo,bar",
		},
	})

	if event, err := conf.Worker[0].TestGetOutputEvent(300 * time.Millisecond); assert.NoError(err) {
		require.Equal(expectedEvent, event)
	}
}
func Test_filter_mutate_module_configured(t *testing.T) {
	require := require.New(t)
	require.NotNil(require)

	ctx := context.Background()
	conf, err := config.LoadFromYAML([]byte(strings.TrimSpace(`
debugch: true
filter:
  - type: mutate
    add_tag: ["testing"]
	`)))
	require.NoError(err)
	require.NoError(conf.Worker[0].Start(ctx))
}

func Test_filter_mutate_module_split(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	ctx := context.Background()
	conf, err := config.LoadFromYAML([]byte(strings.TrimSpace(`
debugch: true
filter:
  - type: mutate
    split: ["key", ","]
	`)))
	require.NoError(err)
	require.NoError(conf.Worker[0].Start(ctx))

	expectedEvent := logevent.LogEvent{
		Extra: map[string]interface{}{
			"key": []string{"foo", "bar"},
		},
	}

	conf.Worker[0].TestInputEvent(logevent.LogEvent{
		Extra: map[string]interface{}{
			"key": "foo,bar",
		},
	})

	if event, err := conf.Worker[0].TestGetOutputEvent(300 * time.Millisecond); assert.NoError(err) {
		require.Equal(expectedEvent, event)
	}
}

func Test_filter_mutate_module_replace(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	ctx := context.Background()
	conf, err := config.LoadFromYAML([]byte(strings.TrimSpace(`
debugch: true
filter:
  - type: mutate
    replace: ["key", ",", "|"]
	`)))
	require.NoError(err)
	require.NoError(conf.Worker[0].Start(ctx))

	expectedEvent := logevent.LogEvent{
		Extra: map[string]interface{}{
			"key": "foo|bar",
		},
	}

	conf.Worker[0].TestInputEvent(logevent.LogEvent{
		Extra: map[string]interface{}{
			"key": "foo,bar",
		},
	})

	if event, err := conf.Worker[0].TestGetOutputEvent(300 * time.Millisecond); assert.NoError(err) {
		require.Equal(expectedEvent, event)
	}
}

func Test_filter_mutate_module_merge(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	ctx := context.Background()
	conf, err := config.LoadFromYAML([]byte(strings.TrimSpace(`
debugch: true
filter:
  - type: mutate
    merge: ["key", "value"]
  - type: mutate
    merge: ["key", "%{field}"]
	`)))
	require.NoError(err)
	require.NoError(conf.Worker[0].Start(ctx))

	expectedEvent := logevent.LogEvent{
		Extra: map[string]interface{}{
			"key":   []string{"value", "fieldvalue"},
			"field": "fieldvalue",
		},
	}

	conf.Worker[0].TestInputEvent(logevent.LogEvent{
		Extra: map[string]interface{}{
			"field": "fieldvalue",
		},
	})

	if event, err := conf.Worker[0].TestGetOutputEvent(300 * time.Millisecond); assert.NoError(err) {
		require.Equal(expectedEvent, event)
	}
}

func Test_filter_mutate_module_merge_error(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	ctx := context.Background()
	conf, err := config.LoadFromYAML([]byte(strings.TrimSpace(`
debugch: true
filter:
  - type: mutate
    merge: ["key", "value"]
	`)))
	require.NoError(err)
	require.NoError(conf.Worker[0].Start(ctx))

	expectedEvent := logevent.LogEvent{
		Extra: map[string]interface{}{
			"key": 1,
		},
		Tags: []string{ErrorTag},
	}

	conf.Worker[0].TestInputEvent(logevent.LogEvent{
		Extra: map[string]interface{}{
			"key": 1,
		},
	})

	if event, err := conf.Worker[0].TestGetOutputEvent(300 * time.Millisecond); assert.NoError(err) {
		require.Equal(expectedEvent, event)
	}
}
