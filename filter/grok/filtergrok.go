package filtergrok

import (
	"context"
	errutil "github.com/tmsong/utils/error"

	"github.com/vjeantet/grok"
	"gstash/config"
	"gstash/helper/logger"

	"gstash/config/logevent"
)

// ModuleName is the name used in config file
const ModuleName = "grok"

// ErrorTag tag added to event when process module failed
const ErrorTag = "gstash_filter_grok_error"

var (
	ErrGrokNoMatches = errutil.NewFactory("grok: no matches")
)

// FilterConfig holds the configuration json fields and internal objects
type FilterConfig struct {
	config.FilterConfig

	PatternsPath      string            `json:"patternsPath"`      // path to patterns file
	Patterns          map[string]string `json:"patterns"`          // pattern definitions
	Match             []string          `json:"match"`             // match pattern
	Source            string            `json:"source"`            // source message field name
	RemoveEmptyValues bool              `json:"removeEmptyValues"` // remove empty values
	DropError         bool              `json:"dropError"`         // drop event if error

	grk *grok.Grok
}

// DefaultFilterConfig returns an FilterConfig struct with default values
func DefaultFilterConfig() FilterConfig {
	return FilterConfig{
		FilterConfig: config.FilterConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
		PatternsPath:      "",
		Patterns:          nil,
		Match:             []string{"%{COMMONAPACHELOG}"},
		Source:            "message",
		RemoveEmptyValues: true,
	}
}

// InitHandler initialize the filter plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeFilterConfig, error) {
	conf := DefaultFilterConfig()
	err := config.ReflectConfig(raw, &conf)
	if err != nil {
		return nil, err
	}

	g, err := grok.NewWithConfig(&grok.Config{
		NamedCapturesOnly: true,
		RemoveEmptyValues: conf.RemoveEmptyValues,
	})
	if err != nil {
		return nil, err
	}
	if conf.PatternsPath != "" {
		err = g.AddPatternsFromPath(conf.PatternsPath)
		if err != nil {
			return nil, err
		}
	}
	if conf.Patterns != nil {
		err = g.AddPatternsFromMap(conf.Patterns)
		if err != nil {
			return nil, err
		}
	}

	conf.grk = g

	return &conf, nil
}

// Event the main filter event
func (f *FilterConfig) Event(ctx context.Context, event logevent.LogEvent) ([]logevent.LogEvent, error) {
	message := event.GetString(f.Source)
	found := false
	for _, thisMatch := range f.Match {
		// grok Parse will success even it doesn't match
		values, err := f.grk.ParseTyped(thisMatch, message)
		if err == nil && len(values) > 0 {
			found = true
			for key, value := range values {
				switch v := value.(type) {
				case string:
					event.SetValue(key, v)
				case nil:
					// pass
				default:
					event.SetValue(key, value)
				}
			}
			logger.Logger.Debugf("Grok Filter: %q %v - Matched: %v in %q", f.Match, event, values, message)

			break
		}
	}

	if !found {
		event.AddTag(ErrorTag)
		logger.Logger.Debugf("grok: no matches for %q", message)
		if f.DropError {
			return []logevent.LogEvent{}, nil
		}
		return []logevent.LogEvent{event}, ErrGrokNoMatches.New(nil)
	}

	return []logevent.LogEvent{event}, nil
}
