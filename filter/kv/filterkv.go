package filterkv

import (
	"context"
	"gstash/config"
	"strings"

	"gstash/config/logevent"
)

// ModuleName is the name used in config file
const ModuleName = "kv"

// ErrorTag tag added to event when process module failed
const ErrorTag = "gstash_filter_kv_error"

// FilterConfig holds the configuration json fields and internal objects
type FilterConfig struct {
	config.FilterConfig
	FieldSplit string `json:"fieldSplit"`
	ValueSplit string `json:"valueSplit"`
	TrimKey    string `json:"trimKey"`
	TrimValue  string `json:"trimValue"`
	Source     string `json:"source"`
}

// DefaultFilterConfig returns an FilterConfig struct with default values
func DefaultFilterConfig() FilterConfig {
	return FilterConfig{
		FilterConfig: config.FilterConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
		Source:     "message",
		FieldSplit: "|",
		ValueSplit: "=",
		TrimKey:    "",
		TrimValue:  "",
	}
}

// InitHandler initialize the filter plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeFilterConfig, error) {
	conf := DefaultFilterConfig()
	err := config.ReflectConfig(raw, &conf)
	if err != nil {
		return nil, err
	}
	return &conf, nil
}

// parseKeyValuePairs 按照用户思路实现：找到所有分隔符位置，然后智能解析
func (f *FilterConfig) parseKeyValuePairs(source string) map[string]string {
	result := make(map[string]string)

	// 1. 找到所有 fieldSplit 的位置
	fieldSplitPositions := f.findAllPositions(source, f.FieldSplit)
	// 在开头添加一个虚拟的 fieldSplit 位置
	fieldSplitPositions = append([]int{-len(f.FieldSplit)}, fieldSplitPositions...)

	// 2. 找到所有 valueSplit 的位置
	valueSplitPositions := f.findAllPositions(source, f.ValueSplit)

	// 3. 对每个 valueSplit，找到它左边最近的 fieldSplit 作为 key 的开始
	for _, valueSplitPos := range valueSplitPositions {
		// 找到这个 valueSplit 左边最近的 fieldSplit
		keyStart := 0
		for _, fieldSplitPos := range fieldSplitPositions {
			if fieldSplitPos < valueSplitPos {
				keyStart = fieldSplitPos + len(f.FieldSplit)
			} else {
				break
			}
		}

		// 提取 key 候选
		keyCandidate := source[keyStart:valueSplitPos]
		if keyCandidate == "" {
			continue
		}

		// 检查这个 key 候选是否有效：不应该包含 valueSplit
		if strings.Contains(keyCandidate, f.ValueSplit) {
			// 这个 valueSplit 在某个 value 中，跳过
			continue
		}

		// 这是一个有效的 key
		key := keyCandidate

		// 4. 从这个 valueSplit 出发，找到下一个 key 的位置
		valueStart := valueSplitPos + len(f.ValueSplit)
		valueEnd := f.findNextKeyPosition(source, valueStart, fieldSplitPositions, valueSplitPositions)

		// 提取 value（不自动 trim，保留原始空格）
		value := source[valueStart:valueEnd]
		result[key] = value
	}

	return result
}

// findAllPositions 找到字符串中所有指定子串的位置
func (f *FilterConfig) findAllPositions(source, substr string) []int {
	var positions []int
	start := 0
	for {
		pos := strings.Index(source[start:], substr)
		if pos == -1 {
			break
		}
		positions = append(positions, start+pos)
		start += pos + len(substr)
	}
	return positions
}

// findNextKeyPosition 找到下一个 key 的位置，即当前 value 的结束位置
func (f *FilterConfig) findNextKeyPosition(source string, valueStart int, fieldSplitPositions, valueSplitPositions []int) int {
	sourceLen := len(source)

	// 找到 valueStart 之后的所有 valueSplit
	for _, valueSplitPos := range valueSplitPositions {
		if valueSplitPos <= valueStart {
			continue
		}

		// 找到这个 valueSplit 左边最近的 fieldSplit
		for _, fieldSplitPos := range fieldSplitPositions {
			if fieldSplitPos >= valueStart && fieldSplitPos < valueSplitPos {
				// 检查 fieldSplit 到 valueSplit 之间是否是一个有效的 key
				keyCandidate := source[fieldSplitPos+len(f.FieldSplit) : valueSplitPos]
				if keyCandidate != "" && !strings.Contains(keyCandidate, f.FieldSplit) && !strings.Contains(keyCandidate, f.ValueSplit) {
					// 这是一个有效的 key，所以当前 value 在这个 fieldSplit 之前结束
					return fieldSplitPos
				}
			}
		}
	}

	// 没有找到下一个 key，value 到字符串末尾
	return sourceLen
}

// Event the main filter event
func (f *FilterConfig) Event(ctx context.Context, event logevent.LogEvent) ([]logevent.LogEvent, error) {
	source := event.GetString(f.Source)
	if source == "" {
		return []logevent.LogEvent{event}, nil
	}

	// 智能解析键值对
	pairs := f.parseKeyValuePairs(source)

	for key, value := range pairs {
		// 应用 trim 配置
		if f.TrimKey != "" {
			key = strings.Trim(key, f.TrimKey)
		}
		if f.TrimValue != "" {
			value = strings.Trim(value, f.TrimValue)
		}

		event.SetValue(key, value)
	}

	return []logevent.LogEvent{event}, nil
}
