package filtertimedrop

import (
	"context"
	"time"

	"gstash/config"

	"gstash/config/logevent"
)

// ModuleName is the name used in config file
const ModuleName = "time_drop"

// FilterConfig holds the configuration json fields and internal objects
type FilterConfig struct {
	config.FilterConfig

	Source          string `json:"source"`          // 从哪里读时间
	OldestTimestamp int64  `json:"oldestTimestamp"` // 这个时间前的事件均被过滤掉
	MaxOffset       int64  `json:"maxOffset"`       // 如果事件到现在的时间大于此值也会被丢掉
}

// DefaultFilterConfig returns an FilterConfig struct with default values
func DefaultFilterConfig() FilterConfig {
	return FilterConfig{
		FilterConfig: config.FilterConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
	}
}

// InitHandler initialize the filter plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeFilterConfig, error) {
	conf := DefaultFilterConfig()
	if err := config.ReflectConfig(raw, &conf); err != nil {
		return nil, err
	}

	if conf.OldestTimestamp <= 0 {
		conf.OldestTimestamp = time.Now().Unix()
	}

	return &conf, nil
}

// Event the main filter event
func (f *FilterConfig) Event(ctx context.Context, event logevent.LogEvent) ([]logevent.LogEvent, error) {

	timestamp := event.Timestamp
	if f.Source != "" { // 自定义了source
		if tmp, ok := event.GetValue(f.Source); !ok {
			return []logevent.LogEvent{event}, nil
		} else if timestamp, ok = tmp.(time.Time); !ok {
			return []logevent.LogEvent{event}, nil
		}
	}
	if timestamp.Unix() < f.OldestTimestamp {
		return nil, nil // drop
	}

	if f.MaxOffset > 0 && time.Now().Unix()-timestamp.Unix() > f.MaxOffset {
		return nil, nil // drop
	}

	return []logevent.LogEvent{event}, nil
}
