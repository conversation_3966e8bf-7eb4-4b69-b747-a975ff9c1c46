package filtercodec

import (
	"context"
	"encoding/base64"
	errutil "github.com/tmsong/utils/error"
	"gstash/config"
	"gstash/config/logevent"
	"net/url"
)

// ModuleName is the name used in config file
const ModuleName = "codec"

// ErrorTag tag added to event when process typeconv failed
const ErrorTag = "gstash_filter_codec_error"

// Errors
var (
	ErrorInvalidType          = errutil.NewFactory(`type %q is not one of ["encode", "decode"]`)
	ErrorInvalidCodec         = errutil.NewFactory(`codec %q is not one of ["base64", "escape"]`)
	ErrorEmptySourceAndTarget = errutil.NewFactory(`empty source and target`)
)

// FilterConfig holds the configuration json fields and internal objects
type FilterConfig struct {
	config.FilterConfig
	Action string `json:"action"`

	Codec  string `json:"codec"`
	Source string `json:"source"`
	Target string `json:"target"`
}

const actionEncode = "encode"
const actionDecode = "decode"

const codecBase64 = "base64"
const codecEscape = "escape"

// DefaultFilterConfig returns an FilterConfig struct with default values
func DefaultFilterConfig() FilterConfig {
	return FilterConfig{
		FilterConfig: config.FilterConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
	}
}

// InitHandler initialize the filter plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeFilterConfig, error) {
	conf := DefaultFilterConfig()
	if err := config.ReflectConfig(raw, &conf); err != nil {
		return nil, err
	}

	if conf.Source == "" || conf.Target == "" {
		return nil, ErrorEmptySourceAndTarget.New(nil)
	}

	switch conf.Action {
	case actionEncode, actionDecode:
	default:
		return nil, ErrorInvalidType.New(nil, conf.Type)
	}

	switch conf.Codec {
	case codecBase64, codecEscape:
	default:
		return nil, ErrorInvalidCodec.New(nil, conf.Codec)
	}

	return &conf, nil
}

// Event the main filter event
func (f *FilterConfig) Event(ctx context.Context, event logevent.LogEvent) ([]logevent.LogEvent, error) {
	if value, ok := event.GetValue(f.Source); ok {
		if valueStr, ok := value.(string); ok {
			switch f.Codec {
			case codecBase64:
				switch f.Action {
				case actionEncode:
					event.SetValue(f.Target, base64.StdEncoding.EncodeToString([]byte(valueStr)))
				case actionDecode:
					newValue, err := base64.StdEncoding.DecodeString(valueStr)
					if err != nil {
						event.AddTag(ErrorTag)
						return []logevent.LogEvent{event}, err
					} else {
						event.SetValue(f.Target, string(newValue))
					}
				}
			case codecEscape:
				switch f.Action {
				case actionEncode:
					event.SetValue(f.Target, url.PathEscape(valueStr))
				case actionDecode:
					newValue, err := url.PathUnescape(valueStr)
					if err != nil {
						event.AddTag(ErrorTag)
						return []logevent.LogEvent{event}, err
					} else {
						event.SetValue(f.Target, newValue)
					}
				}
			}
		}
	}
	return []logevent.LogEvent{event}, nil
}
