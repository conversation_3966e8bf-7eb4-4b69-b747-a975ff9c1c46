package filtermaxlencut

import (
	"context"
	"gstash/config"
	"gstash/config/logevent"
)

// ModuleName is the name used in config file
const ModuleName = "max_len_cut"

// ErrorTag tag added to event when process module failed
const ErrorTag = "gstash_filter_max_len_cut_error"

// FilterConfig holds the configuration json fields and internal objects
type FilterConfig struct {
	config.FilterConfig
	Source string `json:"source"`
	MaxLen int    `json:"maxLen"`
}

// DefaultFilterConfig returns an FilterConfig struct with default values
func DefaultFilterConfig() FilterConfig {
	return FilterConfig{
		FilterConfig: config.FilterConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
		MaxLen: 32768,
	}
}

// InitHandler initialize the filter plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeFilterConfig, error) {
	conf := DefaultFilterConfig()
	if err := config.ReflectConfig(raw, &conf); err != nil {
		return nil, err
	}

	return &conf, nil
}

// Event the main filter event
func (f *FilterConfig) Event(ctx context.Context, event logevent.LogEvent) ([]logevent.LogEvent, error) {
	if f.MaxLen <= 0 {
		return []logevent.LogEvent{event}, nil
	}
	source, ok := event.GetValue(f.Source)
	if !ok {
		return []logevent.LogEvent{event}, nil
	}
	switch source.(type) {
	case string:
		if len(source.(string)) > f.MaxLen {
			event.SetValue(f.Source, source.(string)[:f.MaxLen])
		}
	case []byte:
		if len(source.([]byte)) > f.MaxLen {
			event.SetValue(f.Source, source.([]byte)[:f.MaxLen])
		}
	}
	return []logevent.LogEvent{event}, nil
}
