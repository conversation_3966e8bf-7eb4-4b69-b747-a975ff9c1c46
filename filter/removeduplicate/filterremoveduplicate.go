package filterreduplicate

import (
	"context"
	"fmt"
	"gstash/helper/cache"
	"gstash/helper/redis"
	"time"

	"gstash/config"
	"gstash/helper/logger"

	"gstash/config/logevent"
)

// ModuleName is the name used in config file
const ModuleName = "remove_duplicate"

const (
	DBRedis    = "redis"  // use redis
	DBMemory   = "memory" // use memory
	FlagPrefix = "gstash_remove_duplicate"
)

// FilterConfig holds the configuration json fields and internal objects
type FilterConfig struct {
	config.FilterConfig

	// use which fields to indicate the duplicate messages
	Fields []string `json:"fields"`
	Window int      `json:"window"` // window size, seconds
	DB     string   `json:"db"`

	redisClient *redis.Client
	memoryCache *cache.MemoryCache
}

// DefaultFilterConfig returns an FilterConfig struct with default values
func DefaultFilterConfig() FilterConfig {
	return FilterConfig{
		FilterConfig: config.FilterConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
		Fields: []string{"message"},
		DB:     DBMemory,
	}
}

// InitHandler initialize the filter plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeFilterConfig, error) {
	conf := DefaultFilterConfig()
	err := config.ReflectConfig(raw, &conf)
	if err != nil {
		return nil, err
	}

	if len(conf.Fields) < 1 {
		return nil, fmt.Errorf("remove_duplicate must specify at least one field")
	} else if conf.Window <= 0 {
		return nil, fmt.Errorf("remove_duplicate must specify a valid window")
	}

	switch conf.DB {
	case DBRedis:
		conf.redisClient, err = redis.DefaultClient(logger.Logger)
		if err != nil {
			return nil, err
		}
	case DBMemory:
		conf.memoryCache = cache.NewMemoryCache(128 * cache.MB) // default 128MB cache
	default:
		return nil, fmt.Errorf("filter remove_duplicate db %s not support", conf.DB)
	}

	return &conf, nil
}

// Event the main filter event
func (f *FilterConfig) Event(ctx context.Context, event logevent.LogEvent) ([]logevent.LogEvent, error) {
	flag := FlagPrefix
	for _, field := range f.Fields {
		if value := event.GetString(field); value == "" {
			return []logevent.LogEvent{event}, nil // to prevent unnecessary messages from being filtered
		} else {
			flag += "|" + value
		}
	}
	switch f.DB {
	case DBRedis:
		if suc, err := f.redisClient.SetNX(flag, "1", time.Duration(f.Window)*time.Second); err != nil {
			logger.Logger.Warnf("filter remove_duplicate redis_setnx error: %v", err)
			return []logevent.LogEvent{event}, err // to prevent unnecessary messages from being filtered
		} else if !suc {
			return nil, nil
		} else {
			return []logevent.LogEvent{event}, nil
		}
	case DBMemory:
		var val string
		if err := f.memoryCache.Get(flag, &val); err != nil {
			val = "1"
			_ = f.memoryCache.Set(flag, &val, f.Window)
			return []logevent.LogEvent{event}, err
		} else if val == "" {
			val = "1"
			_ = f.memoryCache.Set(flag, &val, f.Window)
			return []logevent.LogEvent{event}, nil
		} else { // need to remove duplicate
			return nil, nil
		}
	}
	return []logevent.LogEvent{event}, nil
}
