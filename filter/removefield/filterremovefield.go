package filterremovefield

import (
	"context"

	"gstash/config"
	"gstash/helper/logger"

	"gstash/config/logevent"
)

// ModuleName is the name used in config file
const ModuleName = "remove_field"

// FilterConfig holds the configuration json fields and internal objects
type FilterConfig struct {
	config.FilterConfig

	// list all fields to remove
	Fields []string `json:"fields"`
	Tags   []string `json:"tags"`
	// remove event origin message field, not in extra
	RemoveMessage bool `json:"removeMessage"`
}

// DefaultFilterConfig returns an FilterConfig struct with default values
func DefaultFilterConfig() FilterConfig {
	return FilterConfig{
		FilterConfig: config.FilterConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
		Fields:        []string{},
		Tags:          []string{},
		RemoveMessage: false,
	}
}

// InitHandler initialize the filter plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeFilterConfig, error) {
	conf := DefaultFilterConfig()
	if err := config.ReflectConfig(raw, &conf); err != nil {
		return nil, err
	}

	if len(conf.Fields) < 1 {
		logger.Logger.Warnln("filter remove_field config empty fields")
	}

	return &conf, nil
}

// Event the main filter event
func (f *FilterConfig) Event(ctx context.Context, event logevent.LogEvent) ([]logevent.LogEvent, error) {
	if event.Extra == nil {
		event.Extra = map[string]interface{}{}
	}

	for _, field := range f.Fields {
		event.Remove(field)
	}
	event.RemoveTag(f.Tags...)

	if f.RemoveMessage {
		event.Message = ""
	}

	// TODO: remove unset field return false
	return []logevent.LogEvent{event}, nil
}
