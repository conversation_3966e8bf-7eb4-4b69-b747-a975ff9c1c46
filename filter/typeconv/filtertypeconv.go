package filtertypeconv

import (
	"context"
	"fmt"
	"github.com/json-iterator/go"
	pv "github.com/tmsong/utils/pathvalue"
	"gstash/helper/utils"
	"strconv"

	errutil "github.com/tmsong/utils/error"
	"gstash/config"

	"gstash/config/logevent"
)

// ModuleName is the name used in config file
const ModuleName = "typeconv"

// ErrorTag tag added to event when process typeconv failed
const ErrorTag = "gstash_filter_typeconv_error"

// Errors
var (
	ErrorInvalidConvType1 = errutil.NewFactory(`%q is not one of ["string", "int64", "float64"]`)
)

// FilterConfig holds the configuration json fields and internal objects
type FilterConfig struct {
	config.FilterConfig

	ConvType    string        `json:"convType"`    // one of ["string", "int64", "float64"]
	Fields      []string      `json:"fields"`      // fields to convert type
	SliceFields []*SliceField `json:"sliceFields"` // deal with {"a":[{"b":"c"}],use rootField a, elementField b to convert all elements in slice a
}

type SliceField struct {
	RootField    string `json:"rootField"`
	ElementField string `json:"elementField"`
}

const convTypeString = "string"
const convTypeInt64 = "int64"
const convTypeFloat64 = "float64"

// DefaultFilterConfig returns an FilterConfig struct with default values
func DefaultFilterConfig() FilterConfig {
	return FilterConfig{
		FilterConfig: config.FilterConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
		ConvType: "string",
	}
}

// InitHandler initialize the filter plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeFilterConfig, error) {
	conf := DefaultFilterConfig()
	if err := config.ReflectConfig(raw, &conf); err != nil {
		return nil, err
	}

	switch conf.ConvType {
	case convTypeString, convTypeInt64, convTypeFloat64:
	default:
		return nil, ErrorInvalidConvType1.New(nil, conf.ConvType)
	}

	return &conf, nil
}

// Event the main filter event
func (f *FilterConfig) Event(ctx context.Context, event logevent.LogEvent) ([]logevent.LogEvent, error) {
	var errRet error
	for _, field := range f.Fields {
		if value, ok := event.GetValue(field); ok {
			convertedValue, err := f.doConvert(ctx, value)
			if err != nil {
				errRet = err
			} else if convertedValue != nil {
				event.SetValue(field, convertedValue)
			}
		}
	}

	for _, sliceField := range f.SliceFields {
		sliceIface, ok := event.GetValue(sliceField.RootField)
		if !ok {
			continue
		}
		slice, ok := sliceIface.([]interface{})
		if !ok {
			continue
		}
		for idx := range slice {
			value, ok := pv.GetPathValue(slice[idx], sliceField.ElementField)
			if !ok {
				continue
			}
			convertedValue, err := f.doConvert(ctx, value)
			if err != nil {
				errRet = err
			} else if convertedValue == nil {
				continue
			}
			m, ok := slice[idx].(map[string]interface{})
			if !ok {
				continue
			}
			pv.SetPathValue(m, sliceField.ElementField, convertedValue)
			slice[idx] = convertedValue
		}
		event.SetValue(sliceField.RootField, slice)
	}

	return []logevent.LogEvent{event}, errRet
}

func (f *FilterConfig) doConvert(ctx context.Context, value interface{}) (interface{}, error) {
	switch f.ConvType {
	case convTypeString:
		switch v := value.(type) {
		case string:
		case map[string]interface{}:
			if vstr, err := jsoniter.MarshalToString(v); err == nil {
				return vstr, nil
			}
		case []interface{}:
			if vstr, err := jsoniter.MarshalToString(v); err == nil {
				return vstr, nil
			}
		case float64:
			return strconv.FormatFloat(v, 'f', -1, 64), nil
		case int64:
			return strconv.FormatInt(v, 10), nil
		default:
			if !utils.IsNil(v) {
				return fmt.Sprintf("%v", v), nil
			}
		}
	case convTypeInt64:
		switch v := value.(type) {
		case string:
			if vparse, err := strconv.ParseInt(v, 0, 64); err == nil {
				return vparse, nil
			} else if vparse, err := strconv.ParseFloat(fmt.Sprintf("%v", v), 64); err == nil {
				return vparse, nil
			} else {
				return nil, err
			}
		case int:
			return int64(v), nil
		case int8:
			return int64(v), nil
		case int16:
			return int64(v), nil
		case int32:
			return int64(v), nil
		case int64:
		case float32:
			return int64(v), nil
		case float64:
			return int64(v), nil
		default:
			if vparse, err := strconv.ParseInt(fmt.Sprintf("%v", v), 0, 64); err == nil {
				return vparse, nil
			} else if vparse, err := strconv.ParseFloat(fmt.Sprintf("%v", v), 64); err == nil {
				return vparse, nil
			} else {
				return nil, err
			}
		}
	case convTypeFloat64:
		switch v := value.(type) {
		case string:
			if vparse, err := strconv.ParseFloat(v, 64); err == nil {
				return vparse, nil
			} else {
				return nil, err
			}
		case int:
			return float64(v), nil
		case int8:
			return float64(v), nil
		case int16:
			return float64(v), nil
		case int32:
			return float64(v), nil
		case int64:
			return float64(v), nil
		case float32:
			return float64(v), nil
		case float64:
		default:
			if vparse, err := strconv.ParseFloat(fmt.Sprintf("%v", v), 64); err == nil {
				return vparse, nil
			} else {
				return nil, err
			}
		}
	}
	return nil, nil
}
