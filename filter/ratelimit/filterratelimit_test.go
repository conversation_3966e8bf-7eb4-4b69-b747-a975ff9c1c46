package filterratelimit

import (
	"context"
	"gstash/helper/logger"
	"strings"
	"testing"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gstash/config"

	"gstash/config/logevent"
)

func init() {
	logger.Logger.SetLevel(logrus.DebugLevel)
	config.RegistFilterHandler(ModuleName, InitHandler)
}

func Test_filter_ratelimit_module(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	ctx := context.Background()
	conf, err := config.LoadFromYAML([]byte(strings.TrimSpace(`
debugch: true
filter:
  - type: rate_limit
    rate: 10
    burst: 1
	`)))
	require.NoError(err)
	require.NoError(conf.Worker[0].Start(ctx))

	start := time.Now()

	conf.Worker[0].TestInputEvent(logevent.LogEvent{})
	conf.Worker[0].TestInputEvent(logevent.LogEvent{})
	conf.Worker[0].TestInputEvent(logevent.LogEvent{})
	conf.Worker[0].TestInputEvent(logevent.LogEvent{})
	conf.Worker[0].TestInputEvent(logevent.LogEvent{})

	_, err = conf.Worker[0].TestGetOutputEvent(100 * time.Millisecond)
	require.NoError(err)
	_, err = conf.Worker[0].TestGetOutputEvent(100 * time.Millisecond)
	require.NoError(err)
	_, err = conf.Worker[0].TestGetOutputEvent(100 * time.Millisecond)
	require.NoError(err)
	_, err = conf.Worker[0].TestGetOutputEvent(100 * time.Millisecond)
	require.NoError(err)
	_, err = conf.Worker[0].TestGetOutputEvent(100 * time.Millisecond)
	require.NoError(err)

	require.WithinDuration(start.Add(400*time.Millisecond), time.Now(), 150*time.Millisecond)
}

func Test_filter_ratelimit_module_burst(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	ctx := context.Background()
	conf, err := config.LoadFromYAML([]byte(strings.TrimSpace(`
debugch: true
filter:
  - type: rate_limit
    rate: 10
    burst: 4
	`)))
	require.NoError(err)
	require.NoError(conf.Worker[0].Start(ctx))

	time.Sleep(600 * time.Millisecond)

	start := time.Now()

	conf.Worker[0].TestInputEvent(logevent.LogEvent{})
	conf.Worker[0].TestInputEvent(logevent.LogEvent{})
	conf.Worker[0].TestInputEvent(logevent.LogEvent{})
	conf.Worker[0].TestInputEvent(logevent.LogEvent{})
	conf.Worker[0].TestInputEvent(logevent.LogEvent{})
	conf.Worker[0].TestInputEvent(logevent.LogEvent{})

	_, err = conf.Worker[0].TestGetOutputEvent(100 * time.Millisecond)
	require.NoError(err)
	_, err = conf.Worker[0].TestGetOutputEvent(100 * time.Millisecond)
	require.NoError(err)
	_, err = conf.Worker[0].TestGetOutputEvent(100 * time.Millisecond)
	require.NoError(err)
	_, err = conf.Worker[0].TestGetOutputEvent(100 * time.Millisecond)
	require.NoError(err)
	_, err = conf.Worker[0].TestGetOutputEvent(100 * time.Millisecond)
	require.NoError(err)
	_, err = conf.Worker[0].TestGetOutputEvent(100 * time.Millisecond)
	require.NoError(err)

	require.WithinDuration(start.Add(150*time.Millisecond), time.Now(), 100*time.Millisecond)
}

func Test_filter_ratelimit_module_delay(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	ctx := context.Background()
	conf, err := config.LoadFromYAML([]byte(strings.TrimSpace(`
debugch: true
filter:
  - type: rate_limit
    rate: 10
    burst: 1
	`)))
	require.NoError(err)
	require.NoError(conf.Worker[0].Start(ctx))

	time.Sleep(500 * time.Millisecond)

	start := time.Now()

	conf.Worker[0].TestInputEvent(logevent.LogEvent{})
	conf.Worker[0].TestInputEvent(logevent.LogEvent{})
	conf.Worker[0].TestInputEvent(logevent.LogEvent{})
	conf.Worker[0].TestInputEvent(logevent.LogEvent{})
	conf.Worker[0].TestInputEvent(logevent.LogEvent{})

	_, err = conf.Worker[0].TestGetOutputEvent(100 * time.Millisecond)
	require.NoError(err)
	_, err = conf.Worker[0].TestGetOutputEvent(100 * time.Millisecond)
	require.NoError(err)
	_, err = conf.Worker[0].TestGetOutputEvent(100 * time.Millisecond)
	require.NoError(err)
	_, err = conf.Worker[0].TestGetOutputEvent(100 * time.Millisecond)
	require.NoError(err)
	_, err = conf.Worker[0].TestGetOutputEvent(100 * time.Millisecond)
	require.NoError(err)

	require.WithinDuration(start.Add(400*time.Millisecond), time.Now(), 150*time.Millisecond)
}
