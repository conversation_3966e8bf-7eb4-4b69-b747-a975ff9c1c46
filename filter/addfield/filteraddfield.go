package filteraddfield

import (
	"context"
	errutil "github.com/tmsong/utils/error"

	"gstash/config"
	"gstash/config/logevent"
)

// ModuleName is the name used in config file
const ModuleName = "add_field"

// FilterConfig holds the configuration json fields and internal objects
type FilterConfig struct {
	config.FilterConfig
	Key   string `json:"key"`
	Value string `json:"value"`
}

var (
	ErrKeyAlreadyExists = errutil.NewFactory("key already exists: %v")
)

// DefaultFilterConfig returns an FilterConfig struct with default values
func DefaultFilterConfig() FilterConfig {
	return FilterConfig{
		FilterConfig: config.FilterConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
	}
}

// InitHandler initialize the filter plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeFilterConfig, error) {
	conf := DefaultFilterConfig()
	if err := config.ReflectConfig(raw, &conf); err != nil {
		return nil, err
	}

	return &conf, nil
}

// Event the main filter event
func (f *FilterConfig) Event(ctx context.Context, event logevent.LogEvent) ([]logevent.LogEvent, error) {
	if f.Key == "" {
		return []logevent.LogEvent{event}, nil
	}
	if v, ok := event.Extra[f.Key]; ok {
		return []logevent.LogEvent{event}, ErrKeyAlreadyExists.New(nil, v)
	}
	event.SetValue(f.Key, event.Format(f.Value))
	return []logevent.LogEvent{event}, nil
}
