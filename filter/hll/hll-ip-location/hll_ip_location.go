/**
 * @note
 * 读取日志中的ip字段，插入ip位置情报
 *
 * <AUTHOR>
 * @date 	2021-02-02
 */
package hll_ip_location

import (
	"context"
	"encoding/gob"
	"github.com/ipipdotnet/ipdb-go"
	goResty "github.com/tmsong/go-resty"
	"github.com/tsaikd/KDGoLib/errutil"
	"gstash/config"
	"gstash/config/logevent"
	"gstash/helper/logger"
	"gstash/helper/utils"
	"net"
	"os"
	"sync"
	"time"
)

// ModuleName is the name used in config file
const ModuleName = "hll_ip_location"

// ErrorTag tag added to event when process module failed
const ErrorTag = "gstash_filter_hll_ip_location_error"

var (
	once            sync.Once
	ipLocDb         *ipdb.City
	ErrLoadIpdbFile = errutil.NewFactory("load ipdb file error")
)

func init() {
	gob.Register(&IpLocation{})
}

// FilterConfig holds the configuration json fields and internal objects
type FilterConfig struct {
	config.FilterConfig
	IpdbFilePath string `json:"ipdbFilePath"`
	Source       string `json:"source"` //ip field
	Target       string `json:"target"`
	httpClient   *goResty.Client
}

// DefaultFilterConfig returns an FilterConfig struct with default values
func DefaultFilterConfig() FilterConfig {
	return FilterConfig{
		FilterConfig: config.FilterConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
		Source: "client_ip",
		Target: "ip_location",
	}
}

// InitHandler initialize the filter plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeFilterConfig, error) {
	conf := DefaultFilterConfig()
	err := config.ReflectConfig(raw, &conf)
	if err != nil {
		return nil, err
	}
	once.Do(func() { //开始刷新缓存
		ipLocDb, err = ipdb.NewCity(conf.IpdbFilePath)
		refreshIpdb(ctx, conf.IpdbFilePath)
	})
	if ipLocDb == nil || err != nil {
		return nil, ErrLoadIpdbFile.New(err)
	}
	return &conf, nil
}

func refreshIpdb(ctx context.Context, filePath string) {
	if filePath == "" {
		return
	}
	latestUpdateTime := time.Now()
	ticker := time.NewTicker(time.Hour)
	go func() {
		defer ticker.Stop()
		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				select {
				case <-ctx.Done():
					return
				default:
					dgaFileInfo, err := os.Stat(filePath)
					if err != nil {
						continue
					}
					if !dgaFileInfo.ModTime().After(latestUpdateTime) {
						continue
					}
					if err = ipLocDb.Reload(filePath); err == nil {
						latestUpdateTime = dgaFileInfo.ModTime()
						logger.Logger.Infoln("reload ipdb file success")
					} else {
						logger.Logger.Errorln("reload ipdb file error:", err)
					}
				}
			}
		}
	}()
}

// Event the main filter event
func (f *FilterConfig) Event(ctx context.Context, event logevent.LogEvent) ([]logevent.LogEvent, error) {
	ip, ok := event.GetValue(f.Source)
	if !ok {
		return []logevent.LogEvent{event}, nil
	} else if ipStr, ok := ip.(string); !ok {
		return []logevent.LogEvent{event}, nil
	} else if net.ParseIP(ipStr) == nil {
		return []logevent.LogEvent{event}, nil
	} else {
		target := &IpLocation{}
		if utils.IsLocalIp(ipStr) {
			target.Region = "局域网"
			target.Country = "局域网"
		} else {
			cityInfo, err := ipLocDb.FindInfo(ipStr, "CN")
			if err == nil && cityInfo != nil {
				target.BS = cityInfo.BaseStation == "基站"
				target.IDC = cityInfo.IDC == "IDC"
				target.City = cityInfo.CityName
				target.Region = cityInfo.RegionName
				target.Country = cityInfo.CountryName
				target.Latitude = cityInfo.Latitude
				target.Longitude = cityInfo.Longitude
			}
		}
		event.SetValue(f.Target, target)
	}
	return []logevent.LogEvent{event}, nil
}
