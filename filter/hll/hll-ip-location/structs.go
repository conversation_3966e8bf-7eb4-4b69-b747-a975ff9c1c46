/**
 * @note
 * structs
 *
 * <AUTHOR>
 * @date 	2021-02-02
 */
package hll_ip_location

type IpLocation struct {
	BS        bool   `json:"bs"`
	IDC       bool   `json:"idc"`
	City      string `json:"city"`
	Region    string `json:"region"`
	Country   string `json:"country"`
	Latitude  string `json:"latitude"`
	Longitude string `json:"longitude"`
}

type IpLocationData struct {
	BaseStation   string `json:"base_station"`
	CityName      string `json:"city_name"`
	ContinentCode string `json:"continent_code"`
	CountryCode   string `json:"country_code"`
	CountryCode3  string `json:"country_code3"`
	CountryName   string `json:"country_name"`
	Idc           string `json:"idc"`
	IddCode       string `json:"idd_code"`
	IspDomain     string `json:"isp_domain"`
	Latitude      string `json:"latitude"`
	Longitude     string `json:"longitude"`
	OwnerDomain   string `json:"owner_domain"`
	RegionName    string `json:"region_name"`
	Timezone      string `json:"timezone"`
	UtcOffset     string `json:"utc_offset"`
}

type IpLocationResp struct {
	Status int            `json:"status"`
	Msg    string         `json:"msg"`
	Data   IpLocationData `json:"data"`
}
