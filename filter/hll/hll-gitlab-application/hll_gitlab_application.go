package hll_gitlab_application

import (
	"context"
	jsoniter "github.com/json-iterator/go"
	"gstash/config"
	"gstash/config/logevent"
	"strings"
	"time"
)

// ModuleName is the name used in config file
const ModuleName = "hll_gitlab_application"

// FilterConfig holds the configuration json fields and internal objects
type FilterConfig struct {
	config.FilterConfig
}

// DefaultFilterConfig returns an FilterConfig struct with default values
func DefaultFilterConfig() FilterConfig {
	return FilterConfig{
		FilterConfig: config.FilterConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
	}
}

// InitHandler initialize the filter plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeFilterConfig, error) {
	conf := DefaultFilterConfig()
	if err := config.ReflectConfig(raw, &conf); err != nil {
		return nil, err
	}
	return &conf, nil
}

// Event the main filter event
func (f *FilterConfig) Event(ctx context.Context, event logevent.LogEvent) ([]logevent.LogEvent, error) {
	rawLog := &RawLog{}
	err := jsoniter.UnmarshalFromString(event.Message, rawLog)
	if err != nil {
		return nil, err
	}

	if rawLog.Message == "" {
		return nil, nil
	}

	data := strings.Split(rawLog.Message, "\\n")

	infoMap := make(map[string]interface{})
	infoMap["correlation_id"] = rawLog.CorrelationID
	infoMap["severity"] = rawLog.Severity
	extraMap := make(map[string]interface{})
	for _, v := range data {
		msg := strings.Split(v, ": ")
		if len(msg) > 1 {
			switch msg[0] {
			case "dn":
				extraMap["dn"] = msg[1]
			case "cn:":
				infoMap["cn"] = msg[1]
			case "mail":
				infoMap["mail"] = msg[1]
			case "samaccountname":
				event.SetValue(KEY_PERSPECTIVE_VALUE, msg[1])
			case "userprincipalname":
				infoMap["userprincipalname"] = msg[1]
			}
		}
	}

	event.SetValue(KEY_SYSTEM, "gitlab")
	event.SetValue(KEY_PERSPECTIVE_NAME, "hller")

	////解析时间
	event.SetValue(KEY_TIME, rawLog.Time.Format(time.RFC3339Nano))
	event.SetValue(KEY_INFO, infoMap)

	event.SetValue(KEY_ROUTE, "auth")
	event.SetValue(KEY_ACTION, "auth")
	event.SetValue(KEY_CLIENT_IP, "")
	event.SetValue(KEY_USER_AGENT, "")

	return []logevent.LogEvent{event}, nil
}

func (f *FilterConfig) handleTarget(project string) map[string]interface{} {
	ret := make(map[string]interface{})
	ret["project"] = project
	return ret
}
