package hll_gitlab_application

import "time"

const (
	KEY_SYSTEM            = "system"
	KEY_PERSPECTIVE_NAME  = "perspective_name"
	KEY_PERSPECTIVE_VALUE = "perspective_value"
	KEY_ROUTE             = "route"
	KEY_ACTION            = "action"
	KEY_TARGET            = "target"
	KEY_CLIENT_IP         = "client_ip"
	KEY_EXTRA             = "extra"
	KEY_INFO              = "info"
	KEY_TIME              = "time"
	KEY_USER_AGENT        = "user_agent"
)

type RawLog struct {
	Severity      string    `json:"severity"`
	Time          time.Time `json:"time"`
	CorrelationID string    `json:"correlation_id"`
	Message       string    `json:"message"`
}
