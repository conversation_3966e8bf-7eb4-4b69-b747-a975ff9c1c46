package hll_gitlab_rails

import (
	"context"
	jsoniter "github.com/json-iterator/go"
	"gstash/config"
	"gstash/config/logevent"
	"time"
)

// ModuleName is the name used in config file
const ModuleName = "hll_gitlab_rails"

// FilterConfig holds the configuration json fields and internal objects
type FilterConfig struct {
	config.FilterConfig
}

// DefaultFilterConfig returns an FilterConfig struct with default values
func DefaultFilterConfig() FilterConfig {
	return FilterConfig{
		FilterConfig: config.FilterConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
	}
}

// InitHandler initialize the filter plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeFilterConfig, error) {
	conf := DefaultFilterConfig()
	if err := config.ReflectConfig(raw, &conf); err != nil {
		return nil, err
	}
	return &conf, nil
}

// Event the main filter event
func (f *FilterConfig) Event(ctx context.Context, event logevent.LogEvent) ([]logevent.LogEvent, error) {
	rawLog := &RawLog{}
	err := jsoniter.UnmarshalFromString(event.Message, rawLog)
	if err != nil {
		return nil, err
	}
	event.SetValue(KEY_SYSTEM, "gitlab")
	event.SetValue(KEY_PERSPECTIVE_NAME, "hller")

	if rawLog.Username != "" {
		event.SetValue(KEY_PERSPECTIVE_VALUE, rawLog.Username)
	} else {
		event.SetValue(KEY_PERSPECTIVE_VALUE, "unknown")
	}

	event.SetValue(KEY_ACTION, rawLog.Action)
	event.SetValue(KEY_ROUTE, rawLog.Action)

	event.SetValue(KEY_TIME, rawLog.Time.Format(time.RFC3339Nano))
	event.SetValue(KEY_USER_AGENT, rawLog.Ua)
	event.SetValue(KEY_CLIENT_IP, rawLog.RemoteIP)

	event.SetValue(KEY_INFO, f.handleInfo(rawLog))

	event.SetValue(KEY_TARGET, f.handleTarget(rawLog))
	event.SetValue(KEY_EXTRA, f.handleExtra(rawLog))
	return []logevent.LogEvent{event}, nil
}

func (f *FilterConfig) handleTime(timeStr string) string {
	if timeStamp, err := time.ParseInLocation(time.RFC3339Nano, timeStr, time.Local); err == nil {
		return timeStamp.Format(time.RFC3339Nano)
	} else {
		return time.Now().Format(time.RFC3339Nano)
	}
}

func (f *FilterConfig) handleTarget(log *RawLog) map[string]interface{} {
	targetMap := make(map[string]interface{})

	if log.Params != nil {
		for _, v := range log.Params {
			targetMap[v.Key] = v.Value
		}
	}

	return targetMap
}

func (f *FilterConfig) handleInfo(log *RawLog) map[string]interface{} {
	ret := make(map[string]interface{})
	ret["method"] = log.Method
	ret["controller"] = log.Controller
	ret["path"] = log.Path
	ret["status"] = log.Status
	ret["user_id"] = log.UserID
	ret["cpu_s"] = log.CPUS
	ret["correlation_id"] = log.CorrelationID
	ret["feature_category"] = log.MetaFeatureCategory
	return ret
}

func (f *FilterConfig) handleExtra(log *RawLog) map[string]interface{} {
	ret := make(map[string]interface{})
	ret["format"] = log.Format
	ret["view_duration"] = log.ViewDurationS
	ret["duration"] = log.DurationS
	ret["db_count"] = log.DbCount
	ret["db_write_count"] = log.DbWriteCount
	ret["db_cached_count"] = log.DbCachedCount
	ret["db_duration_s"] = log.DbDurationS
	ret["gitaly_calls"] = log.GitalyCalls
	ret["gitaly_duration_s"] = log.GitalyDurationS
	if log.RedisCalls != 0 {
		ret["redis_calls"] = log.RedisCalls
		ret["redis_duration_s"] = log.RedisDurationS
		ret["redis_read_bytes"] = log.RedisReadBytes
		ret["redis_write_bytes"] = log.RedisWriteBytes
		ret["redis_cache_calls"] = log.RedisCacheCalls
		ret["redis_cache_duration_s"] = log.RedisCacheDurationS
		ret["redis_cache_read_bytes"] = log.RedisCacheReadBytes
		ret["redis_cache_write_bytes"] = log.RedisCacheWriteBytes
		ret["redis_shared_state_calls"] = log.RedisSharedStateCalls
		ret["redis_shared_state_duration_s"] = log.RedisSharedStateDurationS
		ret["redis_shared_state_write_bytes"] = log.RedisSharedStateWriteBytes
	}

	return ret
}
