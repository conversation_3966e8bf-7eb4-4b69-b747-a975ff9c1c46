package hll_gitlab_rails

import "time"

const (
	KEY_SYSTEM            = "system"
	KEY_PERSPECTIVE_NAME  = "perspective_name"
	KEY_PERSPECTIVE_VALUE = "perspective_value"
	KEY_ROUTE             = "route"
	KEY_ACTION            = "action"
	KEY_TARGET            = "target"
	KEY_CLIENT_IP         = "client_ip"
	KEY_EXTRA             = "extra"
	KEY_INFO              = "info"
	KEY_TIME              = "time"
	KEY_USER_AGENT        = "user_agent"
)

type RawLog struct {
	Method                     string    `json:"method"`
	Path                       string    `json:"path"`
	Format                     string    `json:"format"`
	Controller                 string    `json:"controller"`
	Action                     string    `json:"action"`
	Status                     int       `json:"status"`
	Time                       time.Time `json:"time"`
	Params                     []Params  `json:"params"`
	RemoteIP                   string    `json:"remote_ip"`
	UserID                     int       `json:"user_id"`
	Username                   string    `json:"username"`
	Ua                         string    `json:"ua"`
	CorrelationID              string    `json:"correlation_id"`
	MetaUser                   string    `json:"meta.user"`
	MetaProject                string    `json:"meta.project"`
	MetaRootNamespace          string    `json:"meta.root_namespace"`
	MetaCallerID               string    `json:"meta.caller_id"`
	MetaRemoteIP               string    `json:"meta.remote_ip"`
	MetaFeatureCategory        string    `json:"meta.feature_category"`
	GitalyCalls                int       `json:"gitaly_calls"`
	GitalyDurationS            float64   `json:"gitaly_duration_s"`
	RedisCalls                 int       `json:"redis_calls"`
	RedisDurationS             float64   `json:"redis_duration_s"`
	RedisReadBytes             int       `json:"redis_read_bytes"`
	RedisWriteBytes            int       `json:"redis_write_bytes"`
	RedisCacheCalls            int       `json:"redis_cache_calls"`
	RedisCacheDurationS        float64   `json:"redis_cache_duration_s"`
	RedisCacheReadBytes        int       `json:"redis_cache_read_bytes"`
	RedisCacheWriteBytes       int       `json:"redis_cache_write_bytes"`
	RedisSharedStateCalls      int       `json:"redis_shared_state_calls"`
	RedisSharedStateDurationS  float64   `json:"redis_shared_state_duration_s"`
	RedisSharedStateReadBytes  int       `json:"redis_shared_state_read_bytes"`
	RedisSharedStateWriteBytes int       `json:"redis_shared_state_write_bytes"`
	DbCount                    int       `json:"db_count"`
	DbWriteCount               int       `json:"db_write_count"`
	DbCachedCount              int       `json:"db_cached_count"`
	CPUS                       float64   `json:"cpu_s"`
	DbDurationS                float64   `json:"db_duration_s"`
	ViewDurationS              float64   `json:"view_duration_s"`
	DurationS                  float64   `json:"duration_s"`
}

type Params struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}
