package hll_gitlab_shell

import (
	"context"
	jsoniter "github.com/json-iterator/go"
	"gstash/config"
	"gstash/config/logevent"
	"time"
)

// ModuleName is the name used in config file
const ModuleName = "hll_gitlab_shell"

// FilterConfig holds the configuration json fields and internal objects
type FilterConfig struct {
	config.FilterConfig
}

// DefaultFilterConfig returns an FilterConfig struct with default values
func DefaultFilterConfig() FilterConfig {
	return FilterConfig{
		FilterConfig: config.FilterConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
	}
}

// InitHandler initialize the filter plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeFilterConfig, error) {
	conf := DefaultFilterConfig()
	if err := config.ReflectConfig(raw, &conf); err != nil {
		return nil, err
	}
	return &conf, nil
}

// Event the main filter event
func (f *FilterConfig) Event(ctx context.Context, event logevent.LogEvent) ([]logevent.LogEvent, error) {
	rawLog := &RawLog{}
	err := jsoniter.UnmarshalFromString(event.Message, rawLog)
	if err != nil {
		return nil, err
	}

	if ignore := ignoreMsg[rawLog.Msg]; ignore {
		return nil, nil
	}

	event.SetValue(KEY_SYSTEM, "gitlab")
	event.SetValue(KEY_PERSPECTIVE_NAME, "hller")
	if rawLog.Username != "" {
		event.SetValue(KEY_PERSPECTIVE_VALUE, rawLog.Username)
	} else {
		event.SetValue(KEY_PERSPECTIVE_VALUE, "unknown")
	}

	event.SetValue(KEY_ROUTE, rawLog.Command)
	event.SetValue(KEY_TIME, rawLog.Time.Format(time.RFC3339Nano))
	event.SetValue(KEY_USER_AGENT, "")
	event.SetValue(KEY_CLIENT_IP, rawLog.RemoteIP)
	event.SetValue(KEY_ACTION, rawLog.Command)
	event.SetValue(KEY_INFO, f.handleInfo(rawLog))
	event.SetValue(KEY_TARGET, f.handleTarget(rawLog))

	return []logevent.LogEvent{event}, nil
}

func (f *FilterConfig) handleTime(timeStr string) string {
	if timeStamp, err := time.ParseInLocation(time.RFC3339Nano, timeStr, time.Local); err == nil {
		return timeStamp.Format(time.RFC3339Nano)
	} else {

		return time.Now().Format(time.RFC3339Nano)
	}
}

func (f *FilterConfig) handleInfo(log *RawLog) map[string]interface{} {
	ret := make(map[string]interface{})
	ret["msg"] = log.Msg
	ret["level"] = log.Level
	ret["gl_key_id"] = log.GlKeyID
	ret["gl_key_type"] = log.GlKeyType
	ret["correlation_id"] = log.CorrelationID

	return ret
}

func (f *FilterConfig) handleTarget(log *RawLog) map[string]interface{} {
	ret := make(map[string]interface{})
	ret["gl_project_path"] = log.GlProjectPath
	ret["gl_repository"] = log.GlRepository
	ret["user_id"] = log.UserID

	return ret
}
