package hll_gitlab_shell

import "time"

const (
	KEY_SYSTEM            = "system"
	KEY_PERSPECTIVE_NAME  = "perspective_name"
	KEY_PERSPECTIVE_VALUE = "perspective_value"
	KEY_ROUTE             = "route"
	KEY_ACTION            = "action"
	KEY_TARGET            = "target"
	KEY_CLIENT_IP         = "client_ip"
	KEY_EXTRA             = "extra"
	KEY_INFO              = "info"
	KEY_TIME              = "time"
	KEY_USER_AGENT        = "user_agent"
)

type RawLog struct {
	Command       string    `json:"command"`
	CorrelationID string    `json:"correlation_id"`
	GitProtocol   string    `json:"git_protocol"`
	GlKeyID       int       `json:"gl_key_id"`
	GlKeyType     string    `json:"gl_key_type"`
	GlProjectPath string    `json:"gl_project_path"`
	GlRepository  string    `json:"gl_repository"`
	Level         string    `json:"level"`
	Msg           string    `json:"msg"`
	RemoteIP      string    `json:"remote_ip"`
	Time          time.Time `json:"time"`
	UserID        string    `json:"user_id"`
	Username      string    `json:"username"`
}

var ignoreMsg = map[string]bool{
	"Finished HTTP request": true,
}
