/**
 * @note
 * 读取日志中的hller字段，插入hller相关情报
 *
 * <AUTHOR>
 * @date 	2021-02-02
 */
package enrich_mysql

import (
	"bytes"
	"context"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gstash/config"
	"gstash/config/logevent"
	"gstash/helper/logger"
	hmysql "gstash/helper/mysql"
	"text/template"
)

// ModuleName is the name used in config file
const ModuleName = "enrich_mysql"

// ErrorTag tag added to event when process module failed
const ErrorTag = "gstash_filter_enrich_mysql_error"

// FilterConfig holds the configuration json fields and internal objects
type FilterConfig struct {
	config.FilterConfig
	Query          string `json:"query"`
	DataSourceName string `json:"dataSourceName"` //mysql的连接串
	Target         string `json:"target"`         //要把信息set到哪里
	db             *gorm.DB
	queryTmpl      *template.Template
}

// DefaultFilterConfig returns an FilterConfig struct with default values
func DefaultFilterConfig() FilterConfig {
	return FilterConfig{
		FilterConfig: config.FilterConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
	}
}

// InitHandler initialize the filter plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeFilterConfig, error) {
	conf := DefaultFilterConfig()
	if err := config.ReflectConfig(raw, &conf); err != nil {
		return nil, err
	} else if conf.queryTmpl, err = template.New("").Parse(conf.Query); err != nil {
		return nil, err
	} else if conf.db, err = gorm.Open(mysql.Open(conf.DataSourceName), &gorm.Config{Logger: hmysql.NewGormLogger(logger.Logger)}); err != nil {
		return nil, err
	}
	return &conf, nil
}

// Event the main filter event
func (f *FilterConfig) Event(ctx context.Context, event logevent.LogEvent) ([]logevent.LogEvent, error) {
	//step 1 请求mysql
	buf := new(bytes.Buffer)
	err := f.queryTmpl.Execute(buf, event.GetJSONMap())
	if err != nil {
		event.AddTag(ErrorTag)
		return []logevent.LogEvent{event}, err
	}
	result := make(map[string]interface{})
	db := f.db.Raw(buf.String()).Scan(&result)
	if db.Error != nil {
		event.AddTag(ErrorTag)
		return []logevent.LogEvent{event}, err
	}

	event.SetValue(f.Target, result)
	return []logevent.LogEvent{event}, nil
}
