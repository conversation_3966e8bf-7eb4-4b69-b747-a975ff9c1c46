/**
 * @note
 * 读取日志中的ip字段，插入ip位置情报
 *
 * <AUTHOR>
 * @date 	2021-02-02
 */
package validate_utf8

import (
	"context"
	"encoding/base64"
	pv "github.com/tmsong/utils/pathvalue"
	"gstash/config"
	"gstash/config/logevent"
	"unicode/utf8"
)

// ModuleName is the name used in config file
const ModuleName = "validate_utf8"

// ErrorTag tag added to event when process module failed
const ErrorTag = "gstash_filter_utf8validate_error"

const typeFilter = "filter"
const typeBase64 = "base64"

// FilterConfig holds the configuration json fields and internal objects
type FilterConfig struct {
	config.FilterConfig
	Fields   []string `json:"fields"`
	ConvType string   `json:"convType"`
}

// DefaultFilterConfig returns an FilterConfig struct with default values
func DefaultFilterConfig() FilterConfig {
	return FilterConfig{
		FilterConfig: config.FilterConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
	}
}

// InitHandler initialize the filter plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeFilterConfig, error) {
	conf := DefaultFilterConfig()
	if err := config.ReflectConfig(raw, &conf); err != nil {
		return nil, err
	}
	pv.SetPathToStaticCache(conf.Fields...)
	return &conf, nil
}

// Event the main filter event
func (f *FilterConfig) Event(ctx context.Context, event logevent.LogEvent) ([]logevent.LogEvent, error) {
	for _, field := range f.Fields {
		v, ok := event.GetValue(field)
		if !ok {
			continue
		}
		newV, changed := f.validate(v)
		if changed {
			event.SetValue(field, newV)
		}
	}
	return []logevent.LogEvent{event}, nil
}

func (f *FilterConfig) validate(value interface{}) (newValue interface{}, changed bool) {
	switch value.(type) {
	case string:
		str := value.(string)
		if utf8.ValidString(str) {
			return value, false
		}
		switch f.ConvType {
		case typeFilter:
			return "Invalid utf8 detected by gstash, filtered", true
		case typeBase64:
			return "Invalid utf8 detected by gstash, base64: " + base64.StdEncoding.EncodeToString([]byte(str)), true
		default:
			return value, false
		}
	case []interface{}:
		slice := value.([]interface{})
		for idx := range slice {
			if newSubValue, subChanged := f.validate(slice[idx]); subChanged {
				changed = true
				slice[idx] = newSubValue
			}
		}
		return slice, changed
	case map[string]interface{}:
		m := value.(map[string]interface{})
		for k, v := range m {
			if newSubValue, subChanged := f.validate(v); subChanged {
				changed = true
				m[k] = newSubValue
			}
		}
		return m, changed
	default:
		return value, false
	}
}
