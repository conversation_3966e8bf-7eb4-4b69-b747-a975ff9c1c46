package filterexpand

import (
	"context"
	errutil "github.com/tmsong/utils/error"
	"gstash/config"
	"gstash/config/logevent"
	"strings"
)

const (
	// ModuleName is the name used in config file
	ModuleName = "expand"
	// ErrorTag tag added to event when process module failed
	ErrorTag = "gstash_filter_expand_error"
)

// errors
var (
	ErrNotConfigured = errutil.NewFactory("filter expand not configured")
)

// FilterConfig holds the configuration json fields and internal objects
type FilterConfig struct {
	config.FilterConfig

	Source     string   `json:"source"`
	KeepFields []string `json:"keepFields"`
}

// DefaultFilterConfig returns an FilterConfig struct with default values
func DefaultFilterConfig() FilterConfig {
	return FilterConfig{
		FilterConfig: config.FilterConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
	}
}

// InitHandler initialize the filter plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeFilterConfig, error) {
	conf := DefaultFilterConfig()
	err := config.ReflectConfig(raw, &conf)
	if err != nil {
		return nil, err
	}

	if len(conf.Source) == 0 &&
		!conf.IsConfigured() {
		return nil, ErrNotConfigured.New(nil)
	}

	keepFields := make([]string, 0)
	for _, k := range conf.KeepFields {
		if strings.Split(k, ".")[0] == strings.Split(conf.Source, ".")[0] { // 不能产生递归情况
			continue
		}
		keepFields = append(keepFields, k)
	}
	conf.KeepFields = keepFields

	return &conf, nil
}

// Event the main filter event
func (f *FilterConfig) Event(ctx context.Context, event logevent.LogEvent) ([]logevent.LogEvent, error) {
	i, ok := event.GetValue(f.Source)
	if !ok {
		return []logevent.LogEvent{event}, nil
	}
	if m, ok := i.(map[string]interface{}); ok {
		for _, field := range f.KeepFields {
			if keepValue, ok := event.GetValue(field); ok {
				m[field] = keepValue
			}
		}
		event.Extra = m
		return []logevent.LogEvent{event}, nil
	} else if ifaces, ok := i.([]interface{}); ok {
		var rets []logevent.LogEvent
		for _, iface := range ifaces {
			element, ok := iface.(map[string]interface{})
			if !ok {
				continue
			}
			for _, field := range f.KeepFields {
				if keepValue, ok := event.GetValue(field); ok {
					element[field] = keepValue
				}
			}
			newEvent := logevent.LogEvent{
				Timestamp: event.Timestamp,
				Extra:     element,
			}
			if len(event.Tags) > 0 {
				newEvent.Tags = append(newEvent.Tags, event.Tags...)
			}
			rets = append(rets, newEvent)
		}
		return rets, nil
	}
	return nil, nil
}
