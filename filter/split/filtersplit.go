package filtersplit

import (
	"context"
	"gstash/helper/utils"
	"strings"

	"gstash/config"
	"gstash/config/logevent"
)

// ModuleName is the name used in config file
const ModuleName = "split"

// FilterConfig holds the configuration json fields and internal objects
type FilterConfig struct {
	config.FilterConfig
	Delimiter string `json:"delimiter"`
}

// DefaultFilterConfig returns an FilterConfig struct with default values
func DefaultFilterConfig() FilterConfig {
	return FilterConfig{
		FilterConfig: config.FilterConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
	}
}

// InitHandler initialize the filter plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeFilterConfig, error) {
	conf := DefaultFilterConfig()
	if err := config.ReflectConfig(raw, &conf); err != nil {
		return nil, err
	}

	return &conf, nil
}

// Event the main filter event
func (f *FilterConfig) Event(ctx context.Context, event logevent.LogEvent) ([]logevent.LogEvent, error) {
	if f.Delimiter == "" {
		return []logevent.LogEvent{event}, nil
	}
	splits := strings.Split(event.Message, f.Delimiter)
	ret := make([]logevent.LogEvent, 0, len(splits))
	for _, split := range splits {
		if split == "" {
			continue
		}
		newEvent := logevent.LogEvent{
			Timestamp: event.Timestamp,
			Message:   split,
		}
		if len(event.Tags) > 0 {
			newEvent.Tags = append(newEvent.Tags, event.Tags...)
		}

		if event.Extra != nil {
			newExtraIface := utils.CopyJsonMap(event.Extra)
			if newExtra, ok := newExtraIface.(map[string]interface{}); ok {
				newEvent.Extra = newExtra
			} else {
				newExtra = make(map[string]interface{})
			}
		}
		ret = append(ret, newEvent)
	}
	return ret, nil
}
