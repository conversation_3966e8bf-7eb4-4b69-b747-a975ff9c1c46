/**
 * @note
 * 读取日志中的ip字段，插入ip位置情报
 *
 * <AUTHOR>
 * @date 	2021-02-02
 */
package strmap

import (
	"context"
	"gstash/config"
	"gstash/config/logevent"
)

// ModuleName is the name used in config file
const ModuleName = "strmap"

// ErrorTag tag added to event when process module failed
const ErrorTag = "gstash_filter_strmap_error"

// FilterConfig holds the configuration json fields and internal objects
type FilterConfig struct {
	config.FilterConfig
	Source string                 `json:"source"`
	Target string                 `json:"target"`
	Map    map[string]interface{} `json:"map"`
}

// DefaultFilterConfig returns an FilterConfig struct with default values
func DefaultFilterConfig() FilterConfig {
	return FilterConfig{
		FilterConfig: config.FilterConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
	}
}

// InitHandler initialize the filter plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeFilterConfig, error) {
	conf := DefaultFilterConfig()
	if err := config.ReflectConfig(raw, &conf); err != nil {
		return nil, err
	}
	return &conf, nil
}

// Event the main filter event
func (f *FilterConfig) Event(ctx context.Context, event logevent.LogEvent) ([]logevent.LogEvent, error) {
	if f.Source == "" || f.Target == "" || len(f.Map) == 0 {
		return []logevent.LogEvent{event}, nil
	}
	if key := event.GetString(f.Source); key != "" {
		if value, ok := f.Map[key]; ok {
			event.SetValue(f.Target, value)
		}
	}
	return []logevent.LogEvent{event}, nil
}
