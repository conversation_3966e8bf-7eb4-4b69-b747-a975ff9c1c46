package filtercond

import (
	"context"
	"encoding/json"
	"github.com/tmsong/govaluate"
	errutil "github.com/tmsong/utils/error"
	"gstash/config"
	"gstash/config/logevent"
	"gstash/helper/logger"
	"math/rand"
	"reflect"
	"strings"
)

// ModuleName is the name used in config file
const ModuleName = "cond"

// ErrorTag tag added to event when process geoip2 failed
const ErrorTag = "gstash_filter_cond_error"

var (
	ErrEvalCond         = errutil.NewFactory("eval cond error")
	ErrArgsNumNotMatch  = errutil.NewFactory("args num not match")
	ErrArgsTypeNotMatch = errutil.NewFactory("args type not match")
)

// built-in functions
var (
	ErrorBuiltInFunctionParameters1 = errutil.NewFactory("Built-in function '%s' parameters error")
	BuiltInFunctions                = map[string]govaluate.ExpressionFunction{
		"empty": func(args ...interface{}) (interface{}, error) {
			if len(args) > 1 {
				return nil, ErrorBuiltInFunctionParameters1.New(ErrArgsNumNotMatch, "empty")
			} else if len(args) == 0 {
				return true, nil
			}
			return args[0] == nil, nil
		},
		"strlen": func(args ...interface{}) (interface{}, error) {
			if len(args) > 1 {
				return nil, ErrorBuiltInFunctionParameters1.New(ErrArgsNumNotMatch, "strlen")
			} else if len(args) == 0 {
				return float64(0), nil
			}
			length := len(args[0].(string))
			return (float64)(length), nil
		},
		"strprefix": func(args ...interface{}) (interface{}, error) {
			if len(args) <= 1 {
				return nil, ErrorBuiltInFunctionParameters1.New(ErrArgsNumNotMatch, "strprefix")
			}
			src, ok := args[0].(string)
			if !ok {
				return false, nil
			}
			for i := 1; i < len(args); i++ {
				if tar, ok := args[i].(string); ok && strings.HasPrefix(src, tar) {
					return true, nil
				}
			}
			return false, nil
		},
		"strcontains": func(args ...interface{}) (interface{}, error) {
			if len(args) < 1 {
				return nil, ErrorBuiltInFunctionParameters1.New(ErrArgsNumNotMatch, "strcontains")
			}
			src, ok := args[0].(string)
			if !ok {
				return false, nil
			}
			for i := 1; i < len(args); i++ {
				if tar, ok := args[i].(string); ok && strings.Contains(src, tar) {
					return true, nil
				}
			}
			return false, nil
		},
		"contains": func(args ...interface{}) (interface{}, error) { //支持字符串或数字
			if len(args) < 2 {
				return nil, ErrorBuiltInFunctionParameters1.New(ErrArgsNumNotMatch, "contains")
			}
			target := args[len(args)-1]
			for i := 0; i < len(args)-1; i++ {
				if jn, ok := args[i].(json.Number); ok {
					if f, err := jn.Float64(); err != nil {
						return false, ErrorBuiltInFunctionParameters1.New(err, "contains")
					} else if f == target {
						return true, nil
					}
				} else if args[i] == target {
					return true, nil
				}
			}
			return false, nil
		},
		"map": func(args ...interface{}) (interface{}, error) {
			if len(args) > 1 {
				return nil, ErrorBuiltInFunctionParameters1.New(ErrArgsNumNotMatch, "map")
			} else if len(args) == 0 {
				return []interface{}{}, nil
			}

			s := reflect.ValueOf(args[0])
			if s.Kind() != reflect.Slice {
				return nil, ErrorBuiltInFunctionParameters1.New(ErrArgsTypeNotMatch, "map")
			}

			ret := make([]interface{}, s.Len())

			for i := 0; i < s.Len(); i++ {
				ret[i] = s.Index(i).Interface()
			}

			return ret, nil
		},
		"rand": func(args ...interface{}) (interface{}, error) {
			if len(args) > 0 {
				return nil, ErrorBuiltInFunctionParameters1.New(ErrArgsNumNotMatch, "rand")
			}
			return rand.Float64(), nil
		},
		"isnumeric": func(args ...interface{}) (interface{}, error) {
			if len(args) != 1 {
				return nil, ErrorBuiltInFunctionParameters1.New(ErrArgsNumNotMatch, "isnumeric")
			}
			kind := reflect.TypeOf(args[0]).Kind()
			return kind == reflect.Int || kind == reflect.Int8 || kind == reflect.Int16 || kind == reflect.Int32 || kind == reflect.Int64 ||
				kind == reflect.Uint || kind == reflect.Uint8 || kind == reflect.Uint16 || kind == reflect.Uint32 || kind == reflect.Uint64 ||
				kind == reflect.Float32 || kind == reflect.Float64, nil
		},
		"isstring": func(args ...interface{}) (interface{}, error) {
			if len(args) != 1 {
				return nil, ErrorBuiltInFunctionParameters1.New(ErrArgsNumNotMatch, "isstring")
			}
			kind := reflect.TypeOf(args[0]).Kind()
			return kind == reflect.String, nil
		},
		"length": func(args ...interface{}) (interface{}, error) {
			return float64(len(args)), nil
		},
	}
)

// FilterConfig holds the configuration json fields and internal objects
type FilterConfig struct {
	config.FilterConfig

	CondFilter []CondFilterRaw `json:"condFilter"`

	ElseFilterRaw []config.ConfigRaw `json:"elseFilter"` // filters when does not met the condition
	elseFilters   []config.TypeFilterConfig

	events         []logevent.LogEvent
	originEvents   []logevent.LogEvent
	filteredEvents []logevent.LogEvent
}

type CondFilterRaw struct {
	Condition  string             `json:"condition"` // condition need to test
	FilterRaw  []config.ConfigRaw `json:"filter"`    // filters when satisfy the condition
	filters    []config.TypeFilterConfig
	expression *govaluate.EvaluableExpression
}

// EventParameters pack event's parameters by member function `Get` access
type EventParameters struct {
	Event *logevent.LogEvent
}

// Get obtaining value from event's specified field recursively
func (ep *EventParameters) Get(field string) (interface{}, error) {
	if !strings.ContainsRune(field, '.') && !strings.ContainsRune(field, '[') && !strings.ContainsRune(field, ']') {
		// no nest fields
		return ep.Event.Get(field), nil
	}
	v, _ := ep.Event.GetValue(field)
	return v, nil
}

// DefaultFilterConfig returns an FilterConfig struct with default values
func DefaultFilterConfig() FilterConfig {
	return FilterConfig{
		FilterConfig: config.FilterConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
	}
}

// InitHandler initialize the filter plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeFilterConfig, error) {
	conf := DefaultFilterConfig()
	err := config.ReflectConfig(raw, &conf)
	if err != nil {
		return nil, err
	}
	for idx := range conf.CondFilter {
		if conf.CondFilter[idx].Condition == "" {
			logger.Logger.Warnln("filter cond config condition empty, ignored")
			continue
		}
		conf.CondFilter[idx].expression, err = govaluate.NewEvaluableExpressionWithFunctions(conf.CondFilter[idx].Condition, BuiltInFunctions)
		if err != nil {
			return nil, err
		}
		conf.CondFilter[idx].filters, err = config.GetFilters(parent, ctx, conf.CondFilter[idx].FilterRaw)
		if err != nil {
			return nil, err
		}
		if len(conf.CondFilter[idx].filters) <= 0 {
			logger.Logger.Warnln("filter cond config filters empty, ignored")
			continue
		}
	}
	if len(conf.ElseFilterRaw) > 0 {
		conf.elseFilters, err = config.GetFilters(parent, ctx, conf.ElseFilterRaw)
		if err != nil {
			return nil, err
		}
	}

	conf.originEvents = make([]logevent.LogEvent, 0, 1<<3)   // 用于放上一层event，先随便开辟一点空间
	conf.filteredEvents = make([]logevent.LogEvent, 0, 1<<3) // 用于放下一层event，先随便开辟一点空间
	return &conf, err
}

// Event the main filter event
func (f *FilterConfig) Event(ctx context.Context, event logevent.LogEvent) ([]logevent.LogEvent, error) {
	var errRet, err error
	var hit bool
	for i := range f.CondFilter { //挨个匹配，有一个满足条件就干
		if f.CondFilter[i].expression != nil {
			ep := EventParameters{Event: &event}
			ret, err := f.CondFilter[i].expression.Eval(&ep)
			if err != nil {
				event.AddTag(ErrorTag)
				logger.Logger.Errorf("filter cond %s eval error: %v", f.CondFilter[i].expression.String(), err)
				errRet = ErrEvalCond.New(err)
				continue
			} else if r, ok := ret.(bool); !ok {
				event.AddTag(ErrorTag)
				errRet = ErrEvalCond.New(err)
				continue
			} else if !r {
				continue
			} else {
				hit = true
				f.filteredEvents = f.filteredEvents[:0]            // 清空数组，层序遍历
				f.filteredEvents = append(f.filteredEvents, event) // 放入第一个元素
				for j := range f.CondFilter[i].filters {
					f.originEvents = f.filteredEvents
					f.filteredEvents = f.originEvents[:0] // 交换内存空间
					for k := range f.originEvents {
						f.events, err = f.CondFilter[i].filters[j].Event(ctx, f.originEvents[k])
						if err != nil {
							errRet = err
						}
						f.filteredEvents = append(f.filteredEvents, f.events...)
					}
				}
				break
			}
		}
	}
	if !hit {
		f.filteredEvents = f.filteredEvents[:0]            // 清空数组，层序遍历
		f.filteredEvents = append(f.filteredEvents, event) // 放入第一个元素
		for j := range f.elseFilters {
			f.originEvents = f.filteredEvents
			f.filteredEvents = f.originEvents[:0] // 交换内存空间
			for k := range f.originEvents {
				f.events, err = f.elseFilters[j].Event(ctx, f.originEvents[k])
				if err != nil {
					errRet = err
				}
				f.filteredEvents = append(f.filteredEvents, f.events...)
			}
		}
	}
	return f.filteredEvents, errRet
}
