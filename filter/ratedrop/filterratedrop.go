package filterratedrop

import (
	"context"
	"math/rand"
	"time"

	"gstash/config"

	"gstash/config/logevent"
)

// ModuleName is the name used in config file
const ModuleName = "rate_drop"

// FilterConfig holds the configuration json fields and internal objects
type FilterConfig struct {
	config.FilterConfig

	Rate int64 `json:"rate"` // event number per second

	count    int64     //current msg count
	lastTime time.Time //last time that flushed ratio
	ratio    int64     //ratio to drop, max = 1e9
	rand     *rand.Rand
}

// DefaultFilterConfig returns an FilterConfig struct with default values
func DefaultFilterConfig() FilterConfig {
	return FilterConfig{
		FilterConfig: config.FilterConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
	}
}

// InitHandler initialize the filter plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeFilterConfig, error) {
	conf := DefaultFilterConfig()
	if err := config.ReflectConfig(raw, &conf); err != nil {
		return nil, err
	}

	conf.rand = rand.New(rand.NewSource(time.Now().UnixNano()))
	conf.lastTime = time.Now()
	conf.ratio = 1e9

	return &conf, nil
}

// Event the main filter event
func (f *FilterConfig) Event(ctx context.Context, event logevent.LogEvent) ([]logevent.LogEvent, error) {
	if f.Rate <= 0 {
		return nil, nil
	}
	f.count++ //计数
	if f.count >= f.Rate {
		currentTime := time.Now()
		f.ratio = currentTime.UnixNano() - f.lastTime.UnixNano()
		f.lastTime = currentTime
		f.count = 0
	}
	if f.ratio >= 1e9 { //no need to drop
		return []logevent.LogEvent{event}, nil
	}

	if f.rand.Int63n(1e9) < f.ratio {
		return []logevent.LogEvent{event}, nil
	}

	return nil, nil
}
