package filterdate

import (
	"context"
	"math"
	"strconv"
	"strings"
	"time"

	"github.com/tengattack/jodatime"
	"gstash/config"
	"gstash/config/logevent"
)

// ModuleName is the name used in config file
const ModuleName = "date"

// ErrorTag tag added to event when process module failed
const ErrorTag = "gstash_filter_date_error"

// DefaultTarget default event field to store date as
const DefaultTarget = "@timestamp"

// FilterConfig holds the configuration json fields and internal objects
type FilterConfig struct {
	config.FilterConfig

	Format      []string `json:"format"`      // date parse format
	Source      string   `json:"source"`      // source message field name
	Sources     []string `json:"sources"`     // try to find time in multi sources
	Joda        bool     `json:"joda"`        // whether using joda time format
	Target      string   `json:"target"`      // target field where date should be stored
	Location    string   `json:"location"`    // parse in location
	IgnoreError bool     `json:"ignoreError"` // ignore parse error

	timeParser         func(layout, value string) (time.Time, error)
	timeLocationParser func(layout, value string, location *time.Location) (time.Time, error)
	timeLocation       *time.Location
}

// DefaultFilterConfig returns an FilterConfig struct with default values
func DefaultFilterConfig() FilterConfig {
	return FilterConfig{
		FilterConfig: config.FilterConfig{
			CommonConfig: config.CommonConfig{
				Type: ModuleName,
			},
		},
		Format: []string{time.RFC3339Nano},
		Source: "message",
		Target: DefaultTarget,
	}
}

// InitHandler initialize the filter plugin
func InitHandler(parent *config.WorkerConfig, ctx context.Context, raw *config.ConfigRaw) (config.TypeFilterConfig, error) {
	conf := DefaultFilterConfig()
	if err := config.ReflectConfig(raw, &conf); err != nil {
		return nil, err
	}

	if conf.Joda {
		conf.timeParser = jodatime.Parse
		conf.timeLocationParser = jodatime.ParseInLocation
	} else {
		conf.timeParser = time.Parse
		conf.timeLocationParser = time.ParseInLocation
	}
	if conf.Location != "" {
		conf.timeLocation, _ = time.LoadLocation(conf.Location)
	}
	if conf.Source != "" && len(conf.Sources) == 0 {
		conf.Sources = append(conf.Sources, conf.Source)
	}
	return &conf, nil
}

// Event the main filter event
func (f *FilterConfig) Event(ctx context.Context, event logevent.LogEvent) ([]logevent.LogEvent, error) {
	var (
		timestamp time.Time
		err       error
	)
LOOP:
	for _, source := range f.Sources {
		for _, thisFormat := range f.Format {
			if thisFormat == "UNIX" || thisFormat == "UNIXMILLI" || thisFormat == "UNIXNANO" {
				var sec, nsec int64
				value := event.Get(source)
				switch value := value.(type) {
				case float64:
					sec, nsec = convertFloat(value, thisFormat == "UNIXMILLI", thisFormat == "UNIXNANO")
					err = nil
				default:
					sec, nsec, err = convert(event.GetString(source), thisFormat == "UNIXMILLI", thisFormat == "UNIXNANO")
				}
				timestamp = time.Unix(sec, nsec)
			} else if f.timeLocation != nil {
				timestamp, err = f.timeLocationParser(thisFormat, event.GetString(source), f.timeLocation)
			} else {
				timestamp, err = f.timeParser(thisFormat, event.GetString(source))
			}
			if err == nil {
				break LOOP
			}
		}
	}

	if err != nil {
		if !f.IgnoreError {
			event.AddTag(ErrorTag)
			return []logevent.LogEvent{event}, err
		}
		return []logevent.LogEvent{event}, nil
	}
	if f.Target == DefaultTarget {
		event.Timestamp = timestamp.UTC()
	} else {
		event.SetValue(f.Target, timestamp.UTC())
	}

	return []logevent.LogEvent{event}, nil
}

func convertFloat(value float64, isMilli bool, isNano bool) (sec int64, nsec int64) {
	if isMilli {
		sec = int64(value / 1000)
		nsec = int64((value - float64(sec*1000)) * 1e6)
	} else if isNano {
		sec = int64(value / 1e9)
		nsec = int64(value) - sec*1e9
	} else {
		sec = int64(value)
		nsec = int64(value-float64(sec)) * 1e9
	}
	return sec, nsec
}

func convert(s string, isMilli bool, isNano bool) (int64, int64, error) {
	var sec, nsec int64
	var err error
	dot := strings.Index(s, ".")

	if indexOfe := strings.Index(s, "e"); dot == 1 && indexOfe != -1 {
		// looks like exponential notation
		result, err := strconv.ParseFloat(s, 64)
		if err != nil {
			return 0, 0, err
		}
		if isMilli {
			result /= 1000
		} else if isNano {
			result /= 1e9
		}
		sec = int64(result)
		rounded := math.Round((result-float64(sec))*1000) * 1000000 //keep 3 digits
		nsec = int64(rounded)
		return sec, nsec, nil
	}
	if dot >= 0 {
		sec, err = strconv.ParseInt(s[:dot], 10, 64)
		if err != nil {
			return 0, 0, err
		}
		if isMilli {
			tmp := sec / 1000
			nsec = (sec - tmp*1000) * 1000000 //keep 3 digits
			sec = tmp
		} else if isNano {
			tmp := sec / 1e9
			nsec = (sec - tmp*1e9) % 1e9 //keep nanosecond part
			sec = tmp
		} else {
			// fraction to nano seconds, avoid precision loss
			nsec, err = strconv.ParseInt(s[dot+1:], 10, 64)
			if err != nil {
				return 0, 0, err
			}
			nsec *= exponent(10, 9-(len(s)-dot-1))
		}
	} else {
		sec, err = strconv.ParseInt(s, 10, 64)
		if err != nil {
			return 0, 0, err
		}
		if isMilli {
			tmp := sec / 1000
			nsec = (sec - tmp*1000) * 1000000 //keep 3 digits
			sec = tmp
		} else if isNano {
			tmp := sec / 1e9
			nsec = (sec - tmp*1e9) % 1e9 //keep nanosecond part
			sec = tmp
		}
	}
	return sec, nsec, nil
}

func exponent(a int64, n int) int64 {
	result := int64(1)
	for i := n; i > 0; i >>= 1 {
		if i&1 != 0 {
			result *= a
		}
		a *= a
	}
	return result
}
