{"pprofAddr": ":55555", "debug": false, "log": {"localLog": true, "logLevel": "info", "logFile": "./gstash.log", "interval": 24, "maxAge": 30, "maxSize": 15360, "localTime": true}, "mysql": {"default": {"dataSourceName": "sec_gstash_user:i75tV9RGHabq9eW@tcp(sec-db-mysql.cjakwymq67dd.ap-northeast-1.rds.amazonaws.com:3306)/sec_gstash?charset=utf8&timeout=200ms&loc=Local&parseTime=True", "maxIdleConns": 100, "maxOpenConns": 200}}, "redis": {"default": {"servers": ["127.0.0.1:6379"], "cluster": false, "maxRetries": -1}}, "worker": [{"id": "aliyun_ack_audit_log", "name": "aliyun_ack日志清洗", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"type": "sls", "region": "cn-hongkong", "endpoint": "cn-hongkong.log.aliyuncs.com", "accessKeyID": "9MFbOQeGaOVML9pJZtpRbqNMDKC3TEHdaAsgTHOZIE8=", "accessKeySecret": "i3QVdFZJXmvclfewEMubLvtACh50ETnVUWbUKX3aS2s=", "project": "ack-audit-log", "logstoreRegex": ".*", "logGroupMaxCount": 200, "interval": 5, "initialTimestamp": 1735660800, "leaderKey": "aliyun_ack_audit_log"}], "filter": [], "output": [{"type": "file", "path": "./aliyun_ack"}]}]}