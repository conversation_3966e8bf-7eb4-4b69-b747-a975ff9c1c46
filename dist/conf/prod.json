{"pprofAddr": ":12315", "prometheusAddr": ":16670", "debug": false, "dbConfig": false, "hotUpdate": false, "env": "prd", "appId": "security-gstash-svc", "log": {"logLevel": "info", "localLog": true, "logFile": "/root/data/logs/gstash/gstash.log", "interval": 24, "maxAge": 30, "maxSize": 15360, "localTime": true, "kafkaLog": true, "kafka": {"servers": ["**********:9092", "***********:9092", "***********:9092"], "topic": "log_gstash_run_log", "injectHostname": true, "app": "avenir-g<PERSON>sh", "appName": "avenir-gstash-prod", "envName": "prod"}}, "mysql": {"default": {"dataSourceName": "sec_gstash_user:i75tV9RGHabq9eW@tcp(sec-db-mysql.cjakwymq67dd.ap-northeast-1.rds.amazonaws.com:3306)/sec_gstash?charset=utf8&timeout=200ms&loc=Local&parseTime=True", "maxIdleConns": 100, "maxOpenConns": 200}}, "redis": {"default": {"servers": ["clustercfg.sec-prd-redis.iks5cl.memorydb.ap-northeast-1.amazonaws.com:6379"], "cluster": false, "tls": true, "maxRetries": -1}}, "worker": [{"id": "http_listen", "name": "http_listen日志统一接收", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"path": "/", "type": "httplisten", "address": "0.0.0.0:8888", "proto": "ipv4", "decodeQuery": true, "decodeHeader": true}], "filter": [], "output": [{"type": "drop"}]}, {"id": "syslog", "name": "syslog日志统一接收", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"type": "syslog", "proto": "udp4", "address": ":514"}, {"type": "syslog", "proto": "tcp4", "address": ":516"}], "filter": [], "output": [{"type": "cond", "condOutput": [{"condition": "strprefix(content, 'jumpserver') && hostname == '***********'", "output": [{"type": "worker", "workerIDs": ["sec_jumpserver"]}]}, {"condition": "strprefix(content, 'jumpserver') && hostname == '***********'", "output": [{"type": "worker", "workerIDs": ["wallet_jumpserver"]}]}, {"condition": "strprefix(client, '**********')", "output": [{"type": "worker", "workerIDs": ["honeypot_log"]}]}, {"condition": "strprefix(client, '************') || strprefix(client, '************') || strprefix(client, '************')", "output": [{"type": "worker", "workerIDs": ["ningdun_log"]}]}, {"condition": "strcontains(content, 'FGTAWSIESARQ')", "output": [{"type": "worker", "workerIDs": ["fortinet_vpn_log"]}]}, {"condition": "strcontains(content, 'FG201') || strcontains(content, 'FG121') || strcontains(content, 'FG101')", "output": [{"type": "worker", "workerIDs": ["fortigate_it_log"]}]}], "elseOutput": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_gstash_test_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}]}, {"id": "ningdun_log", "name": "宁盾审计日志", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [], "filter": [{"type": "kv", "source": "content", "fieldSplit": ";", "valueSplit": ":"}, {"type": "remove_field", "fields": ["content"]}], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_ningdun_audit_log"]}]}, {"id": "ningdun_log_to_ES", "name": "宁盾日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_ningdun_audit_log"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_ningdun_audit_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "mini_mgt_console_audit_log_to_ES", "name": "mini_mgt_console_audit日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_mini_mgt_console_audit"], "offsetOldest": true, "group": "sec-gstash-prod", "codec": "json"}], "filter": [{"type": "mutate", "replace": [["message", "auditLog||", ""]]}, {"type": "kv", "source": "message", "fieldSplit": "||", "valueSplit": "="}, {"type": "remove_field", "fields": ["level_value", "@version"], "removeMessage": true}], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_mini_mgt_console_audit_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "fortigate_it_log", "name": "IT飞塔防火墙日志", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [], "filter": [{"type": "kv", "source": "content", "fieldSplit": " ", "valueSplit": "=", "trimKey": "\"", "trimValue": "\""}, {"type": "date", "source": "eventtime", "format": ["UNIXNANO"]}, {"type": "remove_field", "fields": ["content"]}], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_fortigate_log_washed"]}]}, {"id": "fortigate_it_log_to_ES", "name": "IT飞塔防火墙日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_fortigate_log_washed"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [{"type": "validate_utf8", "convType": "base64", "fields": ["srcname"]}], "output": [{"type": "cond", "elseOutput": [], "condOutput": [{"condition": "type == 'traffic'", "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_fortigate_traffic_it_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"condition": "type == 'event'", "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_fortigate_event_it_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"condition": "type == 'utm'", "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_fortigate_utm_it_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}]}]}, {"id": "fortinet_vpn_log", "name": "飞塔vpn日志", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [], "filter": [{"type": "kv", "source": "content", "fieldSplit": " ", "valueSplit": "=", "trimKey": "\"", "trimValue": "\""}, {"type": "remove_field", "fields": ["content"]}], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_fortinet_vpn_log_washed"]}]}, {"id": "qb_log_to_ES", "name": "qb日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_qb_prod_falco_audit"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [{"type": "date", "source": "time", "format": ["2006-01-02T15:04:05.999999999Z07:00"]}], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_qb_falco_prod_audit_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "fortinet_vpn_log_to_ES", "name": "飞塔vpn日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_fortinet_vpn_log_washed"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_fortinet_vpn_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "security_nginx_access_log_to_ES", "name": "security_nginx日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_security_nginx_access_log"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [{"type": "cond", "condFilter": [{"condition": "log\\.file\\.path != '/var/log/openresty/aas-web_error.log'", "filter": [{"type": "json", "source": "message", "timestampField": "@timestamp", "timestampFormat": "02/Jan/2006:15:04:05 -0700"}, {"type": "remove_field", "removeMessage": true}]}]}, {"type": "remove_field", "fields": ["@metadata", "input", "ecs", "cloud", "agent", "host"]}], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_security_nginx_access_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "avenir_lark_audit", "name": "avenir_lark_audit日志清洗", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"type": "lark_audit", "codec": "json", "appID": "hQeDrhFpeSkn3V+5xLNjALz+/705b33OiCemZwyESVg=", "appSecret": "Pv+Av6OXRXKbSxvLJ6bIV/vctqiELfZ0qjbKkcO4//vSNYM/0Ia4eRpbT/DXtTP+", "interval": 30, "initialTimestamp": 1748707200, "bufferTime": 300, "leaderKey": "avenir_lark_audit", "offsetKey": "avenir_lark_audit"}], "filter": [{"type": "date", "sources": ["event_time"], "format": ["UNIX"]}], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_lark_audit"]}]}, {"id": "avenir_lark_audit_log_to_ES", "name": "avenir_lark_audit日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_lark_audit"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [], "output": [{"type": "elastic", "version": 8, "action": "update", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_lark_audit_log", "documentId": "%{unique_id}", "suffix": "year", "suffixInterval": 1, "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "huobiinfo_zendesk_log", "name": "huobiinfo_zendesk日志清洗", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"type": "zendesk", "codec": "json", "url": "https://huobiinfosupport.zendesk.com", "email": "<EMAIL>", "token": "j730bTPCXtDoKnxQzMb5wmYcoq0Q6L8X0u2HoqTO", "interval": 60, "leaderKey": "huobiinfo_zendesk_log", "offsetKey": "huobiinfo_zendesk_log"}], "filter": [{"type": "date", "sources": ["timestamp", "created_at"], "format": ["2006-01-02T15:04:05Z07:00"]}], "output": [{"type": "cond", "elseOutput": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_zendesk_audit_log"]}], "condOutput": [{"condition": "log_type == 'access' && user_id == 47663755808409 && (strprefix(url, '/api/v2/audit_logs') || strprefix(url, '/api/v2/access_logs'))", "output": [{"type": "drop"}]}]}]}, {"id": "huobiinfo_zendesk_log_to_ES", "name": "huobiinfo_zendesk日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_zendesk_audit_log"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [], "output": [{"type": "cond", "elseOutput": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_zendesk_audit_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}], "condOutput": [{"condition": "log_type == 'access'", "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_zendesk_access_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}]}]}, {"id": "aliyun_oss_audit_log", "name": "aliyun_oss日志清洗", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"type": "sls", "region": "cn-hongkong", "endpoint": "cn-hongkong.log.aliyuncs.com", "accessKeyID": "9MFbOQeGaOVML9pJZtpRbqNMDKC3TEHdaAsgTHOZIE8=", "accessKeySecret": "i3QVdFZJXmvclfewEMubLvtACh50ETnVUWbUKX3aS2s=", "project": "slsaudit-center-5358279038055445-cn-hongkong", "logstoreRegex": "oss_log", "logGroupMaxCount": 200, "interval": 5, "initialTimestamp": 1748707200, "concurrency": 10, "leaderKey": "aliyun_oss_audit_log"}], "filter": [], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_aliyun_oss_audit"]}]}, {"id": "aliyun_oss_audit_log_to_ES", "name": "aliyun_oss_audit日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_aliyun_oss_audit"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_aliyun_oss_audit_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "aliyun_ack_audit_log", "name": "aliyun_ack日志清洗", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"type": "sls", "region": "cn-hongkong", "endpoint": "cn-hongkong.log.aliyuncs.com", "accessKeyID": "9MFbOQeGaOVML9pJZtpRbqNMDKC3TEHdaAsgTHOZIE8=", "accessKeySecret": "i3QVdFZJXmvclfewEMubLvtACh50ETnVUWbUKX3aS2s=", "project": "ack-audit-log", "logstoreRegex": ".*", "logGroupMaxCount": 200, "interval": 60, "initialTimestamp": 1735660800, "concurrency": 20, "leaderKey": "aliyun_ack_audit_log"}], "filter": [], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_aliyun_ack_audit"]}]}, {"id": "aliyun_ack_audit_log_to_ES", "name": "aliyun_ack_audit日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_aliyun_ack_audit"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_aliyun_ack_audit_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "jamf_pro_to_ES", "name": "jamf_pro日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_jamf_pro_audit"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [{"type": "remove_field", "fields": ["log", "ecs", "agent", "host", "input", "@metadata"]}], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_jamf_pro_audit_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "px_mgt_audit_wash", "name": "px_mgt_audit日志清洗", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["prod-kafka-noti-push-broker-1.mw.yorkapp.com:9092", "prod-kafka-noti-push-broker-2.mw.yorkapp.com:9092", "prod-kafka-noti-push-broker-3.mw.yorkapp.com:9092"], "topics": ["prod_mgt_audit_log"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [{"type": "date", "source": "operatingTime", "format": ["UNIXMILLI"]}, {"type": "codec", "source": "businessModule", "target": "businessModule", "codec": "escape", "action": "decode"}], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_px_mgt_prod_audit_washed"]}]}, {"id": "px_mgt_audit_to_ES", "name": "px_mgt_audit日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_px_mgt_prod_audit_washed"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_px_mgt_audit_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "mini_mysql_log_to_ES", "name": "mini_mysql_audit日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_mini_mysql_audit"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [{"type": "mutate", "replace": [["message", "\n", ""]]}, {"type": "grok", "match": ["%{TIMESTAMP_ISO8601:timestamp}\t\\s*%{NUMBER:session_id} %{DATA:log_type}\t%{GREEDYDATA:sql}"]}, {"type": "date", "source": "timestamp", "format": ["UNIX", "2006-01-02T15:04:05.999999-07:00"]}, {"type": "remove_field", "fields": ["log", "ecs", "agent", "host", "input", "@metadata", "timestamp"], "removeMessage": true}], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_mini_mysql_audit_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "microsoft_entra_id_entity_log_to_ES", "name": "microsoft_entra_id_entity日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_microsoft_entra_id_audit"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [{"type": "remove_field", "fields": ["ecs", "cloud", "agent", "host", "input", "@metadata", "data_stream", "elastic_agent"], "tags": ["forwarded"]}], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_microsoft_entra_id_entity_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "actiontrail_audit_log", "name": "actiontrail日志清洗", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"type": "sls", "region": "cn-hongkong", "endpoint": "cn-hongkong.log.aliyuncs.com", "accessKeyID": "9MFbOQeGaOVML9pJZtpRbqNMDKC3TEHdaAsgTHOZIE8=", "accessKeySecret": "i3QVdFZJXmvclfewEMubLvtACh50ETnVUWbUKX3aS2s=", "project": "avenir-console-actiontrail", "logstore": "actiontrail_console-audit-logs", "logGroupMaxCount": 200, "interval": 3, "initialTimestamp": 1735660800, "leaderKey": "actiontrail_audit_log"}], "filter": [{"type": "json", "source": "event"}, {"type": "remove_field", "fields": ["event", "requestParameters", "apiVersion"]}, {"type": "typeconv", "convType": "string", "fields": ["additionalEventData"]}, {"type": "mutate", "rename": [["additionalEventData", "additionalEventDataJson"]]}], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_actiontrail_console_audit"]}]}, {"id": "actiontrail_log_to_ES", "name": "actiontrail日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_actiontrail_console_audit"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_actiontrail_audit_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "aliyun_bastion_audit_log", "name": "阿里云bastion日志清洗", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"type": "sls", "region": "cn-hongkong", "endpoint": "cn-hongkong.log.aliyuncs.com", "accessKeyID": "9MFbOQeGaOVML9pJZtpRbqNMDKC3TEHdaAsgTHOZIE8=", "accessKeySecret": "i3QVdFZJXmvclfewEMubLvtACh50ETnVUWbUKX3aS2s=", "project": "slsaudit-center-5358279038055445-cn-hongkong", "logstore": "bastion_log", "logGroupMaxCount": 200, "interval": 2, "initialTimestamp": 1735660800, "leaderKey": "aliyun_bastion_log"}], "filter": [], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_aliyun_bastion_audit"]}]}, {"id": "aliyun_bastion_log_to_ES", "name": "阿里云bastion日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_aliyun_bastion_audit"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_aliyun_bastion_audit_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "quickbi_log_to_ES", "name": "quickbi日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_aliyun_quickbi_audit"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_aliyun_quickbi_audit_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "honeypot_log", "name": "蜜罐日志", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [], "filter": [{"type": "json", "source": "content"}, {"type": "date", "source": "timestamp", "format": ["2006-01-02 15:04:05 -0700 MST"]}, {"type": "remove_field", "fields": ["content", "facility", "severity", "priority", "tls_peer"], "removeMessage": true}, {"type": "cond", "condFilter": [{"condition": "!empty(DLLSignatureTime) && isstring(DLLSignatureTime) && strlen(DLLSignatureTime) < 1", "filter": [{"type": "remove_field", "fields": ["DLLSignatureTime"]}]}]}], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_honeypot_log_washed"]}]}, {"id": "honeypot_log_to_ES", "name": "蜜罐日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_honeypot_log_washed"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_honeypot_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "sec_jumpserver", "name": "安全prd跳板机日志清洗", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [], "filter": [{"type": "grok", "source": "content", "match": ["jumpserver: %{DATA:log_type} - %{GREEDYDATA:message}"]}, {"type": "json"}, {"type": "date", "source": "timestamp", "format": ["UNIX", "2006-01-02T15:04:05Z07:00", "2006-01-02 15:04:05 -0700 MST"]}, {"type": "remove_field", "fields": ["content", "facility", "severity", "priority", "tls_peer", "timestamp"], "removeMessage": true}], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_security_jumpserver_log_washed"]}]}, {"id": "sec_jumpserver_to_ES", "name": "安全prd跳板机日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_security_jumpserver_log_washed"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_jumpserver_security_audit_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "wallet_jumpserver", "name": "wallet_prd跳板机日志清洗", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [], "filter": [{"type": "grok", "source": "content", "match": ["jumpserver: %{DATA:log_type} - %{GREEDYDATA:message}"]}, {"type": "json"}, {"type": "date", "source": "timestamp", "format": ["UNIX", "2006-01-02T15:04:05Z07:00", "2006-01-02 15:04:05 -0700 MST"]}, {"type": "remove_field", "fields": ["content", "facility", "severity", "priority", "tls_peer", "timestamp"], "removeMessage": true}], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_wallet_jumpserver_log_washed"]}]}, {"id": "wallet_jumpserver_to_ES", "name": "wallet_prd跳板机日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_wallet_jumpserver_log_washed"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_jumpserver_wallet_audit_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "avenir_jumpserver_log", "name": "avenir_jumpserver日志清洗", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_avenir_jumpserver_log"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [{"type": "date", "sources": ["timestamp", "ntime"], "format": ["UNIX", "2006-01-02T15:04:05.999999999Z07:00"]}], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_avenir_jumpserver_log_washed"]}]}, {"id": "avenir_jumpserver_log_to_ES", "name": "avenir_jumpserver日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_avenir_jumpserver_log_washed"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_jumpserver_avenir_audit_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "deeptrading_jumpserver_log", "name": "deeptrading_jumpserver日志清洗", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_deeptrading_jumpserver_log"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [{"type": "cond", "condFilter": [{"condition": "!empty(message) && isstring(message) && strcontains(message,'{') ", "filter": [{"type": "grok", "match": ["%{TIMESTAMP_ISO8601:timestamp} %{IPV4:host_ip} jumpserver: %{DATA:log_type} - %{GREEDYDATA:message}"]}, {"type": "json"}, {"type": "date", "source": "timestamp", "format": ["UNIX", "2006-01-02T15:04:05.999999-07:00", "2006-01-02T15:04:05Z07:00", "2006-01-02 15:04:05 -0700 MST"]}, {"type": "remove_field", "fields": ["log", "cloud", "ecs", "agent", "host", "fields", "input", "@metadata", "host_ip", "timestamp"], "removeMessage": true}]}], "elseFilter": [{"type": "grok", "match": ["%{TIMESTAMP_ISO8601:timestamp} %{IPV4:host_ip} jumpserver: %{GREEDYDATA:message}"]}, {"type": "date", "source": "timestamp", "format": ["2006-01-02T15:04:05.999999-07:00"]}, {"type": "remove_field", "fields": ["log", "cloud", "ecs", "agent", "host", "fields", "input", "@metadata", "host_ip", "timestamp"]}]}], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_deeptrading_jumpserver_log_washed"]}]}, {"id": "deeptrading_jumpserver_log_to_ES", "name": "deeptrading_jumpserver日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_deeptrading_jumpserver_log_washed"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_jumpserver_deeptrading_audit_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "aws_dns_log", "name": "aws_dns日志清洗", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"type": "s3", "codec": "json", "region": "ap-northeast-1", "bucket": "hids-time-machine", "prefix": "dns_query_log/AWSLogs", "archivePrefix": "archived/", "decompress": "gzip", "limit": 1000, "queryInterval": 30, "ignoreBeforeTime": 0, "leaderKey": "dns_query_log", "leaderExpire": 60, "leaderInterval": 10, "syncingKeyExpireTime": 180, "syncedKeyExpireTime": 600}], "filter": [{"type": "date", "source": "query_timestamp", "ignoreError": true, "format": ["2006-01-02T15:04:05Z07:00"]}], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_aws_dns_log_washed"]}]}, {"id": "aws_dns_log_to_ES", "name": "aws_dns日志入ES", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_aws_dns_log_washed"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_dns_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "aws_cloudtrail_log", "name": "aws_cloudtrail日志清洗", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"type": "s3", "codec": "json", "region": "ap-northeast-1", "bucket": "cloudtrail-all-account-logs", "prefix": "AWSLogs", "archivePrefix": "archived/", "limit": 1000, "queryInterval": 30, "ignoreBeforeTime": 0, "leaderKey": "cloudtrail_log", "leaderExpire": 60, "leaderInterval": 10, "syncingKeyExpireTime": 180, "syncedKeyExpireTime": 600}], "filter": [{"type": "expand", "source": "Records", "keepFields": ["aws_s3_object_key"]}, {"type": "date", "source": "eventTime", "ignoreError": true, "format": ["2006-01-02T15:04:05Z07:00"]}, {"type": "date", "source": "digestEndTime", "ignoreError": true, "format": ["2006-01-02T15:04:05Z07:00"]}], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_aws_cloudtrail_log_washed"]}]}, {"id": "aws_cloudtrail_log_to_ES", "name": "aws_cloudtrail日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_aws_cloudtrail_log_washed"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [{"type": "remove_field", "fields": ["apiVersion"]}, {"type": "typeconv", "convType": "string", "fields": ["requestParameters", "responseElements"]}], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_cloudtrail_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "aws_vpc_log", "name": "aws_vpc日志清洗", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"type": "s3", "region": "ap-northeast-1", "bucket": "vpcflow-all-account-logs", "prefix": "AWSLogs", "archivePrefix": "archived/", "limit": 1000, "queryInterval": 30, "ignoreBeforeTime": 86400, "leaderKey": "vpc_log", "leaderExpire": 60, "leaderInterval": 10, "decompress": "gzip", "syncingKeyExpireTime": 180, "syncedKeyExpireTime": 600}], "filter": [{"type": "grok", "match": ["%{NUMBER:vpclog_version} %{DATA:account_id} %{DATA:eni_id} %{DATA:src_ip} %{DATA:dst_ip} %{DATA:src_port} %{DATA:dst_port} %{DATA:portocol} %{DATA:packets} %{DATA:bytes} %{NUMBER:start_time} %{NUMBER:end_time} %{DATA:action} %{DATA:conn_status}"], "source": "message", "dropError": true}, {"type": "date", "source": "start_time", "target": "start_time", "format": ["UNIX"]}, {"type": "date", "source": "end_time", "target": "end_time", "format": ["UNIX"]}, {"type": "mutate", "copy": [["end_time", "@timestamp"]]}], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_aws_vpc_log_washed"]}]}, {"id": "aws_vpc_log_to_ES", "name": "aws_vpc日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_aws_vpc_log_washed"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_vpc_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "security_hub", "name": "aws_security_hub日志清洗", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_aws_security_hub_log"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [{"type": "json"}, {"type": "remove_field", "fields": ["@metadata", "input", "cloud", "event", "elastic_agent", "agent", "ecs", "log", "data_stream"], "tags": ["forwarded"], "removeMessage": true}, {"type": "date", "source": "time", "format": ["2006-01-02T15:04:05Z07:00"]}], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_aws_security_hub_log_washed"]}]}, {"id": "security_hub_log_to_ES", "name": "security_hub日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_aws_security_hub_log_washed"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_security_hub_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "crowdstrike_log", "name": "crowdstrike日志清洗", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_crowdstrike_log"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [{"type": "remove_field", "tags": ["preserve_original_event", "forwarded", "crowdstrike-fdr"], "fields": ["@metadata", "elastic_agent", "agent", "log", "input", "ecs", "cloud", "tags", "aws", "data_stream", "event"]}, {"type": "json"}, {"type": "remove_field", "removeMessage": true}, {"type": "cond", "condFilter": [{"condition": "!empty(timestamp) && isstring(timestamp) && strlen(timestamp) > 0", "filter": [{"type": "date", "source": "timestamp", "format": ["UNIXMILLI", "2006-01-02T15:04:05Z07:00"]}]}], "elseFilter": [{"type": "date", "source": "_time", "ignoreError": true, "format": ["UNIX"]}, {"type": "date", "source": "Time", "ignoreError": true, "format": ["UNIX"]}]}], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_crowdstrike_log_washed"]}]}, {"id": "crowdstrike_log_to_ES", "name": "crowdstrike日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_crowdstrike_log_washed"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_crowdstrike_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "crowdstrike_alert", "name": "crowdstrike报警日志", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_crowdstrike_alert"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [{"type": "json", "appendKey": "parsedMessage"}, {"type": "expand", "source": "parsedMessage"}, {"type": "remove_field", "tags": ["preserve_duplicate_custom_fields", "forwarded", "crowdstrike-alert"], "removeMessage": true}, {"type": "date", "source": "crawled_timestamp", "format": ["2006-01-02T15:04:05.999999999Z07:00"]}], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_crowdstrike_alert_washed"]}]}, {"id": "crowdstrike_alert_to_ES", "name": "crowdstrike_alert日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_crowdstrike_alert_washed"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [], "output": [{"type": "elastic", "version": 8, "action": "update", "documentId": "%{composite_id}", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_crowdstrike_alert", "suffix": "year", "suffixInterval": 1, "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "aws_inspector_log", "name": "aws_inspector日志", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_aws_inspector_log"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [{"type": "json", "appendKey": "parsedMessage"}, {"type": "expand", "source": "parsedMessage"}, {"type": "remove_field", "tags": ["forwarded"], "removeMessage": true}, {"type": "date", "source": "time", "format": ["2006-01-02T15:04:05Z07:00"]}], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_aws_inspector_log_washed"]}]}, {"id": "aws_inspector_log_to_ES", "name": "aws_inspector日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_aws_inspector_log_washed"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [], "output": [{"type": "elastic", "version": 8, "action": "create", "documentId": "%{id}", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_inspector_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "cloudflare_firewall_events", "name": "cloudflare_firewall_events日志", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"type": "cloudflare", "url": "https://api.cloudflare.com/client/v4/graphql", "token": "****************************************", "interval": 5, "timeout": 20, "step": 120, "offsetKey": "gstash_cloudflare_firewall_events_offset_key", "leaderKey": "gstash_cloudflare_firewall_events_leader_key", "leaderExpire": 30, "leaderInterval": 10, "codec": "json"}], "filter": [{"type": "date", "source": "datetime", "format": ["2006-01-02T15:04:05Z07:00"]}], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_cloudflare_firewall_events_log"]}]}, {"id": "cloudflare_firewall_events_to_ES", "name": "cloudflare_firewall_events日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_cloudflare_firewall_events_log"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [], "output": [{"type": "elastic", "version": 8, "action": "update", "documentId": "%{rayName}", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_cloudflare_firewall_events_log", "suffix": "month", "suffixInterval": 1, "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "aws_guardduty_log", "name": "aws_guardduty日志", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_aws_guardduty_log"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [{"type": "json", "appendKey": "parsedMessage"}, {"type": "expand", "source": "parsedMessage"}, {"type": "remove_field", "tags": ["forwarded"], "removeMessage": true}, {"type": "date", "source": "time", "format": ["2006-01-02T15:04:05Z07:00"]}], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_aws_guardduty_log_washed"]}]}, {"id": "aws_guardduty_log_to_ES", "name": "guardduty_log日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_aws_guardduty_log_washed"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [], "output": [{"type": "elastic", "version": 8, "action": "create", "documentId": "%{id}", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_guardduty_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "hids_log", "name": "hids日志", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["10.2.34.14:9092", "10.2.34.244:9092", "10.2.34.245:9092"], "securityProtocol": "SASL", "saslUsername": "admin", "saslPassword": "el<PERSON><PERSON>", "topics": ["hids_raw"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [{"type": "date", "source": "time_pkg", "format": ["UNIX"]}, {"type": "strmap", "source": "data_type", "target": "source", "map": {"1": "Driver Plugin", "2": "Driver Plugin", "10": "Driver Plugin", "35": "Driver Plugin", "42": "Driver Plugin", "43": "Driver Plugin", "49": "Driver Plugin", "59": "Driver Plugin", "60": "Driver Plugin", "62": "Driver Plugin", "82": "Driver Plugin", "86": "Driver Plugin", "90": "Driver Plugin", "101": "Driver Plugin", "112": "Driver Plugin", "157": "Driver Plugin", "165": "Driver Plugin", "200": "Driver Plugin", "231": "Driver Plugin", "356": "Driver Plugin", "601": "Driver Plugin", "602": "Driver Plugin", "603": "Driver Plugin", "604": "Driver Plugin", "605": "Driver Plugin", "606": "Driver Plugin", "607": "Driver Plugin", "608": "Driver Plugin", "609": "Driver Plugin", "610": "Driver Plugin", "611": "Driver Plugin", "700": "Driver Plugin", "701": "Driver Plugin", "702": "Driver Plugin", "703": "Driver Plugin", "1000": "Elkeid Agent", "1001": "Elkeid Agent", "1010": "Elkeid Agent", "2010": "Elkeid RASP", "2011": "Elkeid RASP", "2459": "Elkeid RASP", "4000": "Journal Watcher", "4001": "Journal Watcher", "5050": "Collector Plugin", "5051": "Collector Plugin", "5052": "Collector Plugin", "5053": "Collector Plugin", "5054": "Collector Plugin", "5055": "Collector Plugin", "5056": "Collector Plugin", "5057": "Collector Plugin", "5058": "Collector Plugin", "5059": "Collector Plugin", "5060": "Collector Plugin", "5062": "Collector Plugin", "5063": "Collector Plugin", "5064": "Collector Plugin", "8100": "Collector Plugin", "8101": "Collector Plugin", "6001": "Scanner<PERSON><PERSON><PERSON>", "6002": "Scanner<PERSON><PERSON><PERSON>", "6003": "Scanner<PERSON><PERSON><PERSON>", "6021": "ScannerCloud Plugin", "6023": "ScannerCloud Plugin", "6024": "ScannerCloud Plugin", "6000": "ScannerCloud Plugin", "6201": "File Monitor", "6301": "Etrace", "9001": "<PERSON><PERSON><PERSON><PERSON>"}}, {"type": "strmap", "source": "data_type", "target": "description", "map": {"1": "Write", "2": "Open", "10": "Mprotect", "35": "Nanosleep", "42": "Connect", "43": "Accept", "49": "Bind", "59": "Execve", "60": "Exit", "62": "Kill", "82": "<PERSON><PERSON>", "86": "Link", "90": "Chmod", "101": "Ptrace", "112": "SetSid", "157": "Prctl", "165": "Mount", "200": "tkill", "231": "exit_group", "356": "MemfdCreate", "601": "DNS Query", "602": "CreateFile", "603": "LoadModule", "604": "CommitCred", "605": "unlink", "606": "rmdir", "607": "call_usermode", "608": "ReadFile", "609": "WriteFile", "610": "USB Event", "611": "PrivilegeEscalation", "700": "ProcFileHook", "701": "SyscallHook", "702": "LkmHidden", "703": "InterruptsHook", "1000": "Agent Heartbeat", "1001": "Plugin Heartbeat", "1010": "Agent <PERSON><PERSON><PERSON>", "2010": "Command", "2011": "Report", "2459": "Hook", "4000": "SSH Login", "4001": "<PERSON><PERSON><PERSON>", "5050": "Process(linux/win)", "5051": "Port(linux/win)", "5052": "User(linux/win)", "5053": "<PERSON><PERSON>(linux/win)", "5054": "Service(linux/win)", "5055": "Software(linux/win)", "5056": "Container(linux)", "5057": "Integrity(linux)", "5058": "Volume(linux/win)", "5059": "Net Interface(linux/win)", "5060": "App(linux)", "5062": "kmod(linux)", "5063": "pgid_argv", "5064": "vulnerability", "8100": "Weak Passwd(1.0.0.165)(linux)", "8101": "unhide", "6001": "Dir Match", "6002": "Proc Match", "6003": "<PERSON>an <PERSON>", "6021": "<PERSON><PERSON><PERSON><PERSON>", "6023": "Task Di<PERSON>", "6024": "Task Full scan", "6000": "Task Flag", "6201": "File Monitor", "6301": "Ring3Hook", "9001": "<PERSON><PERSON><PERSON><PERSON>"}}, {"type": "validate_utf8", "convType": "base64", "fields": ["new_name", "comm", "ppid_argv", "argv", "pgid_argv"]}], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_hids_raw_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "hids_sys_log", "name": "hids_sys日志", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["10.2.34.14:9092", "10.2.34.244:9092", "10.2.34.245:9092"], "securityProtocol": "SASL", "saslUsername": "admin", "saslPassword": "el<PERSON><PERSON>", "topics": ["hids_sys_log"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [{"type": "date", "source": "timestamp", "format": ["2006-01-02T15:04:05Z07:00"]}, {"type": "mutate", "rename": [["_id", "id"]]}], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_hids_sys_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "gstash_run_log", "name": "gstash运行日志写ES", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_gstash_run_log"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_gstash_run_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}, {"type": "prometheus", "counterVecs": [{"namespace": "sec", "subsystem": "gstash", "name": "run_log_count", "help": "gstash run log count by level", "labelToField": {"level": "level", "env": "env_name"}}]}]}, {"id": "fuse_run_log", "name": "fuse运行日志写ES", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_fuse_run_log"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_fuse_run_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}, {"type": "prometheus", "counterVecs": [{"namespace": "sec", "subsystem": "fuse", "name": "run_log_count", "help": "fuse run log count by level", "labelToField": {"level": "level", "env": "env_name"}}]}]}, {"id": "aas_run_log", "name": "aas运行日志写ES", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_aas_run_log"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [{"type": "mutate", "rename": [["host.hostname", "hostname"], ["log.file.path", "file"]]}, {"type": "grok", "match": ["\\[%{WORD:log_level}\\] %{TIMESTAMP_ISO8601:timestamp}\\|\\|%{GREEDYDATA:message}"], "source": "message", "dropError": true}, {"type": "date", "source": "timestamp", "format": ["2006-01-02 15:04:05.999-0700"]}, {"type": "kv", "source": "message", "fieldSplit": "||", "valueSplit": "="}, {"type": "remove_field", "fields": ["@metadata", "input", "ecs", "agent", "log", "cloud", "timestamp"], "removeMessage": true}, {"type": "json", "source": "request_body", "appendKey": "parsed_request_body"}], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_aas_run_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "cicd_run_log", "name": "cicd运行日志写ES", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_cicd_verify_run_log"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_cicd_verify_run_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}, {"type": "prometheus", "counterVecs": [{"namespace": "sec", "subsystem": "cicd", "name": "run_log_count", "help": "cicd run log count by level", "labelToField": {"level": "level", "env": "env_name"}}]}]}, {"id": "confluence_avenir", "name": "confluence_avenir日志清洗", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_avenir_confluence_log"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [{"type": "date", "source": "timestamp.epochSecond", "format": ["UNIX"]}], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_avenir_confluence_log_washed"]}]}, {"id": "confluence_avenir_to_ES", "name": "confluence_avenir日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_avenir_confluence_log_washed"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_confluence_avenir_audit_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "bitbucket_avenir", "name": "bitbucket_avenir日志清洗", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_avenir_bitbucket_log"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [{"type": "cond", "condFilter": [{"condition": "log_path == '/data/bitbucket/log/atlassian-bitbucket-access.log' && strcontains(log, '| ssh |')", "filter": [{"type": "grok", "match": ["%{DATA:src_ip} \\| %{WORD:protocol} \\| %{DATA:request_id} \\| %{DATA:user} \\| %{TIMESTAMP_ISO8601:timestamp} \\| SSH - %{DATA:command} '%{DATA:repository}' \\| \"%{DATA:user_agent}\" \\| .+ \\| .+ \\| .+ \\| .+ \\| .+ \\| %{DATA:log_id} \\|", "%{DATA:src_ip} \\| %{WORD:protocol} \\| %{DATA:request_id} \\| %{DATA:user} \\| %{TIMESTAMP_ISO8601:timestamp} \\| SSH - %{DATA:command} %{DATA:repository} %{DATA:action} \\| \"%{DATA:user_agent}\" \\| .+ \\| .+ \\| .+ \\| .+ \\| .+ \\| %{DATA:log_id} \\|"], "source": "log"}, {"type": "date", "source": "timestamp", "ignoreError": true, "format": ["2006-01-02 15:04:05,999"], "location": "Asia/Shanghai"}]}, {"condition": "log_path == '/data/bitbucket/log/atlassian-bitbucket-access.log'", "filter": [{"type": "grok", "match": ["%{DATA:src_ip} \\| %{WORD:protocol} \\| %{DATA:request_id} \\| %{DATA:user} \\| %{TIMESTAMP_ISO8601:timestamp} \\| \"%{DATA:request_all}\" \\| \"%{DATA:referrer}\" \"%{DATA:user_agent}\" \\| %{DATA:response_status} \\| .+ \\| %{DATA:response_size} \\| .+ \\| .+ \\| %{DATA:log_id} \\|"], "source": "log"}, {"type": "date", "source": "timestamp", "ignoreError": true, "format": ["2006-01-02 15:04:05,999"], "location": "Asia/Shanghai"}]}, {"condition": "log_path == '/data/bitbucket/log/atlassian-bitbucket.log'", "filter": [{"type": "grok", "match": ["%{TIMESTAMP_ISO8601:timestamp} %{LOGLEVEL:log_level}  \\[%{DATA:thread_name}\\] %{EMAILADDRESS:user} %{DATA:request_id} %{GREEDYDATA:src_ip} \"%{WORD:request_type} %{URIPATH:request_uri} %{DATA:http_version}\" %{DATA:java_classname} %{GREEDYDATA:error_log}"], "source": "log", "dropError": true}, {"type": "date", "source": "timestamp", "ignoreError": true, "format": ["2006-01-02 15:04:05,999"], "location": "Asia/Shanghai"}]}, {"condition": "strcontains(log_path,'audit.log')", "filter": [{"type": "json", "source": "log"}, {"type": "date", "source": "timestamp.epochSecond", "format": ["UNIX"]}, {"type": "remove_field", "fields": ["log", "timestamp"]}]}]}], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_avenir_bitbucket_log_washed"]}]}, {"id": "bitbucket_avenir_to_ES", "name": "bitbucket_avenir日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_avenir_bitbucket_log_washed"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_bitbucket_avenir_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "gitlab_avenir", "name": "gitlab_avenir日志清洗", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_avenir_gitlab_log"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [{"type": "cond", "condFilter": [{"condition": "log_path == '/var/log/gitlab/gitlab-rails/application.log' && strcontains(log, 'Instantiating Gitlab::Auth::Ldap::Person with LDIF')", "filter": [{"type": "grok", "match": ["^%{TIMESTAMP_ISO8601:timestamp}: Instantiating Gitlab::Auth::Ldap::Person with LDIF:\\ndn:\\s+cn=(?<cn>[^,]+),(?<ou_list>(ou=[^,]+,?)+),(?<dc_list>(dc=[^,]+,?)+)\\n(?:cn:: (?<cn_base64>[A-Za-z0-9+/=]+)|cn: (?<cn_text>[^\\n]+))\\nentrydn:: (?<entrydn_base64>[A-Za-z0-9+/=\\n ]+)\\nmail: (?<mail>[^\\n]+)\\nsamaccountname: (?<samaccountname>[^\\n]+)"], "source": "log"}, {"type": "date", "source": "timestamp", "format": ["2006-01-02T15:04:05.999Z07:00"]}]}, {"condition": "log_path == '/var/log/gitlab/gitlab-rails/application.log' && !strcontains(log, 'Instantiating Gitlab::Auth::Ldap::Person with LDIF')", "filter": [{"type": "grok", "match": ["^%{TIMESTAMP_ISO8601:timestamp}: %{GREEDYDATA:log_content}"], "source": "log"}, {"type": "date", "source": "timestamp", "format": ["2006-01-02T15:04:05.999Z07:00"]}]}, {"condition": "!strprefix(log, '{')", "filter": [{"type": "grok", "match": ["%{IP:client_ip} - %{DATA:user} \\[%{HTTPDATE:timestamp}\\] \"%{WORD:method} %{URIPATH:path_uri}?%{GREEDYDATA:query_string} %{DATA:protocol}\" %{NUMBER:status_code} %{NUMBER:response_size} \"%{DATA:referrer}\" \"%{DATA:user_agent}\" (?:%{NUMBER:response_time}|-)"], "source": "log"}, {"type": "date", "source": "timestamp", "format": ["02/Jan/2006:15:04:05 -0700"]}]}, {"condition": "log_path == '/var/log/gitlab/gitlab-rails/production_json.log' || log_path == '/var/log/gitlab/gitlab-rails/api_json.log'", "filter": [{"type": "json", "source": "log"}, {"type": "remove_field", "fields": ["queue_duration_s", "request_urgency", "target_duration_s", "redis_calls", "redis_cache_duration_s", "redis_sessions_read_bytes", "redis_sessions_duration_s", "redis_cache_calls", "redis_sessions_write_bytes", "redis_sessions_calls", "redis_cache_write_bytes", "redis_duration_s", "redis_read_bytes", "redis_write_bytes", "redis_feature_flag_calls", "redis_feature_flag_duration_s", "redis_feature_flag_read_bytes", "redis_feature_flag_write_bytes", "db_count", "db_write_count", "db_cached_count", "db_txn_count", "db_replica_txn_count", "db_primary_txn_count", "db_replica_count", "db_primary_count", "db_replica_write_count", "db_primary_write_count", "db_replica_cached_count", "db_primary_cached_count", "db_replica_wal_count", "db_primary_wal_count", "db_replica_wal_cached_count", "db_primary_wal_cached_count", "db_replica_txn_max_duration_s", "db_primary_txn_max_duration_s", "db_replica_txn_duration_s", "db_primary_txn_duration_s", "db_replica_duration_s", "db_primary_duration_s", "db_main_txn_count", "db_ci_txn_count", "db_main_replica_txn_count", "db_ci_replica_txn_count", "db_main_count", "db_ci_count", "db_main_replica_count", "db_ci_replica_count", "db_main_write_count", "db_ci_write_count", "db_main_replica_write_count", "db_ci_replica_write_count", "db_main_cached_count", "db_ci_cached_count", "db_main_replica_cached_count", "db_ci_replica_cached_count", "db_main_wal_count", "db_ci_wal_count", "db_main_replica_wal_count", "db_ci_replica_wal_count", "db_main_wal_cached_count", "db_ci_wal_cached_count", "db_main_replica_wal_cached_count", "db_ci_replica_wal_cached_count", "db_main_txn_max_duration_s", "db_ci_txn_max_duration_s", "db_main_replica_txn_max_duration_s", "db_ci_replica_txn_max_duration_s", "db_main_txn_duration_s", "db_ci_txn_duration_s", "db_main_replica_txn_duration_s", "db_ci_replica_txn_duration_s", "db_main_duration_s", "db_ci_duration_s", "db_main_replica_duration_s", "db_ci_replica_duration_s", "cpu_s", "mem_objects", "mem_bytes", "mem_mallocs", "mem_total_bytes", "pid", "worker_id", "rate_limiting_gates", "db_duration_s", "view_duration_s", "duration_s", "redis_db_load_balancing_write_bytes", "redis_db_load_balancing_calls", "redis_allowed_cross_slot_calls", "redis_repository_cache_calls", "redis_sessions_allowed_cross_slot_calls", "redis_db_load_balancing_duration_s", "graphql", "redis_repository_cache_duration_s", "redis_repository_cache_read_bytes", "redis_repository_cache_write_bytes", "params"]}, {"type": "date", "source": "time", "format": ["2006-01-02T15:04:05.999999999Z07:00"]}]}], "elseFilter": [{"type": "json", "source": "log"}, {"type": "remove_field", "fields": ["log"]}, {"type": "date", "source": "time", "format": ["2006-01-02T15:04:05Z07:00"]}]}], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_avenir_gitlab_log_washed"]}]}, {"id": "gitlab_avenir_to_ES", "name": "gitlab_avenir日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_avenir_gitlab_log_washed"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_gitlab_avenir_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "exchange_audit", "name": "exchange_audit日志清洗", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_exchange_mailflow_audit"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [{"type": "json"}, {"type": "remove_field", "fields": ["@metadata", "input", "ecs", "cloud", "agent", "elastic_agent"], "removeMessage": true}, {"type": "remove_duplicate", "db": "redis", "window": 3600, "fields": ["MessageId", "Recipient<PERSON>ddress", "Subject", "Received", "Status", "Size"]}], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_exchange_mailflow_audit_washed"]}]}, {"id": "exchange_audit_to_ES", "name": "exchange_audit日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_exchange_mailflow_audit_washed"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_exchange_mailflow_audit_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "office365_audit", "name": "office365_audit日志清洗", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_office365_audit"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [{"type": "remove_field", "fields": ["@metadata", "input", "ecs", "cloud", "agent", "elastic_agent"]}, {"type": "cond", "condFilter": [{"condition": "length(o365audit\\.ModifiedProperties) > 0 && isstring(o365audit\\.ModifiedProperties\\[0\\])", "filter": [{"type": "mutate", "rename": [["o365audit.ModifiedProperties", "o365audit.ModifiedPropertiesNames"]]}]}]}], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_office365_audit_washed"]}]}, {"id": "office365_audit_to_ES", "name": "office365_audit日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_office365_audit_washed"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [{"type": "typeconv", "convType": "string", "fields": ["o365audit.ListBaseType", "o365audit.Parameters"]}], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_office365_audit_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "mini_app_system_to_ES", "name": "mini_app_system日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_mini_app_system_audit"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [{"type": "date", "sources": ["create_time", "f_created_at", "gmt_create", "datachange_createdtime"], "format": ["2006-01-02T15:04:05Z07:00", "UNIXMILLI"]}, {"type": "typeconv", "convType": "string", "fields": ["ip"]}], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_mini_app_system_audit_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "mini_hue_to_ES", "name": "mini_hue日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_mini_hue_audit"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [{"type": "mutate", "rename": [["host.hostname", "hostname"], ["log.file.path", "file"]]}, {"type": "remove_field", "fields": ["@metadata", "input", "ecs", "host", "agent", "log"]}], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_mini_hue_audit_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "aws_eks_audit_to_ES", "name": "aws_eks_audit日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_aws_eks_audit"], "group": "sec-gstash-prod"}], "filter": [{"type": "mutate", "replace": [["message", "\".\":{},", ""]]}, {"type": "json", "timestampField": "stageTimestamp", "timestampFormat": "2006-01-02T15:04:05Z07:00", "removeMessage": true}, {"type": "typeconv", "convType": "string", "fields": ["requestObject", "responseObject"]}], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_aws_eks_audit_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "archery_audit_log_test", "name": "archery_audit_log测试环境日志", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"type": "mysql", "dataSourceName": "security_audit_r:Security#9876yhn@tcp(master-3306.db.yorkapp.com:3306)/archery?charset=utf8mb4&timeout=200ms&loc=Local&parseTime=True", "tableName": "audit_log", "redisKey": "archery_audit_log_test", "select": "*", "queryInterval": 30, "leaderExpire": 60, "leaderInterval": 10, "offsetField": "id", "offsetFieldType": "int64", "limit": 100, "codec": "json"}], "filter": [{"type": "date", "source": "action_time", "format": ["2006-01-02T15:04:05.999999Z07:00"], "location": "Asia/Shanghai"}, {"type": "add_field", "key": "log_type", "value": "audit_log"}], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_archery_test_log"]}]}, {"id": "archery_query_log_test", "name": "archery_query_log测试环境日志", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"type": "mysql", "dataSourceName": "security_audit_r:Security#9876yhn@tcp(master-3306.db.yorkapp.com:3306)/archery?charset=utf8mb4&timeout=200ms&loc=Local&parseTime=True", "tableName": "query_log", "redisKey": "archery_query_log_test", "select": "*", "queryInterval": 30, "leaderExpire": 60, "leaderInterval": 10, "offsetField": "id", "offsetFieldType": "int64", "limit": 100, "codec": "json"}], "filter": [{"type": "date", "source": "create_time", "format": ["2006-01-02T15:04:05.999999Z07:00"], "location": "Asia/Shanghai"}, {"type": "add_field", "key": "log_type", "value": "query_log"}], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_archery_test_log"]}]}, {"id": "archery_workflow_log_test", "name": "archery_workflow_log测试环境日志", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"type": "mysql", "dataSourceName": "security_audit_r:Security#9876yhn@tcp(master-3306.db.yorkapp.com:3306)/archery?charset=utf8mb4&timeout=200ms&loc=Local&parseTime=True", "tableName": "sql_workflow AS a", "redisKey": "archery_workflow_log_test", "select": "a.id, a.workflow_name, CASE WHEN a.syntax_type = 1 THEN 'DDL' WHEN a.syntax_type = 2 THEN 'DML' END AS syntax_type_name, a.engineer, a.engineer_display, a.status, a.is_backup, a.create_time, b.instance_name, b.host, b.port, b.db_type, a.db_name, c.group_name", "joins": ["JOIN sql_instance b ON a.instance_id = b.id", "JOIN resource_group c ON a.group_id = c.group_id"], "queryInterval": 30, "leaderExpire": 60, "leaderInterval": 10, "offsetField": "a.id", "offsetFieldInEvent": "id", "offsetFieldType": "int64", "limit": 100, "codec": "json"}], "filter": [{"type": "date", "source": "create_time", "format": ["2006-01-02T15:04:05.999999Z07:00"], "location": "Asia/Shanghai"}, {"type": "add_field", "key": "log_type", "value": "sql_workflow"}], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_archery_test_log"]}]}, {"id": "archery_audit_log_prod", "name": "archery_audit_log生产环境日志", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"type": "mysql", "dataSourceName": "security_audit_r:Security#9876yhn@tcp(master-3307.db.yorkapp.com:3307)/archery?charset=utf8mb4&timeout=200ms&loc=Local&parseTime=True", "tableName": "audit_log", "redisKey": "archery_audit_log_prod", "select": "*", "queryInterval": 30, "leaderExpire": 60, "leaderInterval": 10, "offsetField": "id", "offsetFieldType": "int64", "limit": 100, "codec": "json"}], "filter": [{"type": "date", "source": "action_time", "format": ["2006-01-02T15:04:05.999999Z07:00"], "location": "Asia/Shanghai"}, {"type": "add_field", "key": "log_type", "value": "audit_log"}], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_archery_prod_log"]}]}, {"id": "archery_query_log_prod", "name": "archery_query_log生产环境日志", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"type": "mysql", "dataSourceName": "security_audit_r:Security#9876yhn@tcp(master-3307.db.yorkapp.com:3307)/archery?charset=utf8mb4&timeout=200ms&loc=Local&parseTime=True", "tableName": "query_log", "redisKey": "archery_query_log_prod", "select": "*", "queryInterval": 30, "leaderExpire": 60, "leaderInterval": 10, "offsetField": "id", "offsetFieldType": "int64", "limit": 100, "codec": "json"}], "filter": [{"type": "date", "source": "create_time", "format": ["2006-01-02T15:04:05.999999Z07:00"], "location": "Asia/Shanghai"}, {"type": "add_field", "key": "log_type", "value": "query_log"}], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_archery_prod_log"]}]}, {"id": "archery_workflow_log_prod", "name": "archery_workflow_log生产环境日志", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"type": "mysql", "dataSourceName": "security_audit_r:Security#9876yhn@tcp(master-3307.db.yorkapp.com:3307)/archery?charset=utf8mb4&timeout=200ms&loc=Local&parseTime=True", "tableName": "sql_workflow AS a", "redisKey": "archery_workflow_log_prod", "select": "a.id, a.workflow_name, CASE WHEN a.syntax_type = 1 THEN 'DDL' WHEN a.syntax_type = 2 THEN 'DML' END AS syntax_type_name, a.engineer, a.engineer_display, a.status, a.is_backup, a.create_time, b.instance_name, b.host, b.port, b.db_type, a.db_name, c.group_name", "joins": ["JOIN sql_instance b ON a.instance_id = b.id", "JOIN resource_group c ON a.group_id = c.group_id"], "queryInterval": 30, "leaderExpire": 60, "leaderInterval": 10, "offsetField": "a.id", "offsetFieldInEvent": "id", "offsetFieldType": "int64", "limit": 100, "codec": "json"}], "filter": [{"type": "date", "source": "create_time", "format": ["2006-01-02T15:04:05.999999Z07:00"], "location": "Asia/Shanghai"}, {"type": "add_field", "key": "log_type", "value": "sql_workflow"}], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_archery_prod_log"]}]}, {"id": "archery_test_to_ES", "name": "archery测试环境日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_archery_test_log"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_archery_test_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "archery_prod_to_ES", "name": "archery生产环境日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_archery_prod_log"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_archery_prod_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "nebula_audit_log", "name": "nebula审计日志", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_nebula_audit"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [{"type": "date", "source": "start_time", "format": ["2006-01-02T15:04:05.999999Z07:00"]}], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_nebula_audit_washed"]}]}, {"id": "nebula_audit_to_ES", "name": "nebula_audit日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_nebula_audit_washed"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [{"type": "typeconv", "convType": "string", "fields": ["result.data"]}], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_nebula_audit_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "gitlab_deeptrading", "name": "deeptrading gitlab日志清洗", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_deeptrading_gitlab_log"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [{"type": "remove_field", "fields": ["@metadata", "input", "ecs", "cloud", "agent", "host", "container", "fields"]}, {"type": "split", "delimiter": "\n"}, {"type": "cond", "condFilter": [{"condition": "log\\.file\\.path == '/data/logs/gitlab/nginx/gitlab_access.log'", "filter": [{"type": "grok", "match": ["%{IPV4:ip} - - \\[%{HTTPDATE:timestamp}\\] \"%{WORD:method} %{URIPATH:path_uri}?%{GREEDYDATA:query_string} %{DATA:protocol}\" %{INT:status_code} %{INT:response_size} (?:\"%{URI:referer}\"|\"\") (?:\"%{DATA:user_agent}\"|\"\") %{GREEDYDATA:extra}"]}, {"type": "date", "source": "timestamp", "format": ["02/Jan/2006:15:04:05 -0700"]}, {"type": "cond", "condFilter": [{"condition": "!empty(extra) && isstring(extra)", "filter": [{"type": "mutate", "rename": [["extra", "extra_string"]]}]}]}]}, {"condition": "log\\.file\\.path == '/data/logs/gitlab/gitlab-rails/production_json.log' || log\\.file\\.path == '/data/logs/gitlab/gitlab-rails/api_json.log' || log\\.file\\.path == '/data/logs/gitlab/sidekiq/current' ", "filter": [{"type": "json"}, {"type": "remove_field", "fields": ["queue_duration_s", "request_urgency", "target_duration_s", "redis_calls", "redis_cache_duration_s", "redis_sessions_read_bytes", "redis_sessions_duration_s", "redis_cache_calls", "redis_sessions_write_bytes", "redis_sessions_calls", "redis_cache_write_bytes", "redis_duration_s", "redis_read_bytes", "redis_write_bytes", "redis_feature_flag_calls", "redis_feature_flag_duration_s", "redis_feature_flag_read_bytes", "redis_feature_flag_write_bytes", "db_count", "db_write_count", "db_cached_count", "db_txn_count", "db_replica_txn_count", "db_primary_txn_count", "db_replica_count", "db_primary_count", "db_replica_write_count", "db_primary_write_count", "db_replica_cached_count", "db_primary_cached_count", "db_replica_wal_count", "db_primary_wal_count", "db_replica_wal_cached_count", "db_primary_wal_cached_count", "db_replica_txn_max_duration_s", "db_primary_txn_max_duration_s", "db_replica_txn_duration_s", "db_primary_txn_duration_s", "db_replica_duration_s", "db_primary_duration_s", "db_main_txn_count", "db_ci_txn_count", "db_main_replica_txn_count", "db_ci_replica_txn_count", "db_main_count", "db_ci_count", "db_main_replica_count", "db_ci_replica_count", "db_main_write_count", "db_ci_write_count", "db_main_replica_write_count", "db_ci_replica_write_count", "db_main_cached_count", "db_ci_cached_count", "db_main_replica_cached_count", "db_ci_replica_cached_count", "db_main_wal_count", "db_ci_wal_count", "db_main_replica_wal_count", "db_ci_replica_wal_count", "db_main_wal_cached_count", "db_ci_wal_cached_count", "db_main_replica_wal_cached_count", "db_ci_replica_wal_cached_count", "db_main_txn_max_duration_s", "db_ci_txn_max_duration_s", "db_main_replica_txn_max_duration_s", "db_ci_replica_txn_max_duration_s", "db_main_txn_duration_s", "db_ci_txn_duration_s", "db_main_replica_txn_duration_s", "db_ci_replica_txn_duration_s", "db_main_duration_s", "db_ci_duration_s", "db_main_replica_duration_s", "db_ci_replica_duration_s", "cpu_s", "mem_objects", "mem_bytes", "mem_mallocs", "mem_total_bytes", "pid", "worker_id", "rate_limiting_gates", "db_duration_s", "view_duration_s", "duration_s", "redis_db_load_balancing_write_bytes", "redis_db_load_balancing_calls", "redis_allowed_cross_slot_calls", "redis_repository_cache_calls", "redis_sessions_allowed_cross_slot_calls", "redis_db_load_balancing_duration_s", "graphql", "redis_repository_cache_duration_s", "redis_repository_cache_read_bytes", "redis_repository_cache_write_bytes", "params"]}, {"type": "date", "source": "time", "format": ["2006-01-02T15:04:05Z07:00"]}]}, {"condition": "log\\.file\\.path == '/data/logs/gitlab/gitlab-rails/audit_json.log'", "filter": [{"type": "json"}, {"type": "remove_field", "remove_message": true}, {"type": "date", "source": "time", "format": ["2006-01-02T15:04:05Z07:00"]}]}]}], "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_deeptrading_gitlab_log_washed"]}]}, {"id": "deeptrading_gitlab_log_to_ES", "name": "deeptrading_gitlab日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_deeptrading_gitlab_log_washed"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_gitlab_deeptrading_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "hids_agent_ko_missed_log_to_ES", "name": "hids_agent_ko_missed日志入库", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["10.2.34.14:9092", "10.2.34.244:9092", "10.2.34.245:9092"], "offsetOldest": true, "securityProtocol": "SASL", "saslUsername": "admin", "saslPassword": "el<PERSON><PERSON>", "topics": ["log_hids_agent_ko_missed"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [{"type": "date", "source": "time", "format": ["UNIX"]}], "output": [{"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_hids_agent_ko_missed_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"id": "yunshu_log", "name": "云枢日志", "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1, "input": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_eagle_cloud_yunshu_log"], "group": "sec-gstash-prod", "codec": "json"}, {"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_eagle_cloud_yunshu_admin_log"], "group": "sec-gstash-prod", "codec": "json"}], "filter": [{"type": "remove_field", "removeMessage": true}, {"type": "mutate", "rename": [["hit_summary.file_security_count.", "hit_summary.file_security_count.unknown"]]}, {"type": "cond", "condFilter": [{"condition": "!empty(log_time) && isnumeric(log_time) && log_time < 943891200000", "filter": [{"type": "date", "source": "log_time", "format": ["UNIX"]}]}, {"condition": "!empty(log_time) && isnumeric(log_time) && log_time >= 943891200000", "filter": [{"type": "date", "source": "log_time", "format": ["UNIXMILLI"]}]}, {"condition": "!empty(alert_time) && isnumeric(alert_time)", "filter": [{"type": "date", "source": "alert_time", "format": ["UNIX"]}]}, {"condition": "!empty(login_time)", "filter": [{"type": "date", "source": "login_time", "format": ["2006-01-02T15:04:05.999999999Z07:00"]}]}, {"condition": "!empty(date) ", "filter": [{"type": "date", "source": "date", "format": ["2006-01-02"]}]}], "elseFilter": [{"type": "date", "source": "log_time", "ignoreError": true, "format": ["2006-01-02T15:04:05.999999999Z07:00", "2006-01-02T15:04:05", "2006-01-02", "2006-01-02T15:04:05Z07:00"]}]}], "output": [{"type": "cond", "elseOutput": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_eagle_cloud_yunshu_log_washed"]}, {"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_yunshu_other_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}], "condOutput": [{"condition": "log_type == 'access_log' || log_type == 'l7_access_log'", "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_eagle_cloud_yunshu_log_washed"]}, {"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_yunshu_access_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"condition": "log_type == 'terminal_software_log'", "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_eagle_cloud_yunshu_log_washed"]}, {"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_yunshu_terminal_software_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"condition": "log_type == 'yunshu_terminal'", "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_eagle_cloud_yunshu_log_washed"]}, {"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_yunshu_terminal_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"condition": "log_type == 'yunshu_terminal_event'", "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_eagle_cloud_yunshu_log_washed"]}, {"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_yunshu_terminal_event", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"condition": "log_type == 'dlp_file_send_log'", "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_eagle_cloud_yunshu_log_washed"]}, {"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_yunshu_dlp_file_send_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"condition": "log_type == 'access_control_log'", "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_eagle_cloud_yunshu_log_washed"]}, {"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_yunshu_access_control_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"condition": "log_type == 'threat_control_log'", "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_eagle_cloud_yunshu_log_washed"]}, {"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_yunshu_threat_control_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"condition": "log_type == 'terminal_compliance_log'", "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_eagle_cloud_yunshu_log_washed"]}, {"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_yunshu_terminal_compliance_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"condition": "log_type == 'yunshu_console_login_log' || log_type == 'yunshu_console_ops_log'", "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_eagle_cloud_yunshu_log_washed"]}, {"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_yunshu_console_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}, {"condition": "log_type == 'open_api_log'", "output": [{"version": "3.9.0", "type": "kafka", "brokers": ["**********:9092", "***********:9092", "***********:9092"], "topics": ["log_eagle_cloud_yunshu_log_washed"]}, {"type": "elastic", "version": 8, "action": "create", "url": ["https://**********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200", "https://***********:9200"], "index": "sec_yunshu_open_api_log", "bulkSize": ********, "sslCertificateValidation": false, "user": "gstash", "password": "MGQyVAQ6oQ4YUZb"}]}]}]}]}