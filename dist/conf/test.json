{"pprofAddr": "", "prometheusAddr": ":16670", "debug": false, "env": "dev", "appId": "security-gstash-svc", "log": {"localLog": false, "logFile": "", "interval": 24, "maxAge": 30, "maxSize": 15360, "localTime": true}, "dbConfig": false, "hotUpdate": false, "mysql": {"default": {"dataSourceName": "root:12345678@tcp(127.0.0.1:3306)/gstash?charset=utf8&timeout=200ms&loc=Local&parseTime=True", "maxIdleConns": 100, "maxOpenConns": 200}, "datasec": {"dataSourceName": "root:12345678@tcp(127.0.0.1:3306)/gstash?charset=utf8&timeout=200ms&loc=Local&parseTime=True", "maxIdleConns": 100, "maxOpenConns": 200}}, "redis": {"default": {"servers": ["127.0.0.1:6379"], "cluster": false, "maxRetries": -1}}, "worker": [{"id": "test", "name": "test", "input": [{"type": "constant", "rate": 1, "value": "2025-04-16T12:33:00.532Z: Instantiating Gitlab::Auth::Ldap::Person with LDIF:\\ndn: cn=<EMAIL>,ou=java组,ou=技术支持部,ou=量化运营中心,ou=资产管理投资集团,dc=avenir,dc=com\\ncn: <EMAIL>\\nentrydn:: Y249Z2F2aW4ud2FuZ0BhdmVuaXIuaGssb3U9amF2Yee7hCxvdT3mioDmnK/m\\n lK/mjIHpg6gsb3U96YeP5YyW6L+Q6JCl5Lit5b+DLG91Pei1hOS6p+euoeeQ\\n huaKlei1hOmbhuWboixkYz1hdmVuaXIsZGM9Y29t\\nmail: <EMAIL>\\nsamaccountname: <EMAIL>"}], "filter": [{"type": "rate_drop", "rate": 1500}], "output": [{"type": "drop"}], "channelSize": 100, "inputConcurrency": 1, "filterConcurrency": 1, "outputConcurrency": 1}]}