/**
 * @note
 * 简单的通过redis选主
 *
 * <AUTHOR>
 * @date 	2020-11-30
 */
package redis

import (
	"context"
	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"
	"github.com/tmsong/hlog"
	"sync"
	"time"
)

type Leader struct {
	lockKey     string
	lockEx      time.Duration
	tryInterval time.Duration
	leaderAddr  string
	localAddr   string
	lock        sync.RWMutex
	isLeader    bool
	RedisClient *Client
}

func (l *Leader) IsLeader() bool {
	l.lock.RLock()
	defer l.lock.RUnlock()
	return l.isLeader
}
func (l *Leader) LeaderIP() string {
	l.lock.RLock()
	defer l.lock.RUnlock()
	return l.leaderAddr
}

func (l *Leader) tryLeader(ctx context.Context, wg *sync.WaitGroup) {
	wg.Add(1)
	defer wg.Done()

	ticker := time.NewTicker(l.tryInterval)
	defer ticker.Stop()
	err := l.tryLeaderOnce()
	if err != nil {
		l.RedisClient.logger.Warnf("try leader once err: %v", err)
	}
	for {
		select {
		case <-ctx.Done():
			l.RedisClient.logger.Infof("leader stop, time:%v", time.Now().Unix())
			return
		case <-ticker.C:
			err := l.tryLeaderOnce()
			if err != nil {
				l.RedisClient.logger.Warnf("try leader once err: %v", err)
			}
		}
	}
}

func (l *Leader) tryLeaderOnce() error {
	var err error
	var suc bool

	val, err := l.RedisClient.Get(l.lockKey)
	var leaderAddr string
	if err != nil && err != redis.Nil {
		l.RedisClient.logger.Errorf("try leader get lockkey err: %v", err)
		goto FAIL_END
	} else if err == nil {
		if leaderAddr, err = val.Result(); err != nil {
			l.RedisClient.logger.Errorf("try leader get lockkey err: %v", err)
			goto FAIL_END
		}
	}

	if leaderAddr == "" {

		suc, err = l.RedisClient.SetNX(l.lockKey, l.localAddr, l.lockEx)
		if !suc || err != nil {
			goto FAIL_END
		}

		l.leaderAddr = l.localAddr

		goto SUCESS_END
	}

	l.leaderAddr = leaderAddr
	if l.localAddr != leaderAddr {
		l.RedisClient.logger.Infof("try leader not leader, leader addr:%v", leaderAddr)
		goto FAIL_END
	}
	// extend leadership
	suc, err = l.RedisClient.Expire(l.lockKey, l.lockEx)
	if suc && err == nil {
		goto SUCESS_END
	}

FAIL_END:
	l.lock.Lock()
	l.isLeader = false
	l.lock.Unlock()

	return err

SUCESS_END:
	l.RedisClient.logger.Debugf("try leader still leader")
	l.lock.Lock()
	l.isLeader = true
	l.lock.Unlock()

	return nil
}

/**
 * 通过redis选主算法
 * @param lockKey 锁的名称
 * @param lockEx  锁的过期时间（expire）,单位毫秒
 * @param interval 每隔多久续签一次TTL,单位秒（必须小于lockEx）
 * @param host 主机名（或者ip地址）
 *
 * @return leader Leader对象
 * @return err 发生的错误
 */

func (c *Client) ElectLeader(ctx context.Context, lockKey string, lockEx, interval time.Duration, host string) (leader *Leader, err error) {
	logField := hlog.GetLogField(hlog.LogTagRedisOk)
	defer func() {
		f := logrus.Fields{"lockKey": lockKey, "lockEx": lockEx, "interval": interval, "host": host, "api": "ELECT"}
		c.Print(err, logField, f)
	}()
	wg := new(sync.WaitGroup)
	leader = &Leader{lockKey: lockKey, lockEx: lockEx, tryInterval: interval, localAddr: host, RedisClient: c}
	go leader.tryLeader(ctx, wg)
	time.Sleep(1 * time.Second)
	return leader, nil
}
