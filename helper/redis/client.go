/**
 * @note
 * client.go
 *
 * <AUTHOR>
 * @date 	2020-11-27
 */
package redis

import (
	"context"
	"crypto/tls"
	"errors"
	"fmt"
	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"
	"github.com/tmsong/hlog"
	"gstash/helper/logger"
	"time"
)

type Client struct {
	client        *redis.Client
	clusterClient *redis.ClusterClient
	logger        *hlog.Logger
	isCluster     bool
	PrintLog      bool
}

func (c *Client) SetLogger(l *hlog.Logger) {
	c.logger = l
}

func (c *Client) WithContext(ctx context.Context) *Client {
	if c.isCluster {
		c.clusterClient = c.clusterClient.WithContext(ctx)
	} else {
		c.client = c.client.WithContext(ctx)
	}
	return c
}

func (c *Client) Context() context.Context {
	if c.isCluster {
		return c.clusterClient.Context()
	} else {
		return c.client.Context()
	}
}

func newClient(conf RedisConfig) (*Client, error) {
	if len(conf.Servers) == 0 {
		return nil, errors.New("invalid servers")
	}
	c := &Client{isCluster: conf.Cluster}
	if conf.Cluster {
		c.clusterClient = redis.NewClusterClient(&redis.ClusterOptions{
			Addrs:          conf.Servers,
			Username:       conf.User,
			Password:       conf.Password,
			MaxRetries:     conf.MaxRetries,
			DialTimeout:    conf.dialTimeoutT,
			ReadTimeout:    conf.readTimeoutT,
			WriteTimeout:   conf.writeTimeoutT,
			PoolSize:       conf.PoolSize,
			MinIdleConns:   conf.MinIdle,
			MaxConnAge:     conf.maxConnAgeT,
			PoolTimeout:    conf.poolTimeoutT,
			IdleTimeout:    conf.idleTimeoutT,
			MaxRedirects:   conf.MaxRedirects,
			RouteByLatency: conf.RouteByLatency,
			RouteRandomly:  conf.RouteRandomly,
			TLSConfig: func(useTLS bool) *tls.Config {
				if useTLS {
					return &tls.Config{InsecureSkipVerify: true}
				}
				return nil
			}(conf.TLS),
		})
	} else {
		c.client = redis.NewClient(&redis.Options{
			Addr:         conf.Servers[0],
			Username:     conf.User,
			Password:     conf.Password,
			DB:           conf.Database,
			MaxRetries:   conf.MaxRetries,
			DialTimeout:  conf.dialTimeoutT,
			ReadTimeout:  conf.readTimeoutT,
			WriteTimeout: conf.writeTimeoutT,
			PoolSize:     conf.PoolSize,
			MinIdleConns: conf.MinIdle,
			MaxConnAge:   conf.maxConnAgeT,
			PoolTimeout:  conf.poolTimeoutT,
			IdleTimeout:  conf.idleTimeoutT,
			TLSConfig: func(useTLS bool) *tls.Config {
				if useTLS {
					return &tls.Config{InsecureSkipVerify: true}
				}
				return nil
			}(conf.TLS),
		})
	}
	return c, nil
}

func (c *Client) Print(err error, startField, endField logrus.Fields) {
	if c.PrintLog {
		logger.PrintLogWithError(err, c.logger, startField, endField)
	}
}

// SET expiration = -1 时为keepTTL
func (c *Client) Set(key string, value interface{}, expiration time.Duration) (err error) {
	logField := hlog.GetLogField(hlog.LogTagRedisOk)
	defer func() {
		f := logrus.Fields{"key": key, "api": "SET", "value": value, "expire": expiration}
		c.Print(err, logField, f)
	}()
	if c.isCluster {
		return c.clusterClient.Set(c.Context(), key, value, expiration).Err()
	} else {
		return c.client.Set(c.Context(), key, value, expiration).Err()
	}
}

func (c *Client) SetNX(key string, value interface{}, expiration time.Duration) (suc bool, err error) {
	logField := hlog.GetLogField(hlog.LogTagRedisOk)
	defer func() {
		f := logrus.Fields{"key": key, "api": "SETNX", "value": value, "expire": expiration, "success": suc}
		c.Print(err, logField, f)
	}()
	if c.isCluster {
		return c.clusterClient.SetNX(c.Context(), key, value, expiration).Result()
	} else {
		return c.client.SetNX(c.Context(), key, value, expiration).Result()
	}
}

func (c *Client) Get(key string) (ret *redis.StringCmd, err error) {
	logField := hlog.GetLogField(hlog.LogTagRedisOk)
	defer func() {
		f := logrus.Fields{"key": key, "api": "GET", "output": ret.Val()}
		c.Print(err, logField, f)
	}()
	if c.isCluster {
		ret = c.clusterClient.Get(c.Context(), key)
	} else {
		ret = c.client.Get(c.Context(), key)
	}
	return ret, ret.Err()
}

func (c *Client) MGet(keys ...string) (ret []interface{}, err error) {
	logField := hlog.GetLogField(hlog.LogTagRedisOk)
	defer func() {
		f := logrus.Fields{"keys": keys, "api": "MGET", "output": fmt.Sprintf("%v", ret)}
		c.Print(err, logField, f)
	}()
	if c.isCluster {
		return c.clusterClient.MGet(c.Context(), keys...).Result()
	} else {
		return c.client.MGet(c.Context(), keys...).Result()
	}
}

func (c *Client) Expire(key string, expiration time.Duration) (suc bool, err error) {
	logField := hlog.GetLogField(hlog.LogTagRedisOk)
	defer func() {
		f := logrus.Fields{"key": key, "api": "EXPIRE", "expire": expiration}
		c.Print(err, logField, f)
	}()
	if c.isCluster {
		return c.clusterClient.Expire(c.Context(), key, expiration).Result()
	} else {
		return c.client.Expire(c.Context(), key, expiration).Result()
	}
}

func (c *Client) PExpire(key string, expiration time.Duration) (suc bool, err error) {
	logField := hlog.GetLogField(hlog.LogTagRedisOk)
	defer func() {
		f := logrus.Fields{"key": key, "api": "PEXPIRE", "expire": expiration}
		c.Print(err, logField, f)
	}()
	if c.isCluster {
		return c.clusterClient.PExpire(c.Context(), key, expiration).Result()
	} else {
		return c.client.PExpire(c.Context(), key, expiration).Result()
	}
}

// INCRBY
func (c *Client) IncrBy(key string, value int64) (ret int64, err error) {
	logField := hlog.GetLogField(hlog.LogTagRedisOk)
	defer func() {
		f := logrus.Fields{"key": key, "api": "INCRBY", "increment": value, "result": ret}
		c.Print(err, logField, f)
	}()
	if c.isCluster {
		return c.clusterClient.IncrBy(c.Context(), key, value).Result()
	} else {
		return c.client.IncrBy(c.Context(), key, value).Result()
	}
}

// INCR
func (c *Client) Incr(key string) (ret int64, err error) {
	logField := hlog.GetLogField(hlog.LogTagRedisOk)
	defer func() {
		f := logrus.Fields{"key": key, "api": "INCR", "result": ret}
		c.Print(err, logField, f)
	}()
	if c.isCluster {
		return c.clusterClient.Incr(c.Context(), key).Result()
	} else {
		return c.client.Incr(c.Context(), key).Result()
	}
}

// DECR
func (c *Client) Decr(key string) (ret int64, err error) {
	logField := hlog.GetLogField(hlog.LogTagRedisOk)
	defer func() {
		f := logrus.Fields{"key": key, "api": "DECR", "result": ret}
		c.Print(err, logField, f)
	}()
	if c.isCluster {
		return c.clusterClient.Decr(c.Context(), key).Result()
	} else {
		return c.client.Decr(c.Context(), key).Result()
	}
}

// DECRBY
func (c *Client) DecrBy(key string, value int64) (ret int64, err error) {
	logField := hlog.GetLogField(hlog.LogTagRedisOk)
	defer func() {
		f := logrus.Fields{"key": key, "decrement": value, "api": "DECRBY", "result": ret}
		c.Print(err, logField, f)

	}()
	if c.isCluster {
		return c.clusterClient.DecrBy(c.Context(), key, value).Result()
	} else {
		return c.client.DecrBy(c.Context(), key, value).Result()
	}
}

// DECRBY
func (c *Client) TTL(key string) (ret time.Duration, err error) {
	logField := hlog.GetLogField(hlog.LogTagRedisOk)
	defer func() {
		f := logrus.Fields{"key": key, "api": "TTL", "output": ret}
		c.Print(err, logField, f)

	}()
	if c.isCluster {
		return c.clusterClient.TTL(c.Context(), key).Result()
	} else {
		return c.client.TTL(c.Context(), key).Result()
	}
}

// DECRBY
func (c *Client) PTTL(key string) (ret time.Duration, err error) {
	logField := hlog.GetLogField(hlog.LogTagRedisOk)
	defer func() {
		f := logrus.Fields{"key": key, "api": "PTTL", "output": ret}
		c.Print(err, logField, f)

	}()
	if c.isCluster {
		return c.clusterClient.PTTL(c.Context(), key).Result()
	} else {
		return c.client.PTTL(c.Context(), key).Result()
	}
}

// DEL
func (c *Client) Del(keys ...string) (ret int64, err error) {
	logField := hlog.GetLogField(hlog.LogTagRedisOk)
	defer func() {
		f := logrus.Fields{"key": keys, "api": "DEL", "effectRows": ret}
		c.Print(err, logField, f)

	}()
	if c.isCluster {
		return c.clusterClient.Del(c.Context(), keys...).Result()
	} else {
		return c.client.Del(c.Context(), keys...).Result()
	}
}

// LPOP
func (c *Client) LPop(key string) (ret *redis.StringCmd, err error) {
	logField := hlog.GetLogField(hlog.LogTagRedisOk)
	defer func() {
		f := logrus.Fields{"key": key, "api": "LPOP", "output": ret.Val()}
		c.Print(err, logField, f)
	}()
	if c.isCluster {
		ret = c.clusterClient.LPop(c.Context(), key)
	} else {
		ret = c.client.LPop(c.Context(), key)
	}
	return ret, ret.Err()
}

// LPUSH
func (c *Client) LPush(key string, values ...interface{}) (ret int64, err error) {
	logField := hlog.GetLogField(hlog.LogTagRedisOk)
	defer func() {
		f := logrus.Fields{"key": key, "api": "LPUSH", "value": values}
		c.Print(err, logField, f)
	}()
	if c.isCluster {
		return c.clusterClient.LPush(c.Context(), key, values...).Result()
	} else {
		return c.client.LPush(c.Context(), key, values...).Result()
	}
}

// RPOP
func (c *Client) RPop(key string) (ret *redis.StringCmd, err error) {
	logField := hlog.GetLogField(hlog.LogTagRedisOk)
	defer func() {
		f := logrus.Fields{"key": key, "api": "RPOP", "output": ret.Val()}
		c.Print(err, logField, f)
	}()
	if c.isCluster {
		ret = c.clusterClient.RPop(c.Context(), key)
	} else {
		ret = c.client.RPop(c.Context(), key)
	}
	return ret, ret.Err()
}

// RPUSH
func (c *Client) RPush(key string, values ...interface{}) (ret int64, err error) {
	logField := hlog.GetLogField(hlog.LogTagRedisOk)
	defer func() {
		f := logrus.Fields{"key": key, "api": "RPUSH", "value": values}
		c.Print(err, logField, f)
	}()
	if c.isCluster {
		return c.clusterClient.RPush(c.Context(), key, values...).Result()
	} else {
		return c.client.RPush(c.Context(), key, values...).Result()
	}
}

// LLEN
func (c *Client) LLen(key string) (ret int64, err error) {
	logField := hlog.GetLogField(hlog.LogTagRedisOk)
	defer func() {
		f := logrus.Fields{"key": key, "api": "LLEN", "output": ret}
		c.Print(err, logField, f)
	}()
	if c.isCluster {
		return c.clusterClient.LLen(c.Context(), key).Result()
	} else {
		return c.client.LLen(c.Context(), key).Result()
	}
}

// LRange
func (c *Client) LRange(key string, start, stop int64) (ret []string, err error) {
	logField := hlog.GetLogField(hlog.LogTagRedisOk)
	defer func() {
		f := logrus.Fields{"key": key, "api": "LRANGE", "start": start, "stop": stop, "output": ret}
		c.Print(err, logField, f)
	}()
	if c.isCluster {
		return c.clusterClient.LRange(c.Context(), key, start, stop).Result()
	} else {
		return c.client.LRange(c.Context(), key, start, stop).Result()
	}
}

// SET expiration = -1 时为keepTTL
func (c *Client) HSet(key string, valuesMap map[string]interface{}) (err error) {
	logField := hlog.GetLogField(hlog.LogTagRedisOk)
	defer func() {
		f := logrus.Fields{"key": key, "api": "HSET", "valuesMap": valuesMap}
		c.Print(err, logField, f)
	}()
	if c.isCluster {
		return c.clusterClient.HSet(c.Context(), key, valuesMap).Err()
	} else {
		return c.client.HSet(c.Context(), key, valuesMap).Err()
	}
}

func (c *Client) HSetNX(key, field string, value interface{}) (suc bool, err error) {
	logField := hlog.GetLogField(hlog.LogTagRedisOk)
	defer func() {
		f := logrus.Fields{"key": key, "api": "HSETNX", "value": value, "success": suc}
		c.Print(err, logField, f)
	}()
	if c.isCluster {
		return c.clusterClient.HSetNX(c.Context(), key, field, value).Result()
	} else {
		return c.client.HSetNX(c.Context(), key, field, value).Result()
	}
}

func (c *Client) HIncrBy(key, field string, value int64) (ret int64, err error) {
	logField := hlog.GetLogField(hlog.LogTagRedisOk)
	defer func() {
		f := logrus.Fields{"key": key, "api": "HINCRBY", "increment": value, "result": ret}
		c.Print(err, logField, f)
	}()
	if c.isCluster {
		return c.clusterClient.HIncrBy(c.Context(), key, field, value).Result()
	} else {
		return c.client.HIncrBy(c.Context(), key, field, value).Result()
	}
}

func (c *Client) HGet(key, field string) (ret *redis.StringCmd, err error) {
	logField := hlog.GetLogField(hlog.LogTagRedisOk)
	defer func() {
		f := logrus.Fields{"key": key, "field": field, "api": "HGET", "output": ret.Val()}
		c.Print(err, logField, f)
	}()
	if c.isCluster {
		ret = c.clusterClient.HGet(c.Context(), key, field)
	} else {
		ret = c.client.HGet(c.Context(), key, field)
	}
	return ret, ret.Err()
}

// HDEL
func (c *Client) HDel(key string, fields ...string) (ret int64, err error) {
	logField := hlog.GetLogField(hlog.LogTagRedisOk)
	defer func() {
		f := logrus.Fields{"key": key, "field": fields, "api": "HDEL", "effectRows": ret}
		c.Print(err, logField, f)

	}()
	if c.isCluster {
		return c.clusterClient.HDel(c.Context(), key, fields...).Result()
	} else {
		return c.client.HDel(c.Context(), key, fields...).Result()
	}
}

func (c *Client) HKeys(key string) (ret []string, err error) {
	logField := hlog.GetLogField(hlog.LogTagRedisOk)
	defer func() {
		f := logrus.Fields{"key": key, "api": "HKEYS", "output": ret}
		c.Print(err, logField, f)
	}()
	if c.isCluster {
		return c.clusterClient.HKeys(c.Context(), key).Result()
	} else {
		return c.client.HKeys(c.Context(), key).Result()
	}
}

func (c *Client) HVals(key string) (ret []string, err error) {
	logField := hlog.GetLogField(hlog.LogTagRedisOk)
	defer func() {
		f := logrus.Fields{"key": key, "api": "HVALS", "output": ret}
		c.Print(err, logField, f)
	}()
	if c.isCluster {
		return c.clusterClient.HVals(c.Context(), key).Result()
	} else {
		return c.client.HVals(c.Context(), key).Result()
	}
}

func (c *Client) HLen(key string) (ret int64, err error) {
	logField := hlog.GetLogField(hlog.LogTagRedisOk)
	defer func() {
		f := logrus.Fields{"key": key, "api": "HLEN", "output": ret}
		c.Print(err, logField, f)
	}()
	if c.isCluster {
		return c.clusterClient.HLen(c.Context(), key).Result()
	} else {
		return c.client.HLen(c.Context(), key).Result()
	}
}

func (c *Client) HGetAll(key string) (ret map[string]string, err error) {
	logField := hlog.GetLogField(hlog.LogTagRedisOk)
	defer func() {
		f := logrus.Fields{"key": key, "api": "HGETALL", "output": ret}
		c.Print(err, logField, f)
	}()
	if c.isCluster {
		return c.clusterClient.HGetAll(c.Context(), key).Result()
	} else {
		return c.client.HGetAll(c.Context(), key).Result()
	}
}
