/**
 * @note
 * config.go
 *
 * <AUTHOR>
 * @date 	2020-11-27
 */
package redis

import (
	"errors"
	"github.com/go-redis/redis/v8"
	"github.com/tmsong/hlog"
	"sync"
	"time"
)

var (
	config  Config
	clients *sync.Map
	Nil     = redis.Nil
)

type RedisConfig struct {
	Servers  []string `json:"servers"`
	User     string   `json:"user"`
	Password string   `json:"password"`
	Database int      `json:"database"`

	PoolSize int `json:"poolSize"`
	MinIdle  int `json:"minIdle"`

	MaxConnAge   string `json:"maxConnAge"`
	PoolTimeout  string `json:"poolTimeout"`
	IdleTimeout  string `json:"idleTimeout"`
	DialTimeout  string `json:"dialTimeout"`
	ReadTimeout  string `json:"readTimeout"`
	WriteTimeout string `json:"writeTimeout"`

	maxConnAgeT  time.Duration
	poolTimeoutT time.Duration
	idleTimeoutT time.Duration

	dialTimeoutT  time.Duration
	readTimeoutT  time.Duration
	writeTimeoutT time.Duration

	MaxRetries int  `json:"maxRetries"` //当为-1时，为不重试
	TLS        bool `json:"tls"`

	//以下是cluster模式选项
	Cluster        bool `json:"cluster"`
	MaxRedirects   int  `json:"maxRedirects"`
	RouteByLatency bool `json:"routeByLatency"`
	RouteRandomly  bool `json:"routeRandomly"`
}

func (c *RedisConfig) parseTime() RedisConfig {
	var t time.Duration
	var err error
	if c.MaxConnAge != "" {
		if t, err = time.ParseDuration(c.MaxConnAge); err == nil {
			c.maxConnAgeT = t
		}
	}
	if c.PoolTimeout != "" {
		if t, err = time.ParseDuration(c.PoolTimeout); err == nil {
			c.poolTimeoutT = t
		}
	}
	if c.IdleTimeout != "" {
		if t, err = time.ParseDuration(c.IdleTimeout); err == nil {
			c.idleTimeoutT = t
		}
	}
	if c.DialTimeout != "" {
		if t, err = time.ParseDuration(c.DialTimeout); err == nil {
			c.dialTimeoutT = t
		}
	}
	if c.ReadTimeout != "" {
		if t, err = time.ParseDuration(c.ReadTimeout); err == nil {
			c.readTimeoutT = t
		}
	}
	if c.WriteTimeout != "" {
		if t, err = time.ParseDuration(c.WriteTimeout); err == nil {
			c.writeTimeoutT = t
		}
	}
	return *c
}

func init() {
	clients = new(sync.Map)
}

type Config map[string]RedisConfig

func SetupConfig(c Config) {
	conf := make(map[string]RedisConfig)
	for k, v := range c {
		conf[k] = v.parseTime()
	}
	config = conf
}

/**
 * @note
 * 根据名称从获取redis-client，若不存在则new一个新的
 */
func GetClient(name string, logger *hlog.Logger) (*Client, error) {
	if logger == nil {
		return nil, errors.New("redis client must set logger")
	}
	if cached, ok := clients.Load(name); ok {
		cachedClient := cached.(*Client)
		newClient := &Client{
			client:        cachedClient.client,
			clusterClient: cachedClient.clusterClient,
			isCluster:     cachedClient.isCluster,
			logger:        logger,
		}
		return newClient, nil
	} else if redisClient := getClient(name, logger); redisClient != nil {
		redisClient.SetLogger(logger)
		return redisClient, nil
	}
	return nil, errors.New("redis client not exist")
}

// 获取默认的redis-client
func DefaultClient(logger *hlog.Logger) (*Client, error) {
	return GetClient("default", logger)
}

// 根据配置新建一个client并存入map中
func getClient(name string, l *hlog.Logger) *Client {
	if conf, ok := config[name]; ok {
		client, err := newClient(conf)
		if err != nil {
			l.Errorln("redis:" + name + "newClient: " + err.Error())
			return nil
		}
		clients.Store(name, client)
		return client
	} else {
		l.Errorln("redis conf undefined, name:" + name)
	}
	return nil
}
