/**
 * @note
 * 内存中的缓存
 *
 * <AUTHOR>
 * @date 	2020-10-14
 */

package cache

import (
	"bytes"
	"encoding/gob"
	"reflect"

	"errors"
	"github.com/coocood/freecache"
)

const (
	Byte = 1
	KB   = 1024 * Byte
	MB   = 1024 * KB
	GB   = 1024 * MB
)

var (
	ErrNoSupportType = errors.New("no support type")
	ErrNotFound      = freecache.ErrNotFound
)

type MemoryCache struct {
	c *freecache.Cache
}

func NewMemoryCache(size int) *MemoryCache {
	return &MemoryCache{
		c: freecache.NewCache(size),
	}
}

func (mc *MemoryCache) Get(key string, val interface{}) (err error) {
	if reflect.TypeOf(val).Kind() != reflect.Ptr {
		err = ErrNoSupportType
		return
	}
	if data, e := mc.c.Get([]byte(key)); e != nil {
		err = e
		return
	} else {
		err = GobDecode(data, val)
		return
	}
}

func (mc *MemoryCache) GetByte(key string) (val []byte, err error) {
	return mc.c.Get([]byte(key))
}

func (mc *MemoryCache) Set(key string, val interface{}, expireSeconds int) (err error) {
	if reflect.TypeOf(val).Kind() != reflect.Ptr {
		err = ErrNoSupportType
		return
	}
	if data, e := GobEncode(val); e != nil {
		err = e
		return
	} else {
		err = mc.c.Set([]byte(key), data, expireSeconds)
		return
	}
}

func (mc *MemoryCache) SetByte(key string, val []byte, expireSeconds int) (err error) {
	return mc.c.Set([]byte(key), val, expireSeconds)
}

func (mc *MemoryCache) Del(key string) (ret bool) {
	return mc.c.Del([]byte(key))
}

func (mc *MemoryCache) Clear() {
	mc.c.Clear()
}

func GobEncode(src interface{}) ([]byte, error) {
	buf := bytes.NewBuffer(nil)
	encoder := gob.NewEncoder(buf)
	err := encoder.Encode(src)
	return buf.Bytes(), err
}

func GobDecode(data []byte, obj interface{}) error {
	buf := bytes.NewBuffer(data)
	decoder := gob.NewDecoder(buf)
	return decoder.Decode(obj)
}
