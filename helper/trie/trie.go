package trie

func (p *PatternTrie) IsNil() bool {
	return p.root == nil
}

/**
 * @note
 * 构造一个新前缀树
 */
func newTree(pattern string, v ...interface{}) *PNode {
	pattern = removeDupWildcard(pattern) //去掉重复的*通配符
	var root, cur, child *PNode          //根节点，当前节点，要添加的子节点
	var j int
	for i, l := 0, len(pattern); i < l; {
		s, escape := []byte{}, false
		for j = 0; j < l-i; j++ {
			if pattern[i+j] == '\\' {
				if escape = !escape; escape {
					continue
				}
			} else if pattern[i+j] == '*' {
				if !escape {
					break
				}
			}
			escape = false
			s = append(s, pattern[i+j])
		}
		if j == 0 {
			child = &PNode{
				S:   "*",
				Typ: nodeWILDCARD,
			}
			i++
		} else {
			child = &PNode{
				S:   string(s),
				Typ: nodeCOMMON,
			}
			i = i + j
		}
		if cur != nil { //非根节点
			switch child.Typ {
			case nodeWILDCARD:
				cur.Wcard = child
			case nodeCOMMON:
				cur.Child = []*PNode{child}
				cur.Childidx = []byte{child.S[0]}
			}
		} else {
			root = child //给根节点赋值
		}
		cur = child //切换当前节点
	}
	cur.V = append(cur.V, v...) //到底了，赋值
	cur.Leaf = true
	return root
}

/**
 * @note
 * set节点值，对原来的值进行扩展
 */
func (p *PNode) setV(v ...interface{}) (ov []interface{}, is bool) {
	is = p.Leaf
	ov = p.V
	p.V, p.Leaf = append(p.V, v...), true
	return ov, is
}

/**
 * @note
 * 增加一个节点，如果已存在，则将元素扩展至已有节点
 * params
 * pattern 对应的pattern
 * V 要添加的元素
 * return
 * ov oldValue的意思，此节点若已存在旧的元素，则返回旧元素集合，否则返回nil
 * has 旧节点是否存在
 */
func (p *PatternTrie) Add(pattern string, v interface{}) (ov interface{}, has bool) {
	if len(pattern) == 0 {
		return
	}
	pattern = removeDupWildcard(pattern) //去掉重复的*通配符
	if p.root == nil {
		p.root = newTree(pattern, v)
		return
	}
	cur := p.root //从根节点开始
INSERT:
	for {
		var i, l int
		var wmatch, escape bool

		if cur.Typ == nodeWILDCARD {
			if len(pattern) > 0 && pattern[0] == '*' { //都是*
				wmatch = true
			}
		} else {
			for i < len(pattern) && l < len(cur.S) {
				if pattern[i] == '\\' {
					if escape = !escape; escape {
						i++
						continue
					}
				}
				if !escape && pattern[i] == '*' {
					break
				}
				if pattern[i] != cur.S[l] { //最长匹配
					break
				}
				escape = false
				i, l = i+1, l+1
			}
			if escape {
				i--
				escape = false
			}
		}
		switch {
		case wmatch:
			i = 1
			fallthrough
		case l == len(cur.S): //完全匹配了cur
			pattern = pattern[i:]
			if len(pattern) == 0 { //当前pattern到头了，则直接将value set到cur
				return cur.setV(v)
			}
			if pattern[0] == '*' { //当前pattern打头是个*
				if cur.Wcard == nil {
					cur.Wcard = newTree(pattern, v)
					return
				} else {
					cur = cur.Wcard
					continue
				}
			}

			first := 0
			if pattern[0] == '\\' {
				first = 1
			}
			if len(pattern[first:]) > 0 {
				for i := 0; i < len(cur.Childidx); i++ {
					if cur.Childidx[i] == pattern[first] { //在childidx内找子孩子节点
						cur = cur.Child[i]
						continue INSERT
					}
				}
			}
			//未完全匹配的情况⤵
		case cur.Typ == nodeWILDCARD:
			i, l = 0, 0
			fallthrough
		default: // 拆分cur为两个节点
			prefix, suffix := cur.S[:l], cur.S[l:] //prefix为pattern与cur的共同部分
			child := &PNode{
				S:        suffix,
				Typ:      cur.Typ,
				Child:    cur.Child,
				Childidx: cur.Childidx,
				Wcard:    cur.Wcard,
				V:        cur.V,
				Leaf:     cur.Leaf,
			}
			*cur = PNode{} //保留cur地址不变并清空cur内的值。
			cur.S = prefix
			cur.Typ = nodeCOMMON
			if child.Typ == nodeWILDCARD {
				cur.Wcard = child
			} else {
				cur.Child = []*PNode{child}
				cur.Childidx = []byte{child.S[0]}
			}
			pattern = pattern[i:]
			if len(pattern) == 0 { // end
				return cur.setV(v)
			}
		}
		// 构造一个新tree塞到cur下。
		// 为了之后搜索时剪枝，这里对childidx进行排序存储
		child := newTree(pattern, v)
		switch child.Typ {
		case nodeCOMMON:
			var targetIdx int
			for ; targetIdx < len(cur.Childidx); targetIdx++ {
				if cur.Childidx[targetIdx] >= child.S[0] {
					break
				}
			}
			if lc := len(cur.Child); lc == 0 || targetIdx == lc { //如果cur没有子节点或者已经到最后一个元素了，则直接append
				cur.Child = append(cur.Child, child)
				cur.Childidx = append(cur.Childidx, child.S[0])
			} else { //实现原地插入，不另开辟空间
				cur.Child = append(cur.Child[:targetIdx+1], cur.Child[targetIdx:]...)
				cur.Child[targetIdx] = child
				cur.Childidx = append(cur.Childidx[:targetIdx+1], cur.Childidx[targetIdx:]...)
				cur.Childidx[targetIdx] = child.S[0]
			}
		case nodeWILDCARD:
			cur.Wcard = child
		}
		return
	}
}

/**
 * 找到此前缀树中所有能匹配的子节点的v，合在一起返回
 * ok 是否找到
 */
func (p *PatternTrie) Lookup(s string, all bool) (values []interface{}, ok bool) {
	nodeSet := lookup(p.root, s, all)
	if len(nodeSet) > 0 {
		for _, n := range nodeSet {
			values = append(values, n.V...)
		}
		ok = true
	}
	return
}

/**
 * @note
 * 寻找前缀树中能匹配上此string的节点
 * param all：是否寻找所有能匹配上的节点，还是只寻找一个。
 */
func lookup(n *PNode, str string, all bool) (nodeSet []*PNode) {
	if n == nil {
		return nil
	}
	if n.Typ == nodeWILDCARD { //如果是通配符，递归处理
		for capture := 0; capture <= len(str); capture++ {
			if set := lookupW(n, str[capture:], all); set != nil {
				if !all { //如果仅是找到即可，则立即返回
					return set
				}
				nodeSet = append(nodeSet, set...)
			}
		}
		return
	}

	minLen := len(str)
	if minLen > len(n.S) {
		minLen = len(n.S)
	}
	var l int // 最长匹配前缀长度
	for l = 0; l < minLen && str[l] == n.S[l]; l++ {
	}
	switch l {
	case len(n.S): // 已经全部匹配了当前节点
		str = str[l:]      //把str里的共同前缀去掉
		if len(str) == 0 { // str也为空了，表示匹配完成
			if n.Leaf { //当前节点是一个子节点
				nodeSet = append(nodeSet, n)
			} else if set := lookup(n.Wcard, str, all); set != nil { //试一下通配符能不能匹配
				nodeSet = append(nodeSet, set...)
			}
			return
		}
		//否则继续往下走，在子孩子中寻找
		return lookupC(n, str, all)
	default:
		return nil
	}
}

/*
 * @note
 * 如果节点n是一个通配符，使用这个方法
 */
func lookupW(n *PNode, str string, all bool) (nodeSet []*PNode) {
	if str == "" {
		if n.Leaf {
			return []*PNode{n}
		}
		return nil
	}
	return lookupC(n, str, all)
}

/*
 * @note
 * 在节点n的child中寻找节点集合
 */
func lookupC(n *PNode, str string, all bool) (nodeSet []*PNode) {
	for i := 0; i < len(n.Childidx); i++ {
		if n.Childidx[i] == str[0] {
			if set := lookup(n.Child[i], str, all); set != nil {
				if !all { //如果仅是找到即可，则立即返回
					return set
				}
				nodeSet = append(nodeSet, set...)
			}
		} else if n.Childidx[i] > str[0] { //超过了，剪枝
			break
		}
	}
	//最后再对通配符子节点进行匹配
	if set := lookup(n.Wcard, str, all); set != nil {
		nodeSet = append(nodeSet, set...)
	}
	return
}

/*
 * @note
 * 对输入pattern的通配符作去重
 */
func removeDupWildcard(pattern string) string {
	var newPattern []byte
	var escape, prevWildcard bool
	for _, b := range pattern {
		if !escape {
			if b == '\\' {
				escape = true
			} else if b == '*' {
				if prevWildcard {
					continue
				}
				prevWildcard = true
			} else {
				prevWildcard = false
				escape = false
			}
		} else {
			prevWildcard = false
			escape = false
		}
		newPattern = append(newPattern, byte(b))
	}
	return string(newPattern)
}
