package trie

import (
	"fmt"
	"gstash/helper/cache"
)

type NodeType int

const (
	nodeCOMMON   NodeType = iota // common
	nodeWILDCARD                 // 通配符
)

const (
	EmptyTrie = "EMPTY"
)

type PatternTrie struct {
	root *PNode
}

func (p *PatternTrie) Encode() ([]byte, error) {
	if p.root == nil {
		return []byte(EmptyTrie), nil
	}
	return cache.GobEncode(p.root)
}

func (p *PatternTrie) Decode(b []byte) error {
	if string(b) == EmptyTrie {
		return nil
	}
	if p.root == nil {
		p.root = &PNode{}
	}
	return cache.GobDecode(b, p.root)
}

//都改成大写开头，因为这样才能序列化
type PNode struct {
	Child    []*PNode      //子孩子们
	Childidx []byte        //每个child的第一个byte
	Wcard    *PNode        //通配符节点
	S        string        //包含的key
	V        []interface{} //包含的值
	Typ      NodeType      //通配符或者普通节点
	Leaf     bool          //是否是子节点
}

//调试用的print
func (p *PatternTrie) PrintTree() {
	p.root.Print(0)
}

func (p *PNode) Print(layer int) {
	fmt.Println("layer", layer)
	fmt.Printf("S: %s, V: %v, Typ:%d, Leaf:%v, ", p.S, p.V, p.Typ, p.Leaf)
	if p.Wcard != nil {
		fmt.Println("Wcard:")
		p.Wcard.Print(layer + 1)
	}
	for i, c := range p.Child {
		fmt.Println("==========")
		fmt.Printf("S %s 'S childs %d ", p.S, i)
		fmt.Println()
		c.Print(layer + 1)
	}
}
