package common

import "time"

const HllMetricNamespace = "hllci"

//代码中使用的一些常量
const (
	STRING_EMPTY       = ""
	STRING_JSON_EMPTY  = "{}"
	STRING_ARRAY_EMPTY = "[]"
	STRING_NULL_VALUE  = "null"
	STRING_DELIMITER   = "-"
)

//秘钥定义
var (
	// The AES key either 16 or 32 bytes to select AES-128 or AES-256.
	AESKey           = []byte("9869c7503f92b79d1f448a34d300f198")
	LocalLocation, _ = time.LoadLocation("Asia/Shanghai")
)
