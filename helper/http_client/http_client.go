/**
 * @note
 * http_client.go
 *
 * <AUTHOR>
 * @date 	2020-09-10
 */
package http_client

import (
	"github.com/sirupsen/logrus"
	goResty "github.com/tmsong/go-resty"
	"github.com/tmsong/hlog"
	errutil "github.com/tmsong/utils/error"
	"gstash/helper/logger"
	"gstash/helper/utils"
	"net/http"
)

const (
	MAX_PRINT_BODY_LEN = 1e6
)

var (
	errHttp = errutil.NewFactory("http failed: %v")
)

func NewHttpClient(logger *hlog.Logger, printLog bool) (c *goResty.Client) {
	c = goResty.New()
	c.PrintLog = printLog
	c.SetLogger(logger)
	c.OnResponseLog(httpLoggerFunc)
	return
}

func httpLoggerFunc(l goResty.Logger, rl *goResty.ResponseLog) error {
	var err error
	if !utils.IsNil(rl.Error()) || rl.ResCode() != http.StatusOK {
		err = errHttp.New(nil, rl.Error())
	}
	f := logrus.Fields{
		"tag":       hlog.LogTagRequestOk,
		"api":       rl.UrlPath(),
		"proc_time": float64(rl.ProcTime().Nanoseconds() / 1e6),
		"url":       rl.UrlString(),
		"out":       rl.ResBody(MAX_PRINT_BODY_LEN, false),
		"get":       rl.UrlQuery(),
		"post":      rl.ReqBody(MAX_PRINT_BODY_LEN, false),
		"header":    rl.ReqHeader(),
		"method":    rl.ReqMethod(),
		"httpCode":  rl.ResCode(),
	}
	if hl, ok := l.(*hlog.Logger); ok && hl != nil {
		logger.PrintLogWithError(err, hl, f)
	}
	return nil
}
