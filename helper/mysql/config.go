/**
 * @note
 * Mysql Helper的配置项
 *
 * <AUTHOR>
 * @date 	2019-10-29
 */
package mysql

import (
	"errors"
	"fmt"
	log "github.com/sirupsen/logrus"
	"github.com/tmsong/hlog"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"os"
	"strings"
)

var config map[string]MysqlConfig

type MysqlConfig struct {
	DataSourceName string `json:"dataSourceName,omitempty" yaml:"dataSourceName"`
	MaxIdleConns   int    `json:"maxIdleConns,omitempty" yaml:"maxIdleConns"`
	MaxOpenConns   int    `json:"maxOpenConns,omitempty" yaml:"maxOpenConns"`
	MaxRetryTimes  int    `json:"maxRetryTimes,omitempty" yaml:"maxRetryTimes"`
}

func SetupConfig(c map[string]MysqlConfig) {
	config = c
}

func getConfig(dbName string) (dbCfg MysqlConfig) {
	if len(config) == 0 {
		return
	}
	if cfg, ok := config[dbName]; ok {
		return cfg
	} else {
		for k, v := range config { //随便返回一个
			log.Errorf("can't get proper mysql config: %s, return a random one: %s", dbName, k)
			return v
		}
	}
	return
}

func GetDB(dbName string, l *hlog.Logger) (db *gorm.DB, err error) {
	if config == nil || len(config) == 0 {
		return nil, errors.New("mysql config is nil")
	}
	if v, ok := gormPool.Load(dbName); ok {
		db = v.(*gorm.DB)
		return db.Session(&gorm.Session{
			Logger: &GormLogger{hl: l}, //设置logger
		}), nil
	}
	dbCfg := getConfig(dbName)
	if dbCfg.DataSourceName != "" {
		db, err = gorm.Open(mysql.Open(dbCfg.DataSourceName), &gorm.Config{Logger: &GormLogger{hl: l}})
		if err != nil {
			go sendDBConnFailureNotify(l, map[string]interface{}{
				"ErrDetail": err.Error(),
				"DataSourceName": func() string {
					dsn := dbCfg.DataSourceName
					dsn = dsn[strings.Index(dsn, "@tcp"):]
					dsn = dsn[:strings.Index(dsn, "?")]
					return dsn
				}(),
				"Host": func() string {
					hostname, _ := os.Hostname()
					return hostname
				}(),
				"Trace": l.GetTrace(),
			})
			return nil, err
		}
		d, err := db.DB() //没有使用gorm.ConnPool的情况下
		if err != nil {
			return nil, err
		}
		d.SetMaxIdleConns(dbCfg.MaxIdleConns)
		d.SetMaxOpenConns(dbCfg.MaxOpenConns)
		gormPool.Store(dbName, db)
		return db, nil
	} else {
		return nil, fmt.Errorf("mysql [dbName:%s] has no config",
			dbName)
	}
}

func GetDefaultDB(l *hlog.Logger) (db *gorm.DB, err error) {
	return GetDB("default", l)
}

func GetDataSecDB(l *hlog.Logger) (db *gorm.DB, err error) {
	return GetDB("datasec", l)
}

func GetSECWorkorderDB(l *hlog.Logger) (db *gorm.DB, err error) {
	return GetDB("sec_workorder", l)
}

func GetJumpserverDB(l *hlog.Logger) (db *gorm.DB, err error) {
	return GetDB("jumpserver", l)
}

func sendDBConnFailureNotify(logger *hlog.Logger, data map[string]interface{}) {
}
