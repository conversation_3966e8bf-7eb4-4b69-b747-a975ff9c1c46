/**
 * @note
 * gorm_logger
 *
 * <AUTHOR>
 * @date 	2020-10-10
 */
package mysql

import (
	"context"
	"github.com/sirupsen/logrus"
	"github.com/tmsong/hlog"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/utils"
	"strings"
	"time"
)

type GormLogger struct {
	hl *hlog.Logger
}

func NewGormLogger(l *hlog.Logger) *GormLogger {
	return &GormLogger{
		hl: l,
	}
}

func (l *GormLogger) LogMode(level logger.LogLevel) logger.Interface {
	switch level {
	case logger.Silent:
		l.hl.SetLevel(logrus.PanicLevel)
	case logger.Error:
		l.hl.SetLevel(logrus.ErrorLevel)
	case logger.Warn:
		l.hl.SetLevel(logrus.WarnLevel)
	case logger.Info:
		if l.hl.GetLevel() < logrus.InfoLevel {
			l.hl.SetLevel(logrus.InfoLevel)
		}
	}
	return l
}

// 屏蔽gorm的Info，Warn和Error
func (l *GormLogger) Info(ctx context.Context, msg string, data ...interface{}) {
	//l.hl.Infof(msg, data...)
}
func (l *GormLogger) Warn(ctx context.Context, msg string, data ...interface{}) {
	//l.hl.Warnf(msg, data...)
}
func (l *GormLogger) Error(ctx context.Context, msg string, data ...interface{}) {
	//l.hl.Errorf(msg, data...)
}

func (l *GormLogger) Trace(ctx context.Context, begin time.Time, fc func() (sql string, rowsAffected int64), err error) {
	if !l.hl.IsLevelEnabled(logrus.DebugLevel) {
		return
	}
	procTime := float64(time.Now().Sub(begin).Nanoseconds()) / 1e6
	sql, rowsAffected := fc()
	line := utils.FileWithLineNum()
	sql = strings.Replace(sql, "_##_", `%`, -1)
	tag := hlog.LogTagMysqlOk
	errString := hlog.LogOK
	if err != nil && err != gorm.ErrRecordNotFound {
		tag = hlog.LogTagMysqlErr
		errString = err.Error()
	} else if err == gorm.ErrRecordNotFound { //暂时认为查不到是正常的
		err = nil
	}
	fields := logrus.Fields{
		"tag":       tag,
		"line":      line,
		"sql":       sql,
		"affected":  rowsAffected,
		"api":       "sql",
		"proc_time": procTime,
	}
	if err != nil {
		l.hl.WithFields(fields).Errorln(errString)
	} else {
		l.hl.WithFields(fields).Infoln(errString)
	}
}
