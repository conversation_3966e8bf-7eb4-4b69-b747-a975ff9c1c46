/**
 * @note
 * 一个模块化的mysql 查询 helper
 *
 * <AUTHOR>
 * @date 	2019-10-29
 */
package mysql

import (
	"fmt"
	"gorm.io/gorm"
	"strings"
)

type QueryMessage struct {
	Offset     *int
	Limit      *int
	Conditions []QueryCondition
	OrderBys   []QueryOrderBy
	Fields     []string
}

type QueryCondition struct {
	Name  string
	Op    string
	Value interface{}
}

type QueryOrderBy struct {
	Field     string
	Direction string
}

func (this *QueryMessage) Build(db *gorm.DB) *gorm.DB {
	if this.Offset != nil {
		db = db.Offset(*this.Offset)
	}
	if this.Limit != nil {
		db = db.Limit(*this.Limit)
	}
	for _, orderBy := range this.OrderBys {
		db = db.Order(fmt.Sprintf("%s %s", orderBy.Field, orderBy.Direction))
	}
	for _, cond := range this.Conditions {
		op := strings.ToUpper(cond.Op)
		switch op {
		case "IS NULL":
			fallthrough
		case "IS NOT NULL":
			db = db.Where(fmt.Sprintf("%s %s", cond.Name, op))
		case "IN":
			fallthrough
		case "NOT IN":
			db = db.Where(fmt.Sprintf("%s %s (?)", cond.Name, op), cond.Value)
		default:
			db = db.Where(fmt.Sprintf("%s %s ?", cond.Name, op), cond.Value)
		}
	}
	return db
}

func (this *QueryMessage) Find(db *gorm.DB, out interface{}) *gorm.DB {
	db = this.Build(db)
	if len(this.Fields) > 0 {
		db = db.Select(strings.Join(this.Fields, ","))
	}
	return db.Find(out)
}

func (this *QueryMessage) Count(db *gorm.DB, val *int64) *gorm.DB {
	db = this.Build(db)
	return db.Count(val)
}

func (this *QueryMessage) RemoveConditionByName(conditionName string) *QueryMessage {
	for idx, condition := range this.Conditions {
		if condition.Name == conditionName {
			this.Conditions = append(this.Conditions[:idx], this.Conditions[idx+1:]...)
		}
	}
	return this
}

func (this *QueryMessage) ReplaceConditionByName(condName string, cond *QueryCondition) *QueryMessage {
	for idx, condition := range this.Conditions {
		if condition.Name == condName {
			if cond == nil {
				this.Conditions = append(this.Conditions[:idx], this.Conditions[idx+1:]...)
			} else {
				this.Conditions[idx] = *cond
			}
		}
	}
	return this
}
