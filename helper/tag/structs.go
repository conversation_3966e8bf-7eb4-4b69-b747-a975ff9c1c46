/**
 * @note
 * structs
 *
 * <AUTHOR>
 * @date 	2021-03-08
 */
package tag

const (
	TAG_TYPE_IP_LOCATION = "ipLocation" //ip地理位置
	TAG_TYPE_UA          = "userAgent"  //常用UA

	TAG_TYPE_WIKI_SPACE_NAME       = "wikiSpaceName"     //wiki空间名称
	TAG_TYPE_HLLER_DEPARTMENT_PATH = "hllerDeptPath"     //hller部门路径
	TAG_TYPE_HLLER_WORKING_STATE   = "hllerWorkingState" //hller是否正在离职
	TAG_TYPE_HLLER_JOB_NAME        = "hllerJobName"      //hller职务名称
)

const (
	RESOURCE_TYPE_HLLER          = "hller"
	RESOURCE_TYPE_WIKI_PAGE_ID   = "wikiPageId"   //wiki页面id
	RESOURCE_TYPE_WIKI_SPACE_KEY = "wikiSpaceKey" //wiki空间key
)

const (
	QUERY_API  = "/tag/query"
	ADD_API    = "/tag/add"
	MODIFY_API = "/tag/modify"
	DELETE_API = "/tag/delete"
	CLEAR_API  = "/tag/clear"
)

type TagInfo struct {
	TagName string `json:"tagName" validate:"required"`
	TagType string `json:"tagType" validate:"required"`
}

type ResourceAndTags struct {
	ResourceId   string    `json:"resourceId" validate:"required"`
	ResourceType string    `json:"resourceType" validate:"required"`
	Tags         []TagInfo `json:"tags" validate:"gt=0,dive"`
}

type ResourceAndTagTypes struct {
	ResourceId   string   `json:"resourceId" validate:"required"`
	ResourceType string   `json:"resourceType" validate:"required"`
	TagTypes     []string `json:"tagTypes" validate:"gt=0,dive,required"`
}

type TagsApiResp struct {
	Code    int         `json:"ret"`
	Message string      `json:"msg"`
	Desc    interface{} `json:"data"`
}
