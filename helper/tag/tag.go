/**
 * @note
 * tag
 *
 * <AUTHOR>
 * @date 	2021-03-08
 */
package tag

import (
	"fmt"
	goResty "github.com/tmsong/go-resty"
	"gstash/helper/utils"
)

func QueryTag(httpCli *goResty.Client, resAndTagTypes []ResourceAndTagTypes) ([]ResourceAndTags, error) {
	resp, err := httpCli.R().
		SetResult(&TagsApiResp{}).
		SetBody(resAndTagTypes).
		Post(GetConfig().Host + QUERY_API)
	if err != nil {
		return nil, err
	} else if tagResp := resp.Result().(*TagsApiResp); tagResp.Code != 0 {
		return nil, fmt.Errorf("query tag error, code: %d, msg: %s", tagResp.Code, tagResp.Message)
	} else {
		ret := make([]ResourceAndTags, 0)
		err = utils.InterfaceToStruct(tagResp.Desc, &ret)
		return ret, err
	}
}

func AddTag(httpCli *goResty.Client, tags []ResourceAndTags) error {
	resp, err := httpCli.R().
		SetResult(&TagsApiResp{}).
		SetBody(tags).
		Post(GetConfig().Host + ADD_API)
	if err != nil {
		return err
	} else if tagResp := resp.Result().(*TagsApiResp); tagResp.Code != 0 {
		return fmt.Errorf("add tag error, code: %d, msg: %s", tagResp.Code, tagResp.Message)
	}
	return nil
}

func ModifyTag(httpCli *goResty.Client, tags []ResourceAndTags) error {
	resp, err := httpCli.R().
		SetResult(&TagsApiResp{}).
		SetBody(tags).
		Post(GetConfig().Host + MODIFY_API)
	if err != nil {
		return err
	} else if tagResp := resp.Result().(*TagsApiResp); tagResp.Code != 0 {
		return fmt.Errorf("modify tag error, code: %d, msg: %s", tagResp.Code, tagResp.Message)
	}
	return nil
}

func DeleteTag(httpCli *goResty.Client, tags []ResourceAndTags) error {
	resp, err := httpCli.R().
		SetResult(&TagsApiResp{}).
		SetBody(tags).
		Post(GetConfig().Host + DELETE_API)
	if err != nil {
		return err
	} else if tagResp := resp.Result().(*TagsApiResp); tagResp.Code != 0 {
		return fmt.Errorf("delete tag error, code: %d, msg: %s", tagResp.Code, tagResp.Message)
	}
	return nil
}

func ClearTag(httpCli *goResty.Client, resAndTagTypes []ResourceAndTagTypes) error {
	resp, err := httpCli.R().
		SetResult(&TagsApiResp{}).
		SetBody(resAndTagTypes).
		Post(GetConfig().Host + CLEAR_API)
	if err != nil {
		return err
	} else if tagResp := resp.Result().(*TagsApiResp); tagResp.Code != 0 {
		return fmt.Errorf("clear tag error, code: %d, msg: %s", tagResp.Code, tagResp.Message)
	}
	return nil
}
