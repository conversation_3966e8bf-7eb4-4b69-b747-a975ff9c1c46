/**
 * @note
 * log
 *
 * <AUTHOR>
 * @date 	2020-11-30
 */
package logger

import (
	"github.com/sirupsen/logrus"
	"github.com/tmsong/hlog"
)

var Logger *hlog.Logger

type LogConfig struct {
	LogLevel  string           `json:"logLevel,omitempty" yaml:"logLevel"`
	LocalLog  bool             `json:"localLog,omitempty" yaml:"localLog"`
	KafkaLog  bool             `json:"kafkaLog,omitempty" yaml:"kafkaLog"`
	Kafka     hlog.KafkaConfig `json:"kafka" yaml:"kafka"`
	LogFile   string           `json:"logFile,omitempty" yaml:"logFile"`
	Interval  int64            `json:"interval,omitempty" yaml:"interval"`
	MaxAge    int64            `json:"maxAge,omitempty" yaml:"maxAge"`
	MaxSize   int64            `json:"maxSize,omitempty" yaml:"maxSize"`
	LocalTime bool             `json:"localTime,omitempty" yaml:"localTime"`
}

/**
 * @note
 * 根据error来打印日志
 * @param error err 错误
 * @param *hlog.Logger 日志句柄
 * @param logrus.Fields fields 指定的fields, 用于叠加
 *
 */
func PrintLogWithError(err error, l *hlog.Logger, fields ...logrus.Fields) {
	var log interface{}
	var code interface{}
	var tag interface{}
	f := logrus.Fields{}
	if len(fields) >= 1 {
		if item, ok := fields[0][hlog.LogCodeName]; ok {
			code = item
		}
		if item, ok := fields[0][hlog.LogTag]; ok {
			tag = item
		}
		e := l.WithFields(fields[0])
		for i := 1; i < len(fields); i++ {
			if item, ok := fields[i][hlog.LogCodeName]; ok {
				code = item
			}
			if item, ok := fields[i][hlog.LogTag]; ok {
				tag = item
			}
			e = e.WithFields(fields[i])
		}
		log = e
	} else {
		log = l
	}
	if code != nil {
		f = logrus.Fields{
			hlog.LogCodeName: code,
		}
	} else {
		f = logrus.Fields{
			hlog.LogCodeName: 0,
		}
	}
	if tag != nil {
		f[hlog.LogTag] = tag
	}
	if err != nil {
		//更换tag
		if v, ok := f[hlog.LogTag]; ok {
			if _, ok := hlog.TagDescSuccMapErr[v.(string)]; ok {
				f[hlog.LogTag] = hlog.TagDescSuccMapErr[v.(string)]
			}
		}
		if _, ok := log.(*logrus.Entry); ok {
			log.(*logrus.Entry).WithFields(f).Errorln(err.(error).Error())
		} else {
			log.(*hlog.Logger).WithFields(f).Errorln(err.(error).Error())
		}
	} else {
		if _, ok := log.(*logrus.Entry); ok {
			log.(*logrus.Entry).WithFields(f).Infoln(hlog.LogOK)
		} else {
			log.(*hlog.Logger).WithFields(f).Infoln(hlog.LogOK)
		}
	}
}
