/**
 * @note
 * utils
 *
 * <AUTHOR>
 * @date 	2020-12-11
 */
package utils

import (
	"bytes"
	"encoding/json"
	"gopkg.in/bufio.v1"
	"math/rand"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"
)

var rnd *rand.Rand
var mu sync.Mutex

var commonInitialisms = []string{"ACL", "API", "ASCII", "CPU", "CSS", "DNS", "EOF", "GUID", "HTML", "HTTP", "HTTPS", "ID", "IP", "JSON", "LHS", "QPS", "RAM", "RHS", "RPC", "SLA", "SMTP", "SQL", "SSH", "TCP", "TLS", "TTL", "UDP", "UI", "UID", "UUID", "URI", "URL", "UTF8", "VM", "XML", "XMPP", "XSRF", "XSS"}
var commonInitialismsReplacer *strings.Replacer
var uncommonInitialismsReplacer *strings.Replacer

func init() {
	var commonInitialismsForReplacer []string
	var uncommonInitialismsForReplacer []string
	for i := len(commonInitialisms) - 1; i >= 0; i-- {
		initialism := commonInitialisms[i]
		commonInitialismsForReplacer = append(commonInitialismsForReplacer, initialism, strings.Title(strings.ToLower(initialism)))
		uncommonInitialismsForReplacer = append(uncommonInitialismsForReplacer, strings.Title(strings.ToLower(initialism)), initialism)
	}
	commonInitialismsReplacer = strings.NewReplacer(commonInitialismsForReplacer...)
	uncommonInitialismsReplacer = strings.NewReplacer(uncommonInitialismsForReplacer...)

	rnd = rand.New(rand.NewSource(time.Now().UnixNano()))
}

func InterfaceToStruct(i interface{}, v interface{}) (err error) {
	buf := bufio.NewBuffer([]byte{})
	encoder := json.NewEncoder(buf)
	if err = encoder.Encode(i); err != nil {
		return err
	}
	decoder := json.NewDecoder(buf)
	decoder.UseNumber()
	return decoder.Decode(v)
}

func MapToStruct(m map[string]interface{}, v interface{}) (err error) {
	buf := bufio.NewBuffer([]byte{})
	encoder := json.NewEncoder(buf)
	if err = encoder.Encode(m); err != nil {
		return err
	}
	decoder := json.NewDecoder(buf)
	decoder.UseNumber()
	return decoder.Decode(v)
}

func IsNil(src interface{}) bool {
	if src == nil {
		return true
	}
	switch reflect.TypeOf(src).Kind() {
	case reflect.Ptr, reflect.Map, reflect.Interface:
		if reflect.ValueOf(src).IsNil() {
			return true
		}
	case reflect.Slice, reflect.Array:
		if reflect.ValueOf(src).IsNil() {
			return true
		}
	}
	return false
}

func MaxInt(x, y int) int {
	if x > y {
		return x
	}
	return y
}

/**
 * @note
 * 得到长度为lenNum的随机字符串 （数字）
 * @param int lenNum 数字串的长度
 *
 * @return string
 */
func RandNumString(lenNum int) string {
	str := "1234567890"
	ret := make([]byte, lenNum)
	length := len(str)
	for i := 0; i < lenNum; i++ {
		mu.Lock()
		pos := rnd.Intn(length)
		mu.Unlock()
		ret[i] = str[pos]
	}
	return string(ret)
}

/**
 * @note
 * 得到长度为lenNum的随机数字与大小写字母的组合
 * @param int lenNum 字符串的长度
 *
 * @return string
 */
func RandStr(lenNum int) string {
	str := "1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"
	ret := make([]byte, lenNum)
	length := len(str)
	for i := 0; i < lenNum; i++ {
		mu.Lock()
		pos := rnd.Intn(length)
		mu.Unlock()
		ret[i] = str[pos]
	}
	return string(ret)
}

func IsLocalIp(ip string) bool {
	//过滤内网IP
	if ip == "127.0.0.1" {
		return true
	}
	if strings.HasPrefix(ip, "10.") ||
		strings.HasPrefix(ip, "192.168.") {
		return true
	}
	if strings.HasPrefix(ip, "172.") {
		if tmp := strings.Split(ip, "."); len(tmp) == 4 {
			if b, err := strconv.Atoi(tmp[1]); err == nil && 16 <= b && b <= 31 {
				return true
			}
		}
	}
	return false
}

func CamelToSnake(name string) string {
	if name == "" {
		return ""
	}

	var (
		value                                    = commonInitialismsReplacer.Replace(name)
		buf                                      = bytes.NewBufferString("")
		lastCase, currCase, nextCase, nextNumber bool
	)

	for i, v := range value[:len(value)-1] {
		nextCase = bool(value[i+1] >= 'A' && value[i+1] <= 'Z')
		nextNumber = bool(value[i+1] >= '0' && value[i+1] <= '9')

		if i > 0 {
			if currCase == true {
				if lastCase == true && (nextCase == true || nextNumber == true) {
					buf.WriteRune(v)
				} else {
					if value[i-1] != '_' && value[i+1] != '_' {
						buf.WriteRune('_')
					}
					buf.WriteRune(v)
				}
			} else {
				buf.WriteRune(v)
				if i == len(value)-2 && (nextCase == true && nextNumber == false) {
					buf.WriteRune('_')
				}
			}
		} else {
			currCase = true
			buf.WriteRune(v)
		}
		lastCase = currCase
		currCase = nextCase
	}

	buf.WriteByte(value[len(value)-1])

	s := strings.ToLower(buf.String())
	return s
}

func CopyJsonMap(value interface{}) interface{} {
	if valueMap, ok := value.(map[string]interface{}); ok {
		newMap := make(map[string]interface{})
		for k, v := range valueMap {
			newMap[k] = CopyJsonMap(v)
		}
		return newMap
	} else if valueSlice, ok := value.([]interface{}); ok {
		newSlice := make([]interface{}, len(valueSlice))
		for k, v := range valueSlice {
			newSlice[k] = CopyJsonMap(v)
		}
		return newSlice
	}
	return value
}
