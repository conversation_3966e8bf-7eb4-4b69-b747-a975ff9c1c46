package modloader

import (
	codec<PERSON><PERSON> "gstash/codec/json"
	"gstash/config"
	filteraddfield "gstash/filter/addfield"
	filtercodec "gstash/filter/codec"
	filtercond "gstash/filter/cond"
	filterdate "gstash/filter/date"
	filterenrichmysql "gstash/filter/enrichmysql"
	filterexpand "gstash/filter/expand"
	filtergeoip2 "gstash/filter/geoip2"
	filtergonx "gstash/filter/gonx"
	filtergrok "gstash/filter/grok"
	filterhllgitlabapplication "gstash/filter/hll/hll-gitlab-application"
	filterhllgitlabrails "gstash/filter/hll/hll-gitlab-rails"
	filterhllgitlabshell "gstash/filter/hll/hll-gitlab-shell"
	filterhlliplocation "gstash/filter/hll/hll-ip-location"
	filterjson "gstash/filter/json"
	filterkv "gstash/filter/kv"
	filtermaxlencut "gstash/filter/maxlencut"
	filtermutate "gstash/filter/mutate"
	filterratedrop "gstash/filter/ratedrop"
	filterratelimit "gstash/filter/ratelimit"
	filterremoveduplicate "gstash/filter/removeduplicate"
	filterremovefield "gstash/filter/removefield"
	filtersplit "gstash/filter/split"
	filterstrmap "gstash/filter/strmap"
	filtertimedrop "gstash/filter/timedrop"
	filtertypeconv "gstash/filter/typeconv"
	filterurlparam "gstash/filter/urlparam"
	filteruseragent "gstash/filter/useragent"
	filtervalidateutf8 "gstash/filter/validateutf8"

	inputs3 "gstash/input/aws_s3"
	inputbeats "gstash/input/beats"
	inputcloudflare "gstash/input/cloudflare"
	inputconstant "gstash/input/constant"
	inputdockerlog "gstash/input/dockerlog"
	inputdockerstats "gstash/input/dockerstats"
	inputexec "gstash/input/exec"
	inputfile "gstash/input/file"
	inputgenerator "gstash/input/generator"
	inputhttp "gstash/input/http"
	inputhttplisten "gstash/input/httplisten"
	inputkafka "gstash/input/kafka"
	inputlarkaudit "gstash/input/lark_audit"
	inputlorem "gstash/input/lorem"
	inputmongo "gstash/input/mongo"
	inputmysql "gstash/input/mysql"
	inputnats "gstash/input/nats"
	inputredis "gstash/input/redis"
	inputsls "gstash/input/sls"
	inputsocket "gstash/input/socket"
	inputsyslog "gstash/input/syslog"
	inputzendesk "gstash/input/zendesk"

	outputamqp "gstash/output/amqp"
	outputbeats "gstash/output/beats"
	outputcond "gstash/output/cond"
	outputdots "gstash/output/dots"
	outputdrop "gstash/output/drop"
	outputelastic "gstash/output/elastic"
	outputemail "gstash/output/email"
	outputfile "gstash/output/file"
	outputhttp "gstash/output/http"
	outputkafka "gstash/output/kafka"
	outputmysql "gstash/output/mysql"
	outputprometheus "gstash/output/prometheus"
	outputredis "gstash/output/redis"
	outputsocket "gstash/output/socket"
	outputstdout "gstash/output/stdout"
	outputworker "gstash/output/worker"
)

func init() {
	config.RegistInputHandler(inputbeats.ModuleName, inputbeats.InitHandler)
	config.RegistInputHandler(inputdockerlog.ModuleName, inputdockerlog.InitHandler)
	config.RegistInputHandler(inputdockerstats.ModuleName, inputdockerstats.InitHandler)
	config.RegistInputHandler(inputexec.ModuleName, inputexec.InitHandler)
	config.RegistInputHandler(inputfile.ModuleName, inputfile.InitHandler)
	config.RegistInputHandler(inputhttp.ModuleName, inputhttp.InitHandler)
	config.RegistInputHandler(inputhttplisten.ModuleName, inputhttplisten.InitHandler)
	config.RegistInputHandler(inputkafka.ModuleName, inputkafka.InitHandler)
	config.RegistInputHandler(inputlorem.ModuleName, inputlorem.InitHandler)
	config.RegistInputHandler(inputnats.ModuleName, inputnats.InitHandler)
	config.RegistInputHandler(inputredis.ModuleName, inputredis.InitHandler)
	config.RegistInputHandler(inputsocket.ModuleName, inputsocket.InitHandler)
	config.RegistInputHandler(inputgenerator.ModuleName, inputgenerator.InitHandler)
	config.RegistInputHandler(inputconstant.ModuleName, inputconstant.InitHandler)
	config.RegistInputHandler(inputmysql.ModuleName, inputmysql.InitHandler)
	config.RegistInputHandler(inputmongo.ModuleName, inputmongo.InitHandler)
	config.RegistInputHandler(inputsyslog.ModuleName, inputsyslog.InitHandler)
	config.RegistInputHandler(inputs3.ModuleName, inputs3.InitHandler)
	config.RegistInputHandler(inputcloudflare.ModuleName, inputcloudflare.InitHandler)
	config.RegistInputHandler(inputsls.ModuleName, inputsls.InitHandler)
	config.RegistInputHandler(inputzendesk.ModuleName, inputzendesk.InitHandler)
	config.RegistInputHandler(inputlarkaudit.ModuleName, inputlarkaudit.InitHandler)

	config.RegistFilterHandler(filteraddfield.ModuleName, filteraddfield.InitHandler)
	config.RegistFilterHandler(filtercond.ModuleName, filtercond.InitHandler)
	config.RegistFilterHandler(filterdate.ModuleName, filterdate.InitHandler)
	config.RegistFilterHandler(filtergeoip2.ModuleName, filtergeoip2.InitHandler)
	config.RegistFilterHandler(filtergonx.ModuleName, filtergonx.InitHandler)
	config.RegistFilterHandler(filtergrok.ModuleName, filtergrok.InitHandler)
	config.RegistFilterHandler(filterjson.ModuleName, filterjson.InitHandler)
	config.RegistFilterHandler(filtermutate.ModuleName, filtermutate.InitHandler)
	config.RegistFilterHandler(filterstrmap.ModuleName, filterstrmap.InitHandler)
	config.RegistFilterHandler(filterratelimit.ModuleName, filterratelimit.InitHandler)
	config.RegistFilterHandler(filterremovefield.ModuleName, filterremovefield.InitHandler)
	config.RegistFilterHandler(filtertypeconv.ModuleName, filtertypeconv.InitHandler)
	config.RegistFilterHandler(filteruseragent.ModuleName, filteruseragent.InitHandler)
	config.RegistFilterHandler(filterurlparam.ModuleName, filterurlparam.InitHandler)
	config.RegistFilterHandler(filtercodec.ModuleName, filtercodec.InitHandler)
	config.RegistFilterHandler(filterenrichmysql.ModuleName, filterenrichmysql.InitHandler)
	config.RegistFilterHandler(filterratedrop.ModuleName, filterratedrop.InitHandler)
	config.RegistFilterHandler(filtersplit.ModuleName, filtersplit.InitHandler)
	config.RegistFilterHandler(filtermaxlencut.ModuleName, filtermaxlencut.InitHandler)
	config.RegistFilterHandler(filtervalidateutf8.ModuleName, filtervalidateutf8.InitHandler)
	config.RegistFilterHandler(filterremoveduplicate.ModuleName, filterremoveduplicate.InitHandler)
	config.RegistFilterHandler(filterexpand.ModuleName, filterexpand.InitHandler)
	config.RegistFilterHandler(filtertimedrop.ModuleName, filtertimedrop.InitHandler)
	config.RegistFilterHandler(filterkv.ModuleName, filterkv.InitHandler)

	//hll filter
	config.RegistFilterHandler(filterhlliplocation.ModuleName, filterhlliplocation.InitHandler)
	config.RegistFilterHandler(filterhllgitlabshell.ModuleName, filterhllgitlabshell.InitHandler)
	config.RegistFilterHandler(filterhllgitlabrails.ModuleName, filterhllgitlabrails.InitHandler)
	config.RegistFilterHandler(filterhllgitlabapplication.ModuleName, filterhllgitlabapplication.InitHandler)

	config.RegistOutputHandler(outputamqp.ModuleName, outputamqp.InitHandler)
	config.RegistOutputHandler(outputcond.ModuleName, outputcond.InitHandler)
	config.RegistOutputHandler(outputelastic.ModuleName, outputelastic.InitHandler)
	config.RegistOutputHandler(outputemail.ModuleName, outputemail.InitHandler)
	config.RegistOutputHandler(outputhttp.ModuleName, outputhttp.InitHandler)
	config.RegistOutputHandler(outputprometheus.ModuleName, outputprometheus.InitHandler)
	config.RegistOutputHandler(outputredis.ModuleName, outputredis.InitHandler)
	config.RegistOutputHandler(outputsocket.ModuleName, outputsocket.InitHandler)
	config.RegistOutputHandler(outputstdout.ModuleName, outputstdout.InitHandler)
	config.RegistOutputHandler(outputfile.ModuleName, outputfile.InitHandler)
	config.RegistOutputHandler(outputkafka.ModuleName, outputkafka.InitHandler)
	config.RegistOutputHandler(outputdots.ModuleName, outputdots.InitHandler)
	config.RegistOutputHandler(outputdrop.ModuleName, outputdrop.InitHandler)
	config.RegistOutputHandler(outputbeats.ModuleName, outputbeats.InitHandler)
	config.RegistOutputHandler(outputworker.ModuleName, outputworker.InitHandler)
	config.RegistOutputHandler(outputmysql.ModuleName, outputmysql.InitHandler)

	config.RegistCodecHandler(config.DefaultCodecName, config.DefaultCodecInitHandler)
	config.RegistCodecHandler(codecjson.ModuleName, codecjson.InitHandler)
}
