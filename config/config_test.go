package config

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestLoadFromJSON(t *testing.T) {
	require := require.New(t)
	require.NotNil(require)

	conf, err := LoadFromJSON([]byte(`{
		"input": [{
			"type": "exec",
			"command": "uptime",
			"args": [],
			"interval": 3,
			"message_prefix": "%{@timestamp} "
		},{
			"type": "exec",
			"command": "whoami",
			"args": [],
			"interval": 4,
			"message_prefix": "%{@timestamp} "
		}],
		"output": [{
			"type": "stdout"
		}]
	}`))
	require.NoError(err)

	require.NotNil(conf.Worker[0].chInFilter)
	require.NotNil(conf.Worker[0].chFilterOut)
	require.Nil(conf.Worker[0].ctx)
	require.Nil(conf.Worker[0].eg)
	require.Len(conf.Worker[0].InputRaw, 2)
	require.Len(conf.Worker[0].FilterRaw, 0)
	require.Len(conf.Worker[0].OutputRaw, 1)

	inputs, err := conf.Worker[0].GetInputs()
	require.Error(err)
	require.Len(inputs, 0)

	filters, err := conf.Worker[0].getFilters()
	require.NoError(err)
	require.Len(filters, 0)

	outputs, err := conf.Worker[0].getOutputs()
	require.Error(err)
	require.Len(outputs, 0)

	conf, err = LoadFromJSON([]byte(`{
		"input": [{
			"type": "exec",
			"command": "uptime",
			"args": [],
			"interval": 3,
			"message_prefix": "%{@timestamp} "
		}],
		"output": [{
			"type": "stdout"
		}],
	}`))
	require.Error(err)
}

func TestLoadFromYAML(t *testing.T) {
	require := require.New(t)
	require.NotNil(require)

	conf, err := LoadFromYAML([]byte(strings.TrimSpace(`
input:
  - type: exec
    command: uptime
    args: []
    interval: 3
    message_prefix: "%{@timestamp} "
  - type: exec
    command: whoami
    args: []
    interval: 4
    message_prefix: "%{@timestamp} "
output:
  - type: stdout
	`)))
	require.NoError(err)

	require.NotNil(conf.Worker[0].chInFilter)
	require.NotNil(conf.Worker[0].chFilterOut)
	require.Nil(conf.Worker[0].ctx)
	require.Nil(conf.Worker[0].eg)
	require.Len(conf.Worker[0].InputRaw, 2)
	require.Len(conf.Worker[0].FilterRaw, 0)
	require.Len(conf.Worker[0].OutputRaw, 1)

	inputs, err := conf.Worker[0].GetInputs()
	require.Error(err)
	require.Len(inputs, 0)

	filters, err := conf.Worker[0].getFilters()
	require.NoError(err)
	require.Len(filters, 0)

	outputs, err := conf.Worker[0].getOutputs()
	require.Error(err)
	require.Len(outputs, 0)
}
