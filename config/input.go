package config

import (
	"context"
	errutil "github.com/tmsong/utils/error"
	"gstash/config/logevent"
)

// errors
var (
	ErrorLackInputType     = errutil.NewFactory("workerId: %v lack input config type")
	ErrorUnknownInputType1 = errutil.NewFactory("unknown input config type: %q")
	ErrorInitInputFailed1  = errutil.NewFactory("initialize input module failed: %v")
)

// TypeInputConfig is interface of input module
type TypeInputConfig interface {
	TypeCommonConfig
	Start(ctx context.Context, msgChan chan<- logevent.LogEvent) (err error)
}

// InputConfig is basic input config struct
type InputConfig struct {
	CommonConfig
	Codec TypeCodecConfig `json:"-"`
}

// InputHandler is a handler to regist input module
type InputHandler func(parent *WorkerConfig, ctx context.Context, raw *ConfigRaw) (TypeInputConfig, error)

var (
	mapInputHandler = map[string]InputHandler{}
)

// RegistInputHandler regist a input handler
func RegistInputHandler(name string, handler InputHandler) {
	mapInputHandler[name] = handler
}

func (t *WorkerConfig) getInputs() (inputs []TypeInputConfig, err error) {
	return GetInputs(t, t.ctx, t.InputRaw)
}

func GetInputs(parent *WorkerConfig, ctx context.Context, inputRaw []ConfigRaw) (inputs []TypeInputConfig, err error) {
	var input TypeInputConfig
	for _, raw := range inputRaw {
		inputType, ok := raw["type"]
		if !ok {
			return inputs, ErrorLackInputType.New(nil, parent.Id)
		}
		handler, ok := mapInputHandler[inputType.(string)]
		if !ok {
			return inputs, ErrorUnknownInputType1.New(nil, raw["type"])
		}

		if input, err = handler(parent, ctx, &raw); err != nil {
			return inputs, ErrorInitInputFailed1.New(err, raw)
		}
		inputs = append(inputs, input)
	}
	return
}

// 对于mysql的input要特殊处理一下，让一个进程里，对于同一个worker，最多只有一个协程在运行
func (t *WorkerConfig) startInputs() (err error) {
	for i := 0; i < t.InputConcurrency; i++ {

		inputs, err := t.getInputs()
		if err != nil {
			return err
		}
		for _, input := range inputs {
			func(input TypeInputConfig) {
				t.eg.Go(func() error {
					return input.Start(t.ctx, t.chInFilter)
				})
			}(input)
		}
		for _, v := range inputs {
			if v.GetType() == "mysql" {
				return nil
			}
		}

	}
	return
}
