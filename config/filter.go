package config

import (
	"context"
	"gstash/helper/logger"
	"sync/atomic"

	errutil "github.com/tmsong/utils/error"
	"gstash/config/logevent"
)

// errors
var (
	ErrorLackFilterType     = errutil.NewFactory("workerId: %v lack filter config type")
	ErrorUnknownFilterType1 = errutil.NewFactory("unknown filter config type: %q")
	ErrorInitFilterFailed1  = errutil.NewFactory("initialize filter module failed: %v")
)

// TypeFilterConfig is interface of filter module
type TypeFilterConfig interface {
	TypeCommonConfig
	Event(context.Context, logevent.LogEvent) (events []logevent.LogEvent, err error)
}

// IsConfigured returns whether common configuration has been setup
func (f *FilterConfig) IsConfigured() bool {
	return len(f.AddTags) != 0 || len(f.AddFields) != 0 || len(f.RemoveTags) != 0 || len(f.RemoveFields) != 0
}

// FilterConfig is basic filter config struct
type FilterConfig struct {
	CommonConfig
	AddTags      []string      `yaml:"addTag" json:"addTag"`
	RemoveTags   []string      `yaml:"removeTag" json:"removeTag"`
	AddFields    []FieldConfig `yaml:"addField" json:"addField"`
	RemoveFields []string      `yaml:"removeField" json:"removeField"`
}

// FieldConfig is a name/value field config
type FieldConfig struct {
	Key   string `yaml:"key"`
	Value string `yaml:"value"`
}

// FilterHandler is a handler to regist filter module
type FilterHandler func(parent *WorkerConfig, ctx context.Context, raw *ConfigRaw) (TypeFilterConfig, error)

var (
	mapFilterHandler = map[string]FilterHandler{}
)

// RegistFilterHandler regist a filter handler
func RegistFilterHandler(name string, handler FilterHandler) {
	mapFilterHandler[name] = handler
}

// GetFilters get filters from config
func GetFilters(parent *WorkerConfig, ctx context.Context, filterRaw []ConfigRaw) (filters []TypeFilterConfig, err error) {
	var filter TypeFilterConfig
	for _, raw := range filterRaw {
		filterType, ok := raw["type"]
		if !ok {
			return filters, ErrorLackFilterType.New(nil, parent.Id)
		}
		handler, ok := mapFilterHandler[filterType.(string)]
		if !ok {
			return filters, ErrorUnknownFilterType1.New(nil, raw["type"])
		}

		if filter, err = handler(parent, ctx, &raw); err != nil {
			return filters, ErrorInitFilterFailed1.New(err, raw)
		}
		filters = append(filters, filter)
	}
	return
}

func (t *WorkerConfig) getFilters() (filters []TypeFilterConfig, err error) {
	return GetFilters(t, t.ctx, t.FilterRaw)
}

func (t *WorkerConfig) startFilters() (err error) {
	for i := 0; i < t.FilterConcurrency; i++ {

		filters, err := t.getFilters()
		if err != nil {
			return err
		}

		t.eg.Go(func() error {
			var events []logevent.LogEvent
			var hasErr bool
			originEvents := make([]logevent.LogEvent, 0, 1<<3)   // 用于放上一层event，先随便开辟一点空间
			filteredEvents := make([]logevent.LogEvent, 0, 1<<3) // 用于放下一层event，先随便开辟一点空间
			for {
				select {
				case <-t.ctx.Done():
					if len(t.chInFilter) < 1 {
						return nil
					}
				case event := <-t.chInFilter:
					atomic.AddUint32(&t.inEveCnt, 1) // 统计输入
					hasErr = false
					filteredEvents = filteredEvents[:0]            // 清空数组，层序遍历
					filteredEvents = append(filteredEvents, event) // 放入第一个元素
					for j := range filters {
						originEvents = filteredEvents
						filteredEvents = originEvents[:0] // 交换内存空间
						for k := range originEvents {
							events, err = filters[j].Event(t.ctx, originEvents[k])
							if err != nil {
								logger.Logger.Errorf("worker %s: %s, filter %d: %s, event error: %v", t.Id, t.Name, j, filters[j].GetType(), err)
								if !hasErr {
									atomic.AddUint32(&t.errEventCnt, 1)
									hasErr = true
								}
							}
							filteredEvents = append(filteredEvents, events...)
						}
					}
					for j := range filteredEvents {
						t.chFilterOut <- filteredEvents[j]
					}
				}
			}
		})
	}

	return
}
