package config

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/icza/dyno"
	jsoniter "github.com/json-iterator/go"
	"gstash/config/logevent"
	"gstash/helper/utils"
	"reflect"
	"regexp"
	"runtime"
	"strings"
)

// ReflectConfig set conf from confraw
func ReflectConfig(confraw *ConfigRaw, conf interface{}) (err error) {
	data, err := json.Marshal(dyno.ConvertMapI2MapS(map[string]interface{}(*confraw)))
	if err != nil {
		return
	}

	if err = json.Unmarshal(data, conf); err != nil {
		return
	}

	rv := reflect.ValueOf(conf).Elem()
	formatReflect(rv)

	return
}

// GetFromObject obtaining value from specified field recursively
func GetFromObject(obj map[string]interface{}, field string) interface{} {
	fieldSplits := strings.Split(field, ".")
	for i, key := range fieldSplits {
		if i >= len(fieldSplits)-1 {
			if v, ok := obj[key]; ok {
				return v
			}
			return nil
		} else if node, ok := obj[key]; ok {
			switch v := node.(type) {
			case map[string]interface{}:
				obj = v
			default:
				return nil
			}
		} else {
			break
		}
	}
	return nil
}

func formatReflect(rv reflect.Value) {
	if !rv.IsValid() {
		return
	}

	switch rv.Kind() {
	case reflect.Ptr:
		if !rv.IsNil() {
			formatReflect(rv.Elem())
		}
	case reflect.Struct:
		for i := 0; i < rv.NumField(); i++ {
			field := rv.Field(i)
			formatReflect(field)
		}
	case reflect.String:
		if !rv.CanSet() {
			return
		}
		value := rv.Interface().(string)
		value = logevent.FormatWithEnv(value)
		rv.SetString(value)
	}
}

// cleanComments used for remove non-standard json comments.
// Supported comment formats
// format 1: ^\s*#
// format 2: ^\s*//
func cleanComments(data []byte) (out []byte, err error) {
	reForm1 := regexp.MustCompile(`^\s*#`)
	reForm2 := regexp.MustCompile(`^\s*//`)
	data = bytes.Replace(data, []byte("\r"), []byte(""), 0) // Windows
	lines := bytes.Split(data, []byte("\n"))
	var filtered [][]byte

	for _, line := range lines {
		if reForm1.Match(line) {
			continue
		}
		if reForm2.Match(line) {
			continue
		}
		filtered = append(filtered, line)
	}

	out = bytes.Join(filtered, []byte("\n"))
	return
}

func IdentifyPanic() string {
	var name string
	var file string
	var line int
	var pc [16]uintptr
	var res = []map[string]string{}
	prefix := MODULE_NAME

	n := runtime.Callers(3, pc[:])
	i := 0
	for _, pc := range pc[:n] {
		fn := runtime.FuncForPC(pc)
		if fn == nil {
			continue
		}
		file, line = fn.FileLine(pc)
		file = file[utils.MaxInt(0, strings.Index(file, prefix)):]
		name = strings.TrimPrefix(fn.Name(), prefix)
		res = append(res, map[string]string{
			"func": name,
			"line": fmt.Sprintf("%s:%d", file, line),
		})
		if strings.Contains(name, MODULE_NAME) {
			if i >= 10 && line > 1 {
				break
			}
			i++
		}
	}
	resStr, _ := jsoniter.MarshalToString(res)
	return resStr
}
