package config

import (
	"context"
	errutil "github.com/tmsong/utils/error"
	"golang.org/x/sync/errgroup"
	"gstash/helper/logger"
	"sync/atomic"

	"gstash/config/logevent"
)

// errors
var (
	ErrorLackOutputType     = errutil.NewFactory("workerId: %v lack output config type")
	ErrorUnknownOutputType1 = errutil.NewFactory("unknown output config type: %q")
	ErrorInitOutputFailed1  = errutil.NewFactory("initialize output module failed: %v")
)

// TypeOutputConfig is interface of output module
type TypeOutputConfig interface {
	TypeCommonConfig
	Output(ctx context.Context, event logevent.LogEvent) (err error)
}

// OutputConfig is basic output config struct
type OutputConfig struct {
	CommonConfig
}

// OutputHandler is a handler to regist output module
type OutputHandler func(parent *WorkerConfig, ctx context.Context, raw *ConfigRaw) (TypeOutputConfig, error)

var (
	mapOutputHandler = map[string]OutputHandler{}
)

// RegistOutputHandler regist a output handler
func RegistOutputHandler(name string, handler OutputHandler) {
	mapOutputHandler[name] = handler
}

// GetOutputs get outputs from config
func GetOutputs(parent *WorkerConfig, ctx context.Context, outputRaw []ConfigRaw) (outputs []TypeOutputConfig, err error) {
	var output TypeOutputConfig
	for _, raw := range outputRaw {
		outputType, ok := raw["type"]
		if !ok {
			return outputs, ErrorLackOutputType.New(nil, parent.Id)
		}
		handler, ok := mapOutputHandler[outputType.(string)]
		if !ok {
			return outputs, ErrorUnknownOutputType1.New(nil, raw["type"])
		}

		if output, err = handler(parent, ctx, &raw); err != nil {
			return outputs, ErrorInitOutputFailed1.New(err, raw)
		}
		outputs = append(outputs, output)
	}
	return
}

func (t *WorkerConfig) getOutputs() (outputs []TypeOutputConfig, err error) {
	return GetOutputs(t, t.ctx, t.OutputRaw)
}

func (t *WorkerConfig) startOutputs() (err error) {
	for i := 0; i < t.OutputConcurrency; i++ {

		outputs, err := t.getOutputs()
		if err != nil {
			return err
		}

		t.eg.Go(func() error {
			for {
				select {
				case <-t.ctx.Done():
					if len(t.chInFilter) < 1 && len(t.chFilterOut) < 1 {
						return nil
					}
				case event := <-t.chFilterOut:
					if len(outputs) == 1 { //针对单output输出情况优化
						if errout := outputs[0].Output(t.ctx, event); errout != nil {
							logger.Logger.Errorf("output module %q failed: %v\n", outputs[0].GetType(), errout)
						}
					} else {
						eg, ctx := errgroup.WithContext(t.ctx)
						for _, output := range outputs {
							func(output TypeOutputConfig) {
								eg.Go(func() error {
									if errout := output.Output(ctx, event); errout != nil {
										logger.Logger.Errorf("output module %q failed: %v\n", output.GetType(), errout)
									}
									return nil
								})
							}(output)
						}
						if err := eg.Wait(); err != nil {
							return err
						}
					}
					atomic.AddUint32(&t.outEveCnt, 1) //统计输出
					if t.chOutDebug != nil {
						t.chOutDebug <- event
					}
				}
			}
		})

	}

	return
}
