/**
 * @note
 * status上报
 *
 * <AUTHOR>
 * @date 	2020-11-06
 */
package config

import (
	"github.com/prometheus/client_golang/prometheus"
	"gstash/helper/logger"
	"net"
	"sync/atomic"
	"time"
)

var (
	WorkerInQpsGauge = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Namespace: "sec",
		Subsystem: "gstash",
		Name:      "worker_in_qps",
		Help:      "event input qps of the worker",
	},
		[]string{"worker_id", "worker_name", "host"},
	)
	WorkerOutQpsGauge = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Namespace: "sec",
		Subsystem: "gstash",
		Name:      "worker_out_qps",
		Help:      "event output qps of the worker",
	},
		[]string{"worker_id", "worker_name", "host"},
	)
	WorkerErrQpsGauge = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Namespace: "sec",
		Subsystem: "gstash",
		Name:      "worker_err_qps",
		Help:      "event error qps of the worker",
	},
		[]string{"worker_id", "worker_name", "host"},
	)
	WorkerInFilterChanLengthGauge = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Namespace: "sec",
		Subsystem: "gstash",
		Name:      "worker_in_filter_chan_length",
		Help:      "length of the channel from input to filter",
	},
		[]string{"worker_id", "worker_name", "host"},
	)
	WorkerOutFilterChanLengthGauge = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Namespace: "sec",
		Subsystem: "gstash",
		Name:      "worker_out_filter_chan_length",
		Help:      "length of the channel from filter to output",
	},
		[]string{"worker_id", "worker_name", "host"},
	)
)

func RegisterPrometheus() {
	prometheus.MustRegister(WorkerInQpsGauge)
	prometheus.MustRegister(WorkerOutQpsGauge)
	prometheus.MustRegister(WorkerErrQpsGauge)
	prometheus.MustRegister(WorkerInFilterChanLengthGauge)
	prometheus.MustRegister(WorkerOutFilterChanLengthGauge)
}

const (
	STATUS_REFRESH_SEC = 15
)

func (t *WorkerConfig) startStatus() error {
	host, _ := getHost()
	t.eg.Go(func() error {
		ticker := time.NewTicker(STATUS_REFRESH_SEC * time.Second)
		for {
			select {
			case <-t.ctx.Done():
				if len(t.chInFilter) < 1 && len(t.chFilterOut) < 1 {
					ticker.Stop()
					return nil
				}
			case <-ticker.C:
				//先打出来	TODO 同步状态
				inQPS := float64(atomic.SwapUint32(&t.inEveCnt, 0)) / STATUS_REFRESH_SEC
				outQPS := float64(atomic.SwapUint32(&t.outEveCnt, 0)) / STATUS_REFRESH_SEC
				errQPS := float64(atomic.SwapUint32(&t.errEventCnt, 0)) / STATUS_REFRESH_SEC

				errorRate := float64(0)
				if errQPS+outQPS != 0 {
					errorRate = errQPS / (errQPS + outQPS)
				}

				// metric 上报inQPS、outQPS、errQPS、inRate、outRate、passRate、errorRate、inChanLen、outChanLen
				logger.Logger.Infof("worker %s: %s, inQps: %.3f, outQps: %.3f, errQps: %.3f, errRate: %.3f, inFilter chLen: %d, outFilter chLen: %d",
					t.Id, t.Name, inQPS, outQPS, errQPS, errorRate, len(t.chInFilter), len(t.chFilterOut))

				WorkerInQpsGauge.With(prometheus.Labels{"host": host, "worker_id": t.Id, "worker_name": t.Name}).Set(inQPS)
				WorkerOutQpsGauge.With(prometheus.Labels{"host": host, "worker_id": t.Id, "worker_name": t.Name}).Set(outQPS)
				WorkerErrQpsGauge.With(prometheus.Labels{"host": host, "worker_id": t.Id, "worker_name": t.Name}).Set(errQPS)
				WorkerInFilterChanLengthGauge.With(prometheus.Labels{"host": host, "worker_id": t.Id, "worker_name": t.Name}).Set(float64(len(t.chInFilter)))
				WorkerOutFilterChanLengthGauge.With(prometheus.Labels{"host": host, "worker_id": t.Id, "worker_name": t.Name}).Set(float64(len(t.chFilterOut)))

			}
		}
	})

	return nil
}

func getHost() (string, error) {
	gInnerIP := ""
	addrs, err := net.InterfaceAddrs()
	if err != nil {
		return gInnerIP, err
	}

	for _, address := range addrs {
		if ipnet, ok := address.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
			if ipnet.IP.To4() != nil {
				gInnerIP = ipnet.IP.String()
				break
			}
		}
	}
	return gInnerIP, nil
}
