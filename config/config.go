package config

import (
	"context"
	"encoding/json"
	"errors"
	"github.com/tmsong/hlog"
	"github.com/tmsong/utils/pathvalue"
	"gstash/helper/logger"
	"gstash/helper/mysql"
	"gstash/helper/redis"
	"gstash/helper/tag"
	wcModel "gstash/model/worker_config"
	"io/ioutil"
	"path/filepath"
	"reflect"
	"strings"
	"time"

	errutil "github.com/tmsong/utils/error"
	"golang.org/x/sync/errgroup"
	"gopkg.in/yaml.v2"

	"gstash/config/logevent"
)

const (
	MODULE_NAME          = "gstash"
	DEFAULT_CHANNEL_SIZE = 100
)

func init() {
	pathvalue.InitByCacheSize(1024)
}

var ProcId string

// errors
var (
	ErrorReadConfigFile1     = errutil.NewFactory("Failed to read config file: %q")
	ErrorUnmarshalJSONConfig = errutil.NewFactory("Failed unmarshalling config in JSON format")
	ErrorUnmarshalYAMLConfig = errutil.NewFactory("Failed unmarshalling config in YAML format")
	ErrorTimeout1            = errutil.NewFactory("timeout: %v")
	ErrorEmptyWorkerId       = errutil.NewFactory("worker id is empty")
)

type Config struct {
	PProfAddr      string                       `json:"pprofAddr,omitempty" yaml:"pprofAddr"`
	Debug          bool                         `json:"debug,omitempty" yaml:"debug"`
	Log            logger.LogConfig             `json:"log,omitempty" yaml:"log"`
	Mysql          map[string]mysql.MysqlConfig `json:"mysql,omitempty" yaml:"mysql"`
	Redis          redis.Config                 `json:"redis,omitempty" yaml:"redis"`
	Tag            tag.Config                   `json:"tag,omitempty" yaml:"tag"`
	DbConfig       bool                         `json:"dbConfig" yaml:"dbConfig"`
	Worker         []*WorkerConfig              `json:"worker,omitempty" yaml:"worker"`
	HotUpdate      bool                         `json:"hotUpdate" yaml:"hotUpdate"`
	PrometheusAddr string                       `json:"prometheusAddr" yaml:"prometheusAddr"`
	ConfigPath     string                       `json:"-"`
	LastUpdateTime time.Time                    `json:"-"`
	Env            string                       `json:"env"`
	AppId          string                       `json:"appId"`
}

func SetupConfig(c Config) {
	config = c
}

func GetConfig() Config {
	return config
}

var config Config

func (c *Config) GetWorkerById(id string) *WorkerConfig {
	for _, worker := range c.Worker {
		if worker.Id == id {
			return worker
		}
	}
	return nil
}

func (c *Config) LoadWorkerFromDB(logger *hlog.Logger) (err error) {
	workers := make([]*WorkerConfig, 0)
	if !c.DbConfig {
		return
	}
	model, err := wcModel.NewWorkConfigModel(logger)
	if err != nil {
		logger.Errorf("db query error, reason:%v", err)
		return err
	}
	configs, err := model.QueryWorkerConfigByStatus([]wcModel.WorkerConfigStatus{wcModel.WORKER_CONFIG_STATUS_ENABLED})
	if err != nil {
		logger.Errorf("db query error, reason:%v", err)
		return err
	}
	for _, v := range configs {
		var data WorkerConfig
		err = json.Unmarshal([]byte(v.Config), &data)
		if err != nil {
			logger.Errorf("gstash exited with error, reason:%v", err)
			return err
		}
		data.Id = v.WorkerID
		data.Name = v.WorkerName
		workers = append(workers, &data)
		if v.UpdatedAt.After(c.LastUpdateTime) {
			c.LastUpdateTime = v.UpdatedAt
		}
	}
	return c.mergeWorkerConfig(workers)
}

func (c *Config) mergeWorkerConfig(workersToAppend []*WorkerConfig) (err error) {
	for _, worker := range c.Worker {
		if worker.Id == "" {
			return ErrorEmptyWorkerId.New(errors.New(worker.Name))
		}
	}
	if !c.DbConfig {
		return nil
	}
	var notExistWorkers []*WorkerConfig
	for _, workerToAppend := range workersToAppend {
		if workerToAppend.Id == "" {
			return ErrorEmptyWorkerId.New(errors.New(workerToAppend.Name))
		}
		var exist bool
		for idx := range c.Worker {
			if c.Worker[idx].Id == workerToAppend.Id {
				c.Worker[idx] = workerToAppend
				exist = true
				break
			}
		}
		if !exist {
			notExistWorkers = append(notExistWorkers, workerToAppend)
		}
	}
	c.Worker = append(c.Worker, notExistWorkers...)
	return nil
}

func (c *Config) InitWorkers() {
	for i := range c.Worker {
		initWorkerConfig(c, i)
		logger.Logger.Infof("init worker %d: id %s, name %s", i, c.Worker[i].Id, c.Worker[i].Name)
	}
}

// Config contains all config
type WorkerConfig struct {
	//id, for finding
	Id string `json:"id,omitempty" yaml:"id"`
	//name, for show
	Name string `json:"name,omitempty" yaml:"name"`

	InputRaw  []ConfigRaw `json:"input,omitempty" yaml:"input"`
	FilterRaw []ConfigRaw `json:"filter,omitempty" yaml:"filter"`
	OutputRaw []ConfigRaw `json:"output,omitempty" yaml:"output"`

	Event *logevent.Config `json:"event,omitempty" yaml:"event"`

	// channel size: chInFilter, chFilterOut, chOutDebug
	ChannelSize int `json:"channelSize,omitempty" yaml:"channelSize"`

	InputConcurrency  int `json:"inputConcurrency,omitempty" yaml:"inputConcurrency"`
	FilterConcurrency int `json:"filterConcurrency,omitempty" yaml:"filterConcurrency"`
	OutputConcurrency int `json:"outputConcurrency,omitempty" yaml:"outputConcurrency"`

	// enable debug channel, used for testing
	DebugChannel bool `json:"debugChannel,omitempty" yaml:"debugChannel"`

	chInFilter  MsgChan // channel from input to filter
	chFilterOut MsgChan // channel from filter to output
	chOutDebug  MsgChan // channel from output to debug
	ctx         context.Context
	eg          *errgroup.Group
	inEveCnt    uint32 //input event count
	outEveCnt   uint32 //output event count
	errEventCnt uint32 // err event count

	parent *Config
}

// MsgChan message channel type
type MsgChan chan logevent.LogEvent

// LoadFromFile load config from filepath
func LoadFromFile(path string) (config Config, err error) {
	data, err := ioutil.ReadFile(path)
	if err != nil {
		return config, ErrorReadConfigFile1.New(err, path)
	}

	ext := strings.ToLower(filepath.Ext(path))
	switch ext {
	case ".yml", ".yaml":
		return LoadFromYAML(data)
	default:
		return LoadFromJSON(data)
	}
}

// LoadFromJSON load config from []byte in JSON format
func LoadFromJSON(data []byte) (config Config, err error) {
	if data, err = cleanComments(data); err != nil {
		return
	}
	if err = json.Unmarshal(data, &config); err != nil {
		return config, ErrorUnmarshalJSONConfig.New(err)
	}
	mysql.SetupConfig(config.Mysql)
	redis.SetupConfig(config.Redis)
	tag.SetupConfig(config.Tag)
	return
}

// LoadFromYAML load config from []byte in YAML format
func LoadFromYAML(data []byte) (config Config, err error) {
	if err = yaml.Unmarshal(data, &config); err != nil {
		return config, ErrorUnmarshalYAMLConfig.New(err)
	}
	mysql.SetupConfig(config.Mysql)
	redis.SetupConfig(config.Redis)
	tag.SetupConfig(config.Tag)
	return
}

/* @note
 * params workerNum int: worker的编号
 */
func initWorkerConfig(config *Config, workerNum int) {
	workerConfig := config.Worker[workerNum]
	rv := reflect.ValueOf(&workerConfig)
	formatReflect(rv)

	if workerConfig.ChannelSize < 1 {
		workerConfig.ChannelSize = DEFAULT_CHANNEL_SIZE
	}
	if workerConfig.InputConcurrency < 1 {
		workerConfig.InputConcurrency = 1
	}
	if workerConfig.FilterConcurrency < 1 {
		workerConfig.FilterConcurrency = 1
	}
	if workerConfig.OutputConcurrency < 1 {
		workerConfig.OutputConcurrency = 1
	}
	if workerConfig.Event != nil {
		logevent.SetConfig(workerConfig.Event)
	}

	workerConfig.chInFilter = make(MsgChan, workerConfig.ChannelSize)
	workerConfig.chFilterOut = make(MsgChan, workerConfig.ChannelSize)
	if workerConfig.DebugChannel {
		workerConfig.chOutDebug = make(MsgChan, workerConfig.ChannelSize)
	}
	workerConfig.parent = config
}

func (t *WorkerConfig) GetParent() *Config {
	return t.parent
}

func (t *WorkerConfig) GetChInFilter() MsgChan {
	return t.chInFilter
}

func (t *WorkerConfig) GetChFilterOut() MsgChan {
	return t.chFilterOut
}

// Start config in goroutines
func (t *WorkerConfig) Start(ctx context.Context) (err error) {
	t.eg, t.ctx = errgroup.WithContext(ctx)

	if err = t.startInputs(); err != nil {
		return
	}
	if err = t.startFilters(); err != nil {
		return
	}
	if err = t.startOutputs(); err != nil {
		return
	}
	if err = t.startStatus(); err != nil {
		return
	}
	return
}

// Wait blocks until all filters returned, then
// returns the first non-nil error (if any) from them.
func (t *WorkerConfig) Wait() (err error) {
	return t.eg.Wait()
}

// TestInputEvent send an event to chInFilter, used for testing
func (t *WorkerConfig) TestInputEvent(event logevent.LogEvent) {
	t.chInFilter <- event
}

// TestGetOutputEvent get an event from chOutDebug, used for testing
func (t *WorkerConfig) TestGetOutputEvent(timeout time.Duration) (event logevent.LogEvent, err error) {
	ctx, cancel := context.WithTimeout(t.ctx, timeout)
	defer cancel()
	select {
	case <-ctx.Done():
		return
	case ev := <-t.chOutDebug:
		return ev, nil
	case <-time.After(timeout + 10*time.Millisecond):
		return event, ErrorTimeout1.New(nil, timeout)
	}
}
